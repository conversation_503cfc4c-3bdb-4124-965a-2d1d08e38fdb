package com.android.internal.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import org.xmlpull.v1.XmlPullParserException;

/* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
/* loaded from: assets/pine_xposed_api.dex */
public final class XmlUtils {
    private XmlUtils() {
    }

    public static final HashMap<String, ?> readMapXml(InputStream inputStream) throws XmlPullParserException, IOException {
        throw new IllegalStateException("Hidden API Stub called: com.android.internal.util.XmlUtils#readMapXml(InputStream)");
    }
}
