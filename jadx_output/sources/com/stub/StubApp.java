package com.stub;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.location.Location;
import android.location.LocationManager;
import android.media.AudioAttributes;
import android.media.MediaPlayer;
import android.media.SoundPool;
import android.os.Build;
import android.util.TypedValue;
import com.tianyu.util.DtcLoader;
import com.tianyu.util.a;
import dalvik.system.DexFile;
import java.io.File;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/* loaded from: classes.dex */
public final class StubApp extends Application {
    private static Context d;
    private static Application a = null;
    public static String strEntryApplication = "entryRunApplication";
    private static Application b = null;
    private static String c = "libjiagu";
    private static boolean loadFromLib = false;
    private static boolean needX86Bridge = false;
    private static boolean returnIntern = true;
    private static String e = null;
    private static String f = null;
    private static String g = null;
    private static String h = null;
    private static String i = null;
    private static Map<Integer, String> j = new ConcurrentHashMap();
    private static Map<String, Set<String>> perm = new ConcurrentHashMap();

    public static native void fcmark();

    public static native void interface11(int i2);

    public static native Enumeration<String> interface12(DexFile dexFile);

    public static native long interface13(int i2, long j2, long j3, long j4, int i3, int i4, long j5);

    public static native String interface14(int i2);

    public static native AssetFileDescriptor interface17(AssetManager assetManager, String str);

    public static native InputStream interface18(Class cls, String str);

    public static native InputStream interface19(ClassLoader classLoader, String str);

    public static native InputStream interface199(AssetManager assetManager, String str);

    public static native void interface20();

    public static native void interface21(Application application);

    public static native void interface22(int i2, String[] strArr, int[] iArr);

    public static native void interface24(Activity activity, String[] strArr, int i2);

    public static native ZipEntry interface30(ZipFile zipFile, String str);

    public static native void interface5(Application application);

    public static native InputStream interface51(Resources resources, int i2);

    public static native InputStream interface52(Resources resources, int i2, TypedValue typedValue);

    public static native AssetFileDescriptor interface53(Resources resources, int i2);

    public static native MediaPlayer interface54(Context context, int i2);

    public static native MediaPlayer interface55(Context context, int i2, AudioAttributes audioAttributes, int i3);

    public static native int interface56(SoundPool soundPool, Context context, int i2, int i3);

    public static native String interface6(String str);

    public static native boolean interface7(Application application, Context context);

    public static native boolean interface8(Application application, Context context);

    public static native Location mark(LocationManager locationManager, String str);

    public static native void mark();

    public static native void mark(Location location);

    public static native synchronized Object n010333(Object obj, Object obj2);

    public static native void n0110();

    public static native boolean n0111();

    public static native void n01110(boolean z);

    public static native boolean n01111(int i2);

    public static native void n011110(boolean z, int i2);

    public static native void n0111111110(int i2, boolean z, boolean z2, boolean z3, boolean z4, boolean z5);

    public static native boolean n0111131(float f2, float f3, Object obj);

    public static native boolean n01111331(float f2, float f3, Object obj, Object obj2);

    public static native void n011113330(int i2, int i3, Object obj, Object obj2, Object obj3);

    public static native long n01112(int i2);

    public static native double n0111222(float f2, double d2, double d3);

    public static native void n0111310(int i2, Object obj, int i3);

    public static native void n0111330(int i2, Object obj, Object obj2);

    public static native long n0112();

    public static native void n01120(long j2);

    public static native boolean n01121(long j2);

    public static native void n011210(long j2, boolean z);

    public static native boolean n011211(long j2, boolean z);

    public static native boolean n0112111(long j2, int i2, int i3);

    public static native boolean n01121111(long j2, float f2, float f3, float f4);

    public static native boolean n011211111(long j2, float f2, float f3, float f4, float f5);

    public static native int n01121111122221(long j2, int i2, int i3, int i4, int i5, int i6, double d2, double d3, double d4, double d5);

    public static native Object n011211113(long j2, int i2, int i3, int i4, int i5);

    public static native Object n01121113(long j2, boolean z, int i2, int i3);

    public static native boolean n0112111331(long j2, int i2, int i3, int i4, Object obj, Object obj2);

    public static native int n01121131(long j2, int i2, int i3, Object obj);

    public static native void n011211310(long j2, int i2, int i3, Object obj, int i4);

    public static native int n011211331(long j2, int i2, int i3, Object obj, Object obj2);

    public static native boolean n0112113331(long j2, int i2, int i3, Object obj, Object obj2, Object obj3);

    public static native boolean n0112121(long j2, int i2, long j3);

    public static native boolean n01121211(long j2, int i2, long j3, int i3);

    public static native Object n011213(long j2, boolean z);

    public static native boolean n01121311(long j2, int i2, Object obj, int i3);

    public static native boolean n011213211(long j2, int i2, Object obj, long j3, int i3);

    public static native long n01122(long j2);

    public static native boolean n011221(long j2, long j3);

    public static native boolean n0112211(long j2, long j3, int i2);

    public static native Object n01122113(double d2, double d3, int i2, int i3);

    public static native boolean n0112221(long j2, long j3, long j4);

    public static native boolean n01122221(long j2, double d2, double d3, double d4);

    public static native double n01122222(double d2, double d3, double d4, double d5);

    public static native boolean n011222221(long j2, double d2, double d3, double d4, double d5);

    public static native Object n011223(double d2, double d3);

    public static native void n0112233330(long j2, long j3, Object obj, Object obj2, Object obj3, Object obj4);

    public static native Object n01123(long j2);

    public static native void n011230(long j2, Object obj);

    public static native boolean n011231(long j2, Object obj);

    public static native void n0112310(long j2, Object obj, float f2);

    public static native boolean n0112311(long j2, Object obj, int i2);

    public static native boolean n01123111(long j2, Object obj, int i2, int i3);

    public static native boolean n011231111(long j2, Object obj, int i2, int i3, boolean z);

    public static native boolean n0112313111(long j2, Object obj, int i2, Object obj2, int i3, int i4);

    public static native long n011232(long j2, Object obj);

    public static native void n0112320(long j2, Object obj, double d2);

    public static native Object n011233(long j2, Object obj);

    public static native void n0112330(long j2, Object obj, Object obj2);

    public static native boolean n0112331(long j2, Object obj, Object obj2);

    public static native boolean n01123311(long j2, Object obj, Object obj2, int i2);

    public static native Object n0112333(long j2, Object obj, Object obj2);

    public static native Object n011233331113(long j2, Object obj, Object obj2, Object obj3, Object obj4, boolean z, boolean z2, boolean z3);

    public static native Object n0112333331113(long j2, Object obj, Object obj2, Object obj3, Object obj4, Object obj5, boolean z, boolean z2, boolean z3);

    public static native Object n0113();

    public static native void n01130(Object obj);

    public static native boolean n01131(Object obj);

    public static native void n011310(Object obj, int i2);

    public static native int n011311(Object obj, int i2);

    public static native Object n011313(Object obj, int i2);

    public static native void n0113130(Object obj, int i2, Object obj2);

    public static native Object n0113133(Object obj, int i2, Object obj2);

    public static native long n01132(Object obj);

    public static native Object n01133(Object obj);

    public static native void n011330(Object obj, Object obj2);

    public static native boolean n011331(Object obj, Object obj2);

    public static native void n0113310(Object obj, Object obj2, boolean z);

    public static native Object n011333(Object obj, Object obj2);

    public static native boolean n0113331(Object obj, Object obj2, Object obj3);

    public static native Object n0113333(Object obj, Object obj2, Object obj3);

    public static native void pmark(Context context);

    public static native void rmark();

    public native synchronized void n11030(Object obj);

    public native synchronized boolean n110331(Object obj, Object obj2);

    public native void n1110();

    public native int n1111();

    public native void n11110(int i2);

    public native int n11111(int i2);

    public native boolean n111111331(int i2, float f2, float f3, Object obj, Object obj2);

    public native void n111130(int i2, Object obj);

    public native boolean n11113311(int i2, Object obj, Object obj2, int i3);

    public native Object n11113333(int i2, Object obj, Object obj2, Object obj3);

    public native long n1112();

    public native void n11120(long j2);

    public native int n11121(long j2);

    public native void n111210(long j2, int i2);

    public native int n111211(long j2, int i2);

    public native void n1112110(long j2, int i2, int i3);

    public native boolean n1112111(long j2, int i2, boolean z);

    public native boolean n11121111(long j2, int i2, boolean z, int i3);

    public native boolean n111211111(long j2, int i2, float f2, float f3, float f4);

    public native Object n1112111113(long j2, int i2, int i3, int i4, int i5, boolean z);

    public native Object n11121113(long j2, int i2, int i3, int i4);

    public native long n111211132(long j2, int i2, int i3, int i4, Object obj);

    public native Object n1112113(long j2, int i2, int i3);

    public native void n11121130(long j2, boolean z, int i2, Object obj);

    public native boolean n11121131(long j2, int i2, int i3, Object obj);

    public native long n11121132(long j2, int i2, int i3, Object obj);

    public native Object n111213(long j2, int i2);

    public native void n1112130(long j2, boolean z, Object obj);

    public native boolean n1112131(long j2, int i2, Object obj);

    public native void n11121330(long j2, int i2, Object obj, Object obj2);

    public native long n11122(long j2);

    public native void n111220(long j2, long j3);

    public native int n111221(long j2, long j3);

    public native void n1112210(long j2, long j3, int i2);

    public native boolean n1112211(long j2, long j3, int i2);

    public native void n1112211110(double d2, double d3, float f2, float f3, float f4, int i2);

    public native Object n111221113(long j2, long j3, int i2, int i3, int i4);

    public native boolean n1112221(long j2, double d2, double d3);

    public native boolean n111222111(long j2, long j3, long j4, int i2, int i3);

    public native Object n11122213(long j2, double d2, double d3, int i2);

    public native Object n111222133(long j2, long j3, long j4, boolean z, Object obj);

    public native boolean n11122221(long j2, double d2, double d3, double d4);

    public native void n1112230(long j2, long j3, Object obj);

    public native Object n11123(long j2);

    public native void n111230(long j2, Object obj);

    public native boolean n111231(long j2, Object obj);

    public native void n1112310(long j2, Object obj, boolean z);

    public native boolean n1112311(long j2, Object obj, boolean z);

    public native float n11123111(long j2, Object obj, int i2, int i3);

    public native Object n111233(long j2, Object obj);

    public native void n1112330(long j2, Object obj, Object obj2);

    public native float n1112331(long j2, Object obj, Object obj2);

    public native boolean n11123311(long j2, Object obj, Object obj2, boolean z);

    public native boolean n111233221(long j2, Object obj, Object obj2, long j3, long j4);

    public native boolean n11123331111(long j2, Object obj, Object obj2, Object obj3, int i2, int i3, int i4);

    public native boolean n111233333331111111111(long j2, Object obj, Object obj2, Object obj3, Object obj4, Object obj5, Object obj6, Object obj7, int i2, int i3, int i4, int i5, int i6, int i7, int i8, boolean z, boolean z2);

    public native Object n1113();

    public native void n11130(Object obj);

    public native boolean n11131(Object obj);

    public native void n111310(Object obj, int i2);

    public native void n1113130(Object obj, boolean z, Object obj2);

    public native Object n11133(Object obj);

    public native void n111330(Object obj, Object obj2);

    public native void n1113310(Object obj, Object obj2, boolean z);

    public native void n11133110(Object obj, Object obj2, boolean z, int i2);

    public native Object n111333(Object obj, Object obj2);

    public native void n11133310(Object obj, Object obj2, Object obj3, int i2);

    public native Object n1113333(Object obj, Object obj2, Object obj3);

    public static String getSoPath1() {
        return f;
    }

    public static String getSoPath2() {
        return g;
    }

    public static String getDir() {
        return h;
    }

    public static Context getAppContext() {
        return d;
    }

    public static Context getOrigApplicationContext(Context context) {
        if (context == a) {
            return b;
        }
        return context;
    }

    private static Application a(Context context) {
        ClassLoader classLoader;
        Class<?> clsLoadClass;
        try {
            if (b == null && (classLoader = context.getClassLoader()) != null && (clsLoadClass = classLoader.loadClass(strEntryApplication)) != null) {
                b = (Application) clsLoadClass.newInstance();
            }
        } catch (Exception e2) {
        }
        return b;
    }

    public static void ChangeTopApplication() {
        try {
            interface7(b, a.getBaseContext());
        } catch (Exception e2) {
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v9, types: [java.lang.reflect.Method] */
    /* JADX WARN: Type inference failed for: r2v3, types: [java.lang.Object[]] */
    /* JADX WARN: Type inference failed for: r5v1 */
    /* JADX WARN: Type inference failed for: r5v3 */
    /* JADX WARN: Type inference failed for: r5v4 */
    @Override // android.app.Application
    public final void onCreate() throws IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        System.currentTimeMillis();
        super.onCreate();
        ChangeTopApplication();
        if (b != null) {
            b.onCreate();
        }
        interface21(b);
        ?? r5 = this;
        if (b != null) {
            r5 = b;
        }
        Context context = d;
        if (r5 != 0 && context != null && a.a(context)) {
            try {
                ?? declaredMethod = Class.forName(a.a("s\u007f}>zw>rx>Bu`\u007fbdcDy}u")).getDeclaredMethod(a.a("BuwycdubQsdyfydiSq||Rqs{c"), Application.class);
                declaredMethod.setAccessible(true);
                declaredMethod.invoke(null, new Object[]{r5});
            } catch (Exception e2) {
            }
        }
    }

    @Override // android.content.ContextWrapper
    protected final void attachBaseContext(Context context) throws Throwable {
        boolean zA;
        System.currentTimeMillis();
        super.attachBaseContext(context);
        a.b();
        d = context;
        if (a == null) {
            a = this;
        }
        if (b == null) {
            Boolean boolValueOf = Boolean.valueOf(a.a());
            Boolean bool = false;
            Boolean bool2 = false;
            if (Build.CPU_ABI.contains("64") || Build.CPU_ABI2.contains("64")) {
                bool = true;
            }
            if (Build.CPU_ABI.contains("mips") || Build.CPU_ABI2.contains("mips")) {
                bool2 = true;
            }
            if (boolValueOf.booleanValue() && needX86Bridge) {
                System.loadLibrary("X86Bridge");
            }
            if (loadFromLib) {
                if (boolValueOf.booleanValue() && !needX86Bridge) {
                    System.loadLibrary("jiagu_x86");
                } else {
                    System.loadLibrary("jiagu");
                }
            } else {
                String absolutePath = context.getFilesDir().getParentFile().getAbsolutePath();
                try {
                    absolutePath = context.getFilesDir().getParentFile().getCanonicalPath();
                } catch (Exception e2) {
                }
                String str = absolutePath + "/.jiagu";
                i = a(str, bool.booleanValue(), bool2.booleanValue());
                e = a(str, false, false);
                f = str + File.separator + e;
                g = str + File.separator + i;
                h = str;
                if (bool2.booleanValue()) {
                    a.a(context, c + "_mips.so", str, e);
                } else if (boolValueOf.booleanValue() && !needX86Bridge) {
                    a.a(context, c + "_x86.so", str, e);
                } else {
                    a.a(context, c + ".so", str, e);
                }
                if (bool.booleanValue() && !bool2.booleanValue()) {
                    if (boolValueOf.booleanValue() && !needX86Bridge) {
                        zA = a.a(context, c + "_x64.so", str, i);
                    } else {
                        zA = a.a(context, c + "_a64.so", str, i);
                    }
                    if (zA) {
                        System.load(str + "/" + i);
                    } else {
                        System.load(str + "/" + e);
                    }
                } else {
                    System.load(str + "/" + e);
                }
            }
        }
        DtcLoader.init();
        interface5(a);
        if (b == null) {
            b = a(context);
            if (b != null) {
                try {
                    Method declaredMethod = Application.class.getDeclaredMethod("attach", Context.class);
                    if (declaredMethod != null) {
                        declaredMethod.setAccessible(true);
                        declaredMethod.invoke(b, context);
                    }
                    interface8(b, context);
                    return;
                } catch (Exception e3) {
                    throw new RuntimeException("Failed to call attachBaseContext.", e3);
                }
            }
            System.exit(1);
        }
    }

    private static String a(String str, boolean z, boolean z2) {
        String str2 = c;
        if (Build.VERSION.SDK_INT < 23) {
            str2 = str2 + str.hashCode();
        }
        if (z && !z2) {
            return str2 + "_64.so";
        }
        return str2 + ".so";
    }

    public static String getString2(int i2) {
        String strInterface14 = j.get(Integer.valueOf(i2));
        if (strInterface14 == null) {
            strInterface14 = interface14(i2);
            j.put(Integer.valueOf(i2), strInterface14);
        }
        if (strInterface14 != null && returnIntern) {
            return strInterface14.intern();
        }
        return strInterface14;
    }

    public static String getString2(String str) {
        try {
            return getString2(Integer.parseInt(str));
        } catch (NumberFormatException e2) {
            e2.printStackTrace();
            return null;
        }
    }

    public static boolean isX86Arch() {
        return a.a();
    }

    public static void put(Object obj, String[] strArr) {
        try {
            String canonicalName = obj.getClass().getCanonicalName();
            Set<String> set = perm.get(canonicalName);
            if (set != null) {
                set.addAll(Arrays.asList(strArr));
            } else {
                Set<String> setNewSetFromMap = Collections.newSetFromMap(new ConcurrentHashMap());
                Collections.addAll(setNewSetFromMap, strArr);
                Set<String> setPut = perm.put(canonicalName, setNewSetFromMap);
                if (setPut != null) {
                    setPut.addAll(Arrays.asList(strArr));
                }
            }
        } catch (Exception e2) {
        }
    }
}
