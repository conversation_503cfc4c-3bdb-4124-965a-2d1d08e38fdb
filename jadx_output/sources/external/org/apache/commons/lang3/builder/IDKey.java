package external.org.apache.commons.lang3.builder;

/* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
/* loaded from: assets/pine_xposed_api.dex */
final class IDKey {
    private final int id;
    private final Object value;

    public int hashCode() {
        return this.id;
    }

    public IDKey(Object obj) {
        this.id = System.identityHashCode(obj);
        this.value = obj;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof IDKey)) {
            return false;
        }
        IDKey iDKey = (IDKey) obj;
        return this.id == iDKey.id && this.value == iDKey.value;
    }
}
