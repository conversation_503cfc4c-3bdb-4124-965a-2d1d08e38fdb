package de.robv.android.xposed;

/* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
/* loaded from: assets/pine_xposed_api.dex */
public interface IXposedHookZygoteInit extends IXposedMod {

    /* JADX WARN: Classes with same name are omitted:
      assets/aliu_xposed_api.dex
     */
    public static final class StartupParam {
        public String modulePath;
        public boolean startsSystemServer;
    }

    void initZygote(StartupParam startupParam) throws Throwable;
}
