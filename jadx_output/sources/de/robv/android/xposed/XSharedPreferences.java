package de.robv.android.xposed;

import android.content.SharedPreferences;
import android.os.Environment;
import android.os.StrictMode;
import de.robv.android.xposed.XSharedPreferences;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
@Deprecated
/* loaded from: assets/pine_xposed_api.dex */
public final class XSharedPreferences implements SharedPreferences {
    private static final String TAG = "XSharedPreferences";
    private static Loader sLoader = Loader.SYNC;
    private final File mFile;
    private long mFileSize;
    private long mLastModified;
    private boolean mLoaded;
    private Map<String, Object> mMap;

    public static void setLoader(Loader loader) {
        sLoader = loader;
    }

    public File getFile() {
        return this.mFile;
    }

    /* JADX WARN: Classes with same name are omitted:
      assets/aliu_xposed_api.dex
     */
    public interface Loader {
        public static final Loader SYNC = new Loader() { // from class: de.robv.android.xposed.XSharedPreferences$Loader$$ExternalSyntheticLambda0
            @Override // de.robv.android.xposed.XSharedPreferences.Loader
            public final void run(XSharedPreferences xSharedPreferences, Runnable runnable) {
                XSharedPreferences.Loader.CC.lambda$static$0(xSharedPreferences, runnable);
            }
        };
        public static final Loader ASYNC = new Loader() { // from class: de.robv.android.xposed.XSharedPreferences$Loader$$ExternalSyntheticLambda1
            @Override // de.robv.android.xposed.XSharedPreferences.Loader
            public final void run(XSharedPreferences xSharedPreferences, Runnable runnable) {
                new Thread("XSharedPreferences-load") { // from class: de.robv.android.xposed.XSharedPreferences.Loader.1
                    @Override // java.lang.Thread, java.lang.Runnable
                    public void run() {
                        synchronized (xSharedPreferences) {
                            runnable.run();
                        }
                    }
                }.start();
            }
        };

        void run(XSharedPreferences xSharedPreferences, Runnable runnable);

        /* JADX WARN: Classes with same name are omitted:
          assets/aliu_xposed_api.dex
         */
        /* renamed from: de.robv.android.xposed.XSharedPreferences$Loader$-CC, reason: invalid class name */
        public final /* synthetic */ class CC {
            static {
                Loader loader = Loader.SYNC;
            }

            public static /* synthetic */ void lambda$static$0(XSharedPreferences xSharedPreferences, Runnable runnable) {
                StrictMode.ThreadPolicy threadPolicyAllowThreadDiskReads = StrictMode.allowThreadDiskReads();
                try {
                    runnable.run();
                } finally {
                    StrictMode.setThreadPolicy(threadPolicyAllowThreadDiskReads);
                }
            }
        }
    }

    public XSharedPreferences(File file) {
        this.mLoaded = false;
        this.mFile = file;
        startLoadFromDisk();
    }

    public XSharedPreferences(String str) {
        this(str, str + "_preferences");
    }

    public XSharedPreferences(String str, String str2) {
        this.mLoaded = false;
        this.mFile = new File(Environment.getDataDirectory(), "data/" + str + "/shared_prefs/" + str2 + ".xml");
        startLoadFromDisk();
    }

    public boolean makeWorldReadable() {
        if (this.mFile.exists()) {
            return this.mFile.setReadable(true, false);
        }
        return false;
    }

    private void startLoadFromDisk() {
        synchronized (this) {
            this.mLoaded = false;
        }
        sLoader.run(this, new Runnable() { // from class: de.robv.android.xposed.XSharedPreferences$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() throws Throwable {
                this.f$0.loadFromDiskLocked();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Not initialized variable reg: 3, insn: 0x004a: MOVE (r2 I:??[OBJECT, ARRAY]) = (r3 I:??[OBJECT, ARRAY]), block:B:32:0x004a */
    /* JADX WARN: Removed duplicated region for block: B:43:0x005f  */
    /* JADX WARN: Removed duplicated region for block: B:44:0x006c  */
    /* JADX WARN: Removed duplicated region for block: B:47:0x0079  */
    /* JADX WARN: Removed duplicated region for block: B:50:0x0055 A[EXC_TOP_SPLITTER, PHI: r3
      0x0055: PHI (r3v4 java.io.BufferedInputStream) = (r3v3 java.io.BufferedInputStream), (r3v5 java.io.BufferedInputStream) binds: [B:29:0x0046, B:37:0x0053] A[DONT_GENERATE, DONT_INLINE], SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:55:0x004d A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:59:? A[RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void loadFromDiskLocked() throws java.lang.Throwable {
        /*
            r8 = this;
            boolean r0 = r8.mLoaded
            if (r0 == 0) goto L5
            return
        L5:
            java.io.File r0 = r8.mFile
            long r0 = r0.lastModified()
            r2 = 0
            long r3 = r8.mLastModified     // Catch: java.lang.Throwable -> L39 java.io.IOException -> L3b org.xmlpull.v1.XmlPullParserException -> L3d java.io.FileNotFoundException -> L51
            int r5 = (r0 > r3 ? 1 : (r0 == r3 ? 0 : -1))
            if (r5 == 0) goto L2e
            java.io.BufferedInputStream r3 = new java.io.BufferedInputStream     // Catch: java.lang.Throwable -> L39 java.io.IOException -> L3b org.xmlpull.v1.XmlPullParserException -> L3d java.io.FileNotFoundException -> L51
            java.io.FileInputStream r4 = new java.io.FileInputStream     // Catch: java.lang.Throwable -> L39 java.io.IOException -> L3b org.xmlpull.v1.XmlPullParserException -> L3d java.io.FileNotFoundException -> L51
            java.io.File r5 = r8.mFile     // Catch: java.lang.Throwable -> L39 java.io.IOException -> L3b org.xmlpull.v1.XmlPullParserException -> L3d java.io.FileNotFoundException -> L51
            r4.<init>(r5)     // Catch: java.lang.Throwable -> L39 java.io.IOException -> L3b org.xmlpull.v1.XmlPullParserException -> L3d java.io.FileNotFoundException -> L51
            r5 = 4096(0x1000, float:5.74E-42)
            r3.<init>(r4, r5)     // Catch: java.lang.Throwable -> L39 java.io.IOException -> L3b org.xmlpull.v1.XmlPullParserException -> L3d java.io.FileNotFoundException -> L51
            java.util.HashMap r2 = com.android.internal.util.XmlUtils.readMapXml(r3)     // Catch: java.io.IOException -> L28 org.xmlpull.v1.XmlPullParserException -> L2a java.io.FileNotFoundException -> L2c java.lang.Throwable -> L49
            r7 = r3
            r3 = r2
            r2 = r7
            goto L30
        L28:
            r4 = move-exception
            goto L3f
        L2a:
            r4 = move-exception
            goto L3f
        L2c:
            goto L53
        L2e:
            java.util.Map<java.lang.String, java.lang.Object> r3 = r8.mMap     // Catch: java.lang.Throwable -> L39 java.io.IOException -> L3b org.xmlpull.v1.XmlPullParserException -> L3d java.io.FileNotFoundException -> L51
        L30:
            if (r2 == 0) goto L37
            r2.close()     // Catch: java.io.IOException -> L36
            goto L37
        L36:
        L37:
            r2 = r3
            goto L5a
        L39:
            r0 = move-exception
            goto L4b
        L3b:
            r4 = move-exception
            goto L3e
        L3d:
            r4 = move-exception
        L3e:
            r3 = r2
        L3f:
            java.lang.String r5 = "XSharedPreferences"
            java.lang.String r6 = "getSharedPreferences"
            android.util.Log.w(r5, r6, r4)     // Catch: java.lang.Throwable -> L49
            if (r3 == 0) goto L5a
            goto L55
        L49:
            r0 = move-exception
            r2 = r3
        L4b:
            if (r2 == 0) goto L50
            r2.close()     // Catch: java.io.IOException -> L50
        L50:
            throw r0
        L51:
            r3 = r2
        L53:
            if (r3 == 0) goto L5a
        L55:
            r3.close()     // Catch: java.io.IOException -> L59
            goto L5a
        L59:
        L5a:
            r3 = 1
            r8.mLoaded = r3
            if (r2 == 0) goto L6c
            r8.mMap = r2
            r8.mLastModified = r0
            java.io.File r0 = r8.mFile
            long r0 = r0.length()
            r8.mFileSize = r0
            goto L73
        L6c:
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            r8.mMap = r0
        L73:
            boolean r0 = java.lang.Thread.holdsLock(r8)
            if (r0 == 0) goto L7c
            r8.notifyAll()
        L7c:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: de.robv.android.xposed.XSharedPreferences.loadFromDiskLocked():void");
    }

    public synchronized void reload() {
        if (hasFileChanged()) {
            startLoadFromDisk();
        }
    }

    public synchronized boolean hasFileChanged() {
        StrictMode.ThreadPolicy threadPolicyAllowThreadDiskReads = StrictMode.allowThreadDiskReads();
        try {
            boolean z = true;
            if (!this.mFile.exists()) {
                return true;
            }
            if (this.mFile.lastModified() == this.mLastModified) {
                if (this.mFile.length() == this.mFileSize) {
                    z = false;
                }
            }
            return z;
        } finally {
            StrictMode.setThreadPolicy(threadPolicyAllowThreadDiskReads);
        }
    }

    private void awaitLoadedLocked() throws InterruptedException {
        while (!this.mLoaded) {
            try {
                wait();
            } catch (InterruptedException unused) {
            }
        }
    }

    @Override // android.content.SharedPreferences
    public Map<String, ?> getAll() {
        HashMap map;
        synchronized (this) {
            awaitLoadedLocked();
            map = new HashMap(this.mMap);
        }
        return map;
    }

    @Override // android.content.SharedPreferences
    public String getString(String str, String str2) {
        synchronized (this) {
            awaitLoadedLocked();
            String str3 = (String) this.mMap.get(str);
            if (str3 != null) {
                str2 = str3;
            }
        }
        return str2;
    }

    @Override // android.content.SharedPreferences
    public Set<String> getStringSet(String str, Set<String> set) {
        synchronized (this) {
            awaitLoadedLocked();
            Set<String> set2 = (Set) this.mMap.get(str);
            if (set2 != null) {
                set = set2;
            }
        }
        return set;
    }

    @Override // android.content.SharedPreferences
    public int getInt(String str, int i) {
        synchronized (this) {
            awaitLoadedLocked();
            Integer num = (Integer) this.mMap.get(str);
            if (num != null) {
                i = num.intValue();
            }
        }
        return i;
    }

    @Override // android.content.SharedPreferences
    public long getLong(String str, long j) {
        synchronized (this) {
            awaitLoadedLocked();
            Long l = (Long) this.mMap.get(str);
            if (l != null) {
                j = l.longValue();
            }
        }
        return j;
    }

    @Override // android.content.SharedPreferences
    public float getFloat(String str, float f) {
        synchronized (this) {
            awaitLoadedLocked();
            Float f2 = (Float) this.mMap.get(str);
            if (f2 != null) {
                f = f2.floatValue();
            }
        }
        return f;
    }

    @Override // android.content.SharedPreferences
    public boolean getBoolean(String str, boolean z) {
        synchronized (this) {
            awaitLoadedLocked();
            Boolean bool = (Boolean) this.mMap.get(str);
            if (bool != null) {
                z = bool.booleanValue();
            }
        }
        return z;
    }

    @Override // android.content.SharedPreferences
    public boolean contains(String str) {
        boolean zContainsKey;
        synchronized (this) {
            awaitLoadedLocked();
            zContainsKey = this.mMap.containsKey(str);
        }
        return zContainsKey;
    }

    @Override // android.content.SharedPreferences
    @Deprecated
    public SharedPreferences.Editor edit() {
        throw new UnsupportedOperationException("read-only implementation");
    }

    @Override // android.content.SharedPreferences
    @Deprecated
    public void registerOnSharedPreferenceChangeListener(SharedPreferences.OnSharedPreferenceChangeListener onSharedPreferenceChangeListener) {
        throw new UnsupportedOperationException("listeners are not supported in this implementation");
    }

    @Override // android.content.SharedPreferences
    @Deprecated
    public void unregisterOnSharedPreferenceChangeListener(SharedPreferences.OnSharedPreferenceChangeListener onSharedPreferenceChangeListener) {
        throw new UnsupportedOperationException("listeners are not supported in this implementation");
    }
}
