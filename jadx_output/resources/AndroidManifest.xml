<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.alibaba.android.rimet.beesforce"
    platformBuildVersionCode="32"
    platformBuildVersionName="12"
    android:versionCode="1000"
    android:versionName="1.0"
    android:compileSdkVersion="32"
    android:compileSdkVersionCodename="12">
    <uses-sdk
        android:minSdkVersion="27"
        android:targetSdkVersion="32"/>
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION"/>
    <uses-permission android:name="android.permission.REPLACE_EXISTING_PACKAGE"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES"/>
    <application
        android:theme="@style/AppTheme"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:name="com.stub.StubApp"
        android:launchMode="singleInstance"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:appComponentFactory="qchyyds.OoOoOOOoOO"
        android:requestLegacyExternalStorage="true">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false"/>
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="KpbYCL3vtdPqIqFXX1ffHBZwt51FcSID"/>
        <service
            android:name="com.baidu.location.f"
            android:enabled="true"
            android:exported="false"
            android:process=":remote"/>
        <service
            android:name="com.zcshou.service.ServiceGo"
            android:exported="false"/>
        <provider
            android:name="qchyyds.oooOoO"
            android:exported="false"
            android:authorities="com.alibaba.android.rimet.beesforce.fileProvider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
        <activity
            android:theme="@style/AppTheme"
            android:name="com.zcshou.gogogo.WelcomeActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:theme="@style/AppTheme.NoActionBar"
            android:name="square.boxfk.MainActivity"
            android:exported="false"/>
        <activity
            android:label="@string/app_settings"
            android:name="square.boxfk.SettingsActivity"
            android:exported="false"/>
        <activity
            android:label="@string/app_history"
            android:name="square.boxfk.HistoryActivity"
            android:exported="false"/>
        <activity
            android:name="com.zcshou.gogogo.HomeActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:scheme="um.6125a15210c4020b03ec017a"/>
                <data
                    android:scheme="sa5d580a1c"
                    android:host="debugmode"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.zcshou.gogogo.MapActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:configChanges="screenSize|orientation|keyboardHidden"/>
        <activity
            android:name="com.zcshou.gogogo.SelectaddressActivity"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.zcshou.gogogo.LoginActivity"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <uses-library
            android:name="androidx.window.extensions"
            android:required="false"/>
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false"/>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:exported="false"
            android:authorities="com.alibaba.android.rimet.beesforce.androidx-startup">
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup"/>
        </provider>
        <provider
            android:name="qchyyds.oooOoO"
            android:exported="false"
            android:authorities="com.alibaba.android.rimet.beesforce.kdkg.dfmfgxep"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/epic_paths"/>
        </provider>
        <service
            android:name="qchyyds.oooooOooO"
            android:exported="false"/>
        <activity
            android:name="Epic.plugin.core.A$1"
            android:exported="false"
            android:launchMode="standard"/>
        <activity
            android:theme="@android:style/Theme.Translucent"
            android:name="Epic.plugin.core.A$2"
            android:exported="false"
            android:launchMode="standard"/>
        <activity
            android:name="Epic.plugin.core.B$1"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.B$2"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.B$3"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.B$4"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.B$5"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.B$6"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.B$7"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.B$8"
            android:exported="false"
            android:launchMode="singleTop"/>
        <activity
            android:name="Epic.plugin.core.C$1"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.C$2"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.C$3"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.C$4"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.C$5"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.C$6"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.C$7"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.C$8"
            android:exported="false"
            android:launchMode="singleTask"/>
        <activity
            android:name="Epic.plugin.core.D$1"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <activity
            android:name="Epic.plugin.core.D$2"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <activity
            android:name="Epic.plugin.core.D$3"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <activity
            android:name="Epic.plugin.core.D$4"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <activity
            android:name="Epic.plugin.core.D$5"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <activity
            android:name="Epic.plugin.core.D$6"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <activity
            android:name="Epic.plugin.core.D$7"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <activity
            android:name="Epic.plugin.core.D$8"
            android:exported="false"
            android:launchMode="singleInstance"/>
        <service
            android:name="qchyyds.oOoOOooOo"
            android:exported="false"/>
        <service
            android:name="qchyyds.OoOoO"
            android:exported="false"
            android:process=":daemon">
            <intent-filter>
                <action android:name="com.alibaba.android.rimet.beesforce.intent.ACTION_DAEMON_SERVICE"/>
            </intent-filter>
        </service>
        <provider
            android:name="qchyyds.oOoooooO"
            android:exported="false"
            android:process=":daemon"
            android:authorities="com.alibaba.android.rimet.beesforce.VirtualAPK.Provider"
            android:grantUriPermissions="true"/>
        <provider
            android:name="qchyyds.ooOOOooo"
            android:exported="false"
            android:process=":daemon"
            android:authorities="com.alibaba.android.rimet.beesforce.VirtualAPK.FileProvider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/epic_paths"/>
        </provider>
    </application>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <permission
        android:name="com.alibaba.android.rimet.beesforce.openadsdk.permission.TT_PANGOLIN"
        android:protectionLevel="signature"/>
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="com.alibaba.android.rimet.beesforce.openadsdk.permission.TT_PANGOLIN"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_SMS"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW"/>
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="com.asus.msa.SupplementaryDID.ACCESS"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS"/>
    <uses-permission android:name="android.permission.INSTALL_PACKAGES"/>
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <category android:name="android.intent.category.BROWSABLE"/>
            <data android:scheme="https"/>
        </intent>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService"/>
        </intent>
    </queries>
</manifest>
