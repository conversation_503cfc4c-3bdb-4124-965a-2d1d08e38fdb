<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.baidu.mapapi.map.MapView
            android:id="@+id/bmapView"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="start"
        android:orientation="horizontal"
        android:id="@+id/llContent"
        android:background="@drawable/white_tran"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginTop="10dp"
        android:layout_marginHorizontal="10dp">
        <LinearLayout
            android:orientation="horizontal"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.zcshou.widgets.MaterialRippleLayout
                android:layout_width="50dp"
                android:layout_height="50dp">
                <ImageView
                    android:id="@+id/backioc"
                    android:padding="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/back"/>
            </com.zcshou.widgets.MaterialRippleLayout>
            <RelativeLayout
                android:id="@+id/selelayout"
                android:clickable="true"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">
                <LinearLayout
                    android:gravity="center"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_search"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/white"
                        android:textColorHint="#919fac"
                        android:gravity="center_vertical"
                        android:id="@+id/et_address"
                        android:paddingLeft="10dp"
                        android:paddingTop="7dp"
                        android:paddingRight="10dp"
                        android:paddingBottom="7dp"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:hint="搜索地点"
                        android:layout_weight="1"/>
                    <TextView
                        android:textColor="#919fac"
                        android:id="@+id/clear_text"
                        android:padding="5dp"
                        android:visibility="gone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="10dp"
                        android:text=" ╳"/>
                </LinearLayout>
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
    <RelativeLayout
        android:background="@drawable/sel_guide_btn6"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/dingweiimg"
                android:background="#fff"
                android:padding="10dp"
                android:visibility="gone"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginLeft="15dp"/>
            <LinearLayout
                android:gravity="center"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="13dp"
                        android:layout_marginTop="10dp">
                        <ImageView
                            android:id="@+id/baohuimg"
                            android:layout_width="20dp"
                            android:layout_height="20dp"/>
                        <TextView
                            android:id="@+id/baohutv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="未保护"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp">
                        <ImageView
                            android:id="@+id/bianjiimg"
                            android:background="#fff"
                            android:padding="13dp"
                            android:visibility="gone"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_alignParentBottom="true"/>
                        <LinearLayout
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">
                            <TextView
                                android:textSize="13sp"
                                android:textColor="#fff"
                                android:id="@+id/show_address2"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="当前位置："
                                android:drawableLeft="@drawable/ic_position"
                                android:drawablePadding="5dp"
                                android:layout_marginVertical="15dp"/>
                            <TextView
                                android:textSize="14sp"
                                android:id="@+id/show_jingweidu2"
                                android:visibility="gone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"/>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
                <TextView
                    android:textSize="14sp"
                    android:textColor="@color/black"
                    android:gravity="center"
                    android:id="@+id/tiaozhuan"
                    android:background="@drawable/sel_guide_btn7"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:text="  保存位置  "
                    android:layout_marginHorizontal="30dp"
                    android:layout_marginVertical="10dp"/>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
