<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_vertical"
    android:paddingRight="?android:attr/scrollbarSize"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?android:attr/listPreferredItemHeight"
    android:paddingEnd="?android:attr/scrollbarSize">
    <ImageView
        android:layout_gravity="center"
        android:id="@android:id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="@dimen/preference_icon_minWidth"/>
    <RelativeLayout
        android:clipChildren="false"
        android:clipToPadding="false"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="6dp"
        android:layout_marginRight="8dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp">
        <TextView
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:ellipsize="marquee"
            android:id="@android:id/title"
            android:fadingEdge="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"/>
        <TextView
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="?android:attr/textColorSecondary"
            android:id="@android:id/summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="4"
            android:layout_below="@android:id/title"
            android:layout_alignLeft="@android:id/title"
            android:layout_alignStart="@android:id/title"/>
        <androidx.preference.UnPressableLinearLayout
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@android:id/summary"
            android:layout_alignLeft="@android:id/title"
            android:layout_alignStart="@android:id/title">
            <SeekBar
                android:id="@+id/seekbar"
                android:background="@null"
                android:paddingLeft="@dimen/preference_seekbar_padding_horizontal"
                android:paddingRight="22dp"
                android:focusable="false"
                android:clickable="false"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingStart="@dimen/preference_seekbar_padding_horizontal"
                android:paddingEnd="22dp"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/seekbar_value"
                android:fadingEdge="horizontal"
                android:layout_width="@dimen/preference_seekbar_value_minWidth"
                android:layout_height="match_parent"
                android:singleLine="true"
                android:fontFamily="sans-serif-condensed"/>
        </androidx.preference.UnPressableLinearLayout>
    </RelativeLayout>
</LinearLayout>
