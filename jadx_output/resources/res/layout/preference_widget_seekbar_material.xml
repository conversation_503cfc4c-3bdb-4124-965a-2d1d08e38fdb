<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground"
    android:paddingLeft="?android:attr/listPreferredItemPaddingLeft"
    android:paddingRight="?android:attr/listPreferredItemPaddingRight"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:baselineAligned="false"
    android:minHeight="?android:attr/listPreferredItemHeightSmall"
    android:paddingStart="?android:attr/listPreferredItemPaddingStart"
    android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">
    <include layout="@layout/image_frame"/>
    <LinearLayout
        android:orientation="vertical"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp">
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1">
            <TextView
                android:textAppearance="?android:attr/textAppearanceListItem"
                android:ellipsize="marquee"
                android:id="@android:id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:labelFor="@+id/seekbar"/>
            <TextView
                android:textColor="?android:attr/textColorSecondary"
                android:layout_gravity="start"
                android:id="@android:id/summary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="4"
                android:layout_below="@android:id/title"
                android:layout_alignLeft="@android:id/title"
                android:textAlignment="viewStart"
                android:layout_alignStart="@android:id/title"
                style="@style/PreferenceSummaryTextStyle"/>
        </RelativeLayout>
        <androidx.preference.UnPressableLinearLayout
            android:gravity="center_vertical"
            android:paddingLeft="0dp"
            android:paddingRight="16dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="0dp"
            android:paddingEnd="16dp">
            <SeekBar
                android:id="@+id/seekbar"
                android:background="@null"
                android:paddingLeft="@dimen/preference_seekbar_padding_horizontal"
                android:paddingTop="@dimen/preference_seekbar_padding_vertical"
                android:paddingRight="@dimen/preference_seekbar_padding_horizontal"
                android:paddingBottom="@dimen/preference_seekbar_padding_vertical"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingStart="@dimen/preference_seekbar_padding_horizontal"
                android:paddingEnd="@dimen/preference_seekbar_padding_horizontal"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceListItem"
                android:ellipsize="marquee"
                android:gravity="right"
                android:id="@+id/seekbar_value"
                android:paddingLeft="8dp"
                android:paddingRight="0dp"
                android:scrollbars="none"
                android:fadingEdge="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/preference_seekbar_value_minWidth"
                android:singleLine="true"
                android:paddingStart="8dp"
                android:paddingEnd="0dp"/>
        </androidx.preference.UnPressableLinearLayout>
    </LinearLayout>
</LinearLayout>
