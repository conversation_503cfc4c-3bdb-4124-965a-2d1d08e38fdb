<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.baidu.mapapi.map.MapView
            android:id="@+id/bdMapView"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <RadioGroup
            android:orientation="vertical"
            android:id="@+id/RadioGroupMapType"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_alignParentTop="true"
            android:layout_marginEnd="5dp"
            android:layout_alignParentEnd="true">
            <RadioButton
                android:textColor="#00796b"
                android:id="@+id/mapNormal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="true"
                android:minHeight="48dp"
                android:text="@string/map_pic_normal"
                android:layout_weight="1"/>
            <RadioButton
                android:textColor="#00796b"
                android:id="@+id/mapSatellite"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="48dp"
                android:text="@string/map_pic_sate"
                android:layout_weight="1"/>
        </RadioGroup>
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/cur_position"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="350dp"
            android:contentDescription="@string/cur_position"
            android:layout_marginEnd="10dp"
            android:layout_alignParentEnd="true"
            app:srcCompat="@drawable/ic_home_position"
            app:tint="@color/colorAccent"/>
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/zoom_in"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="383dp"
            android:contentDescription="@string/zoom_in"
            android:layout_marginEnd="10dp"
            android:layout_alignParentEnd="true"
            app:srcCompat="@drawable/ic_zoom_in"
            app:tint="@color/colorAccent"/>
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/zoom_out"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="416dp"
            android:contentDescription="@string/zoom_out"
            android:layout_marginEnd="10dp"
            android:layout_alignParentEnd="true"
            app:srcCompat="@drawable/ic_zoom_out"
            app:tint="@color/colorAccent"/>
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/input_pos"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="240dp"
            android:contentDescription="@string/input_button"
            android:layout_marginEnd="10dp"
            android:layout_alignParentEnd="true"
            app:srcCompat="@drawable/ic_input"
            app:tint="@color/colorAccent"/>
        <TextView
            android:textSize="10sp"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_statement"
            android:layout_alignParentBottom="true"/>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
