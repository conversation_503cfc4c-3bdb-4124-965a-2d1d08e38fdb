<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="58dp"
    style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown">
    <ImageView
        android:id="@android:id/icon1"
        android:visibility="invisible"
        android:layout_width="@dimen/abc_dropdownitem_icon_width"
        android:layout_height="48dp"
        android:scaleType="centerInside"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1"/>
    <ImageView
        android:id="@+id/edit_query"
        android:background="?attr/selectableItemBackground"
        android:visibility="gone"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerInside"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query"/>
    <ImageView
        android:id="@android:id/icon2"
        android:visibility="gone"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerInside"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:layout_alignWithParentIfMissing="true"
        style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2"/>
    <TextView
        android:textAppearance="?attr/textAppearanceSearchResultSubtitle"
        android:gravity="top"
        android:id="@android:id/text2"
        android:paddingBottom="4dp"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="29dp"
        android:singleLine="true"
        android:layout_alignParentBottom="true"
        android:layout_alignWithParentIfMissing="true"
        style="?android:attr/dropDownItemStyle"/>
    <TextView
        android:textAppearance="?attr/textAppearanceSearchResultTitle"
        android:id="@android:id/text1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:layout_above="@android:id/text2"
        android:layout_centerVertical="true"
        style="?android:attr/dropDownItemStyle"/>
</RelativeLayout>
