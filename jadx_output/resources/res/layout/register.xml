<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#282828"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:text="@string/register_title"/>
    <EditText
        android:id="@+id/reg_user_name"
        android:background="@drawable/border_gray"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginTop="3dp"
        android:hint="@string/register_user_name"
        android:inputType="text"
        android:textCursorDrawable="@null"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        android:autofillHints="username"
        app:drawableStartCompat="@drawable/ic_user"/>
    <LinearLayout
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="horizontal"
        android:background="@drawable/border_gray"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp">
        <ImageButton
            android:theme="@style/RippleWhite"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/register_limit"
            app:srcCompat="@drawable/ic_date"/>
        <TextView
            android:textSize="18sp"
            android:textColor="@color/darkgray"
            android:background="@android:color/transparent"
            android:padding="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/register_limit"/>
        <DatePicker
            android:id="@+id/date_picker"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            style="@android:style/Widget.DatePicker"/>
    </LinearLayout>
    <TextView
        android:gravity="center"
        android:id="@+id/reg_request"
        android:background="@drawable/border_gray"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:singleLine="true"
        android:drawablePadding="10dp"
        android:textIsSelectable="true"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        app:drawableStartCompat="@drawable/ic_pwd"/>
    <TextView
        android:textStyle="bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/register_tips"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"/>
    <EditText
        android:id="@+id/reg_response"
        android:background="@drawable/border_gray"
        android:scrollbars="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="@string/register_response"
        android:maxLines="5"
        android:minLines="3"
        android:drawablePadding="10dp"
        android:inputType="textMultiLine"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        android:autofillHints="name"
        app:drawableStartCompat="@drawable/ic_key"/>
    <CheckBox
        android:gravity="center"
        android:layout_gravity="center"
        android:id="@+id/reg_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/register_check"/>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textStyle="bold"
            android:textColor="#282828"
            android:gravity="center"
            android:id="@+id/reg_cancel"
            android:padding="14dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/register_cancel"
            android:layout_weight="1"/>
        <Space
            android:layout_width="1dp"
            android:layout_height="match_parent"/>
        <TextView
            android:textStyle="bold"
            android:textColor="#282828"
            android:gravity="center"
            android:id="@+id/reg_agree"
            android:padding="14dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/register_ok"
            android:layout_weight="1"/>
    </LinearLayout>
</LinearLayout>
