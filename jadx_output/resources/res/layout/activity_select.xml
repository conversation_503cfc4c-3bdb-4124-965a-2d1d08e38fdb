<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/rlTitles"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="48dp">
        <ImageView
            android:id="@+id/ivBack"
            android:padding="15dp"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/back"
            android:layout_centerVertical="true"/>
        <LinearLayout
            android:gravity="center"
            android:layout_gravity="center_vertical"
            android:background="@drawable/bg_white2"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:paddingHorizontal="10dp">
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_search"/>
            <EditText
                android:textSize="14sp"
                android:textColor="@color/black"
                android:textColorHint="#919fac"
                android:gravity="center_vertical"
                android:id="@+id/city"
                android:background="@null"
                android:paddingLeft="10dp"
                android:paddingTop="7dp"
                android:paddingRight="10dp"
                android:paddingBottom="7dp"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:hint="搜索地点"
                android:layout_weight="1"
                android:inputType="text"
                android:imeOptions="actionSearch"/>
        </LinearLayout>
        <TextView
            android:textColor="@color/white"
            android:layout_gravity="center_vertical"
            android:id="@+id/clear_text"
            android:padding="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:text="清除"/>
    </LinearLayout>
    <ListView
        android:id="@+id/sug_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@null"
        android:dividerHeight="0dp"/>
</LinearLayout>
