<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/border_window"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="6dp"
    android:paddingEnd="10dp">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/poi_fly"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_fly"
            android:contentDescription="@null"
            android:layout_marginEnd="5dp"
            app:tint="@color/colorAccent"/>
        <LinearLayout
            android:layout_gravity="center_vertical"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="16sp"
                android:id="@+id/poi_longitude"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:textSize="16sp"
                android:id="@+id/poi_latitude"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>
    <TextView
        android:id="@+id/poi_address"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"/>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginStart="3dp">
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/poi_save"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_save"
            android:contentDescription="@null"
            app:tint="@color/colorAccent"/>
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/poi_copy"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_copy"
            android:layout_weight="1"
            android:contentDescription="@null"
            app:tint="@color/colorAccent"/>
        <ImageButton
            android:theme="@style/RippleWhite"
            android:id="@+id/poi_share"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_share"
            android:contentDescription="@null"
            app:tint="@color/colorAccent"/>
    </LinearLayout>
</LinearLayout>
