<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="10dp">
    <TextView
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="#282828"
        android:layout_gravity="center"
        android:id="@+id/update_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:text="@string/update_title"/>
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:rowCount="2"
        android:columnCount="2">
        <TextView
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_column="0"
            android:text="@string/update_time"
            android:singleLine="true"
            android:layout_row="0"/>
        <TextView
            android:textSize="16sp"
            android:id="@+id/update_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_column="1"
            android:text=""
            android:layout_row="0"/>
        <TextView
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_column="0"
            android:text="@string/update_commit"
            android:layout_row="1"/>
        <TextView
            android:textSize="16sp"
            android:id="@+id/update_commit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_column="1"
            android:text=""
            android:singleLine="true"
            android:layout_row="1"/>
    </GridLayout>
    <ScrollView
        android:background="@drawable/border_gray"
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp">
        <TextView
            android:textSize="15sp"
            android:textColor="#282828"
            android:id="@+id/update_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:lineSpacingMultiplier="1.2"/>
    </ScrollView>
    <LinearLayout
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <Button
            android:id="@+id/update_ignore"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:text="@string/update_ignore"/>
        <Space
            android:layout_width="60dp"
            android:layout_height="match_parent"/>
        <Button
            android:id="@+id/update_agree"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:text="@string/update_download"/>
    </LinearLayout>
</LinearLayout>
