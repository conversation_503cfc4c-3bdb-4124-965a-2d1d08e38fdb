Aacute=Ã
aacute=Ã¡
Abreve=Ä
abreve=Ä
ac=â¾
acd=â¿
acE=â¾Ì³
Acirc=Ã
acirc=Ã¢
acute=Â´
Acy=Ð
acy=Ð°
AElig=Ã
aelig=Ã¦
af=â¡
Afr=ð
afr=ð
Agrave=Ã
agrave=Ã 
alefsym=âµ
aleph=âµ
Alpha=Î
alpha=Î±
Amacr=Ä
amacr=Ä
amalg=â¨¿
amp=&
AMP=&
andand=â©
And=â©
and=â§
andd=â©
andslope=â©
andv=â©
ang=â 
ange=â¦¤
angle=â 
angmsdaa=â¦¨
angmsdab=â¦©
angmsdac=â¦ª
angmsdad=â¦«
angmsdae=â¦¬
angmsdaf=â¦­
angmsdag=â¦®
angmsdah=â¦¯
angmsd=â¡
angrt=â
angrtvb=â¾
angrtvbd=â¦
angsph=â¢
angst=Ã
angzarr=â¼
Aogon=Ä
aogon=Ä
Aopf=ð¸
aopf=ð
apacir=â©¯
ap=â
apE=â©°
ape=â
apid=â
apos='
ApplyFunction=â¡
approx=â
approxeq=â
Aring=Ã
aring=Ã¥
Ascr=ð
ascr=ð¶
Assign=â
ast=*
asymp=â
asympeq=â
Atilde=Ã
atilde=Ã£
Auml=Ã
auml=Ã¤
awconint=â³
awint=â¨
backcong=â
backepsilon=Ï¶
backprime=âµ
backsim=â½
backsimeq=â
Backslash=â
Barv=â«§
barvee=â½
barwed=â
Barwed=â
barwedge=â
bbrk=âµ
bbrktbrk=â¶
bcong=â
Bcy=Ð
bcy=Ð±
bdquo=â
becaus=âµ
because=âµ
Because=âµ
bemptyv=â¦°
bepsi=Ï¶
bernou=â¬
Bernoullis=â¬
Beta=Î
beta=Î²
beth=â¶
between=â¬
Bfr=ð
bfr=ð
bigcap=â
bigcirc=â¯
bigcup=â
bigodot=â¨
bigoplus=â¨
bigotimes=â¨
bigsqcup=â¨
bigstar=â
bigtriangledown=â½
bigtriangleup=â³
biguplus=â¨
bigvee=â
bigwedge=â
bkarow=â¤
blacklozenge=â§«
blacksquare=âª
blacktriangle=â´
blacktriangledown=â¾
blacktriangleleft=â
blacktriangleright=â¸
blank=â£
blk12=â
blk14=â
blk34=â
block=â
bne==â¥
bnequiv=â¡â¥
bNot=â«­
bnot=â
Bopf=ð¹
bopf=ð
bot=â¥
bottom=â¥
bowtie=â
boxbox=â§
boxdl=â
boxdL=â
boxDl=â
boxDL=â
boxdr=â
boxdR=â
boxDr=â
boxDR=â
boxh=â
boxH=â
boxhd=â¬
boxHd=â¤
boxhD=â¥
boxHD=â¦
boxhu=â´
boxHu=â§
boxhU=â¨
boxHU=â©
boxminus=â
boxplus=â
boxtimes=â 
boxul=â
boxuL=â
boxUl=â
boxUL=â
boxur=â
boxuR=â
boxUr=â
boxUR=â
boxv=â
boxV=â
boxvh=â¼
boxvH=âª
boxVh=â«
boxVH=â¬
boxvl=â¤
boxvL=â¡
boxVl=â¢
boxVL=â£
boxvr=â
boxvR=â
boxVr=â
boxVR=â 
bprime=âµ
breve=Ë
Breve=Ë
brvbar=Â¦
bscr=ð·
Bscr=â¬
bsemi=â
bsim=â½
bsime=â
bsolb=â§
bsol=\
bsolhsub=â
bull=â¢
bullet=â¢
bump=â
bumpE=âª®
bumpe=â
Bumpeq=â
bumpeq=â
Cacute=Ä
cacute=Ä
capand=â©
capbrcup=â©
capcap=â©
cap=â©
Cap=â
capcup=â©
capdot=â©
CapitalDifferentialD=â
caps=â©ï¸
caret=â
caron=Ë
Cayleys=â­
ccaps=â©
Ccaron=Ä
ccaron=Ä
Ccedil=Ã
ccedil=Ã§
Ccirc=Ä
ccirc=Ä
Cconint=â°
ccups=â©
ccupssm=â©
Cdot=Ä
cdot=Ä
cedil=Â¸
Cedilla=Â¸
cemptyv=â¦²
cent=Â¢
centerdot=Â·
CenterDot=Â·
cfr=ð 
Cfr=â­
CHcy=Ð§
chcy=Ñ
check=â
checkmark=â
Chi=Î§
chi=Ï
circ=Ë
circeq=â
circlearrowleft=âº
circlearrowright=â»
circledast=â
circledcirc=â
circleddash=â
CircleDot=â
circledR=Â®
circledS=â
CircleMinus=â
CirclePlus=â
CircleTimes=â
cir=â
cirE=â§
cire=â
cirfnint=â¨
cirmid=â«¯
cirscir=â§
ClockwiseContourIntegral=â²
CloseCurlyDoubleQuote=â
CloseCurlyQuote=â
clubs=â£
clubsuit=â£
colon=:
Colon=â·
Colone=â©´
colone=â
coloneq=â
comma=,
commat=@
comp=â
compfn=â
complement=â
complexes=â
cong=â
congdot=â©­
Congruent=â¡
conint=â®
Conint=â¯
ContourIntegral=â®
copf=ð
Copf=â
coprod=â
Coproduct=â
copy=Â©
COPY=Â©
copysr=â
CounterClockwiseContourIntegral=â³
crarr=âµ
cross=â
Cross=â¨¯
Cscr=ð
cscr=ð¸
csub=â«
csube=â«
csup=â«
csupe=â«
ctdot=â¯
cudarrl=â¤¸
cudarrr=â¤µ
cuepr=â
cuesc=â
cularr=â¶
cularrp=â¤½
cupbrcap=â©
cupcap=â©
CupCap=â
cup=âª
Cup=â
cupcup=â©
cupdot=â
cupor=â©
cups=âªï¸
curarr=â·
curarrm=â¤¼
curlyeqprec=â
curlyeqsucc=â
curlyvee=â
curlywedge=â
curren=Â¤
curvearrowleft=â¶
curvearrowright=â·
cuvee=â
cuwed=â
cwconint=â²
cwint=â±
cylcty=â­
dagger=â 
Dagger=â¡
daleth=â¸
darr=â
Darr=â¡
dArr=â
dash=â
Dashv=â«¤
dashv=â£
dbkarow=â¤
dblac=Ë
Dcaron=Ä
dcaron=Ä
Dcy=Ð
dcy=Ð´
ddagger=â¡
ddarr=â
DD=â
dd=â
DDotrahd=â¤
ddotseq=â©·
deg=Â°
Del=â
Delta=Î
delta=Î´
demptyv=â¦±
dfisht=â¥¿
Dfr=ð
dfr=ð¡
dHar=â¥¥
dharl=â
dharr=â
DiacriticalAcute=Â´
DiacriticalDot=Ë
DiacriticalDoubleAcute=Ë
DiacriticalGrave=`
DiacriticalTilde=Ë
diam=â
diamond=â
Diamond=â
diamondsuit=â¦
diams=â¦
die=Â¨
DifferentialD=â
digamma=Ï
disin=â²
div=Ã·
divide=Ã·
divideontimes=â
divonx=â
DJcy=Ð
djcy=Ñ
dlcorn=â
dlcrop=â
dollar=$
Dopf=ð»
dopf=ð
Dot=Â¨
dot=Ë
DotDot=â
doteq=â
doteqdot=â
DotEqual=â
dotminus=â¸
dotplus=â
dotsquare=â¡
doublebarwedge=â
DoubleContourIntegral=â¯
DoubleDot=Â¨
DoubleDownArrow=â
DoubleLeftArrow=â
DoubleLeftRightArrow=â
DoubleLeftTee=â«¤
DoubleLongLeftArrow=â¸
DoubleLongLeftRightArrow=âº
DoubleLongRightArrow=â¹
DoubleRightArrow=â
DoubleRightTee=â¨
DoubleUpArrow=â
DoubleUpDownArrow=â
DoubleVerticalBar=â¥
DownArrowBar=â¤
downarrow=â
DownArrow=â
Downarrow=â
DownArrowUpArrow=âµ
DownBreve=Ì
downdownarrows=â
downharpoonleft=â
downharpoonright=â
DownLeftRightVector=â¥
DownLeftTeeVector=â¥
DownLeftVectorBar=â¥
DownLeftVector=â½
DownRightTeeVector=â¥
DownRightVectorBar=â¥
DownRightVector=â
DownTeeArrow=â§
DownTee=â¤
drbkarow=â¤
drcorn=â
drcrop=â
Dscr=ð
dscr=ð¹
DScy=Ð
dscy=Ñ
dsol=â§¶
Dstrok=Ä
dstrok=Ä
dtdot=â±
dtri=â¿
dtrif=â¾
duarr=âµ
duhar=â¥¯
dwangle=â¦¦
DZcy=Ð
dzcy=Ñ
dzigrarr=â¿
Eacute=Ã
eacute=Ã©
easter=â©®
Ecaron=Ä
ecaron=Ä
Ecirc=Ã
ecirc=Ãª
ecir=â
ecolon=â
Ecy=Ð­
ecy=Ñ
eDDot=â©·
Edot=Ä
edot=Ä
eDot=â
ee=â
efDot=â
Efr=ð
efr=ð¢
eg=âª
Egrave=Ã
egrave=Ã¨
egs=âª
egsdot=âª
el=âª
Element=â
elinters=â§
ell=â
els=âª
elsdot=âª
Emacr=Ä
emacr=Ä
empty=â
emptyset=â
EmptySmallSquare=â»
emptyv=â
EmptyVerySmallSquare=â«
emsp13=â
emsp14=â
emsp=â
ENG=Å
eng=Å
ensp=â
Eogon=Ä
eogon=Ä
Eopf=ð¼
eopf=ð
epar=â
eparsl=â§£
eplus=â©±
epsi=Îµ
Epsilon=Î
epsilon=Îµ
epsiv=Ïµ
eqcirc=â
eqcolon=â
eqsim=â
eqslantgtr=âª
eqslantless=âª
Equal=â©µ
equals==
EqualTilde=â
equest=â
Equilibrium=â
equiv=â¡
equivDD=â©¸
eqvparsl=â§¥
erarr=â¥±
erDot=â
escr=â¯
Escr=â°
esdot=â
Esim=â©³
esim=â
Eta=Î
eta=Î·
ETH=Ã
eth=Ã°
Euml=Ã
euml=Ã«
euro=â¬
excl=!
exist=â
Exists=â
expectation=â°
exponentiale=â
ExponentialE=â
fallingdotseq=â
Fcy=Ð¤
fcy=Ñ
female=â
ffilig=ï¬
fflig=ï¬
ffllig=ï¬
Ffr=ð
ffr=ð£
filig=ï¬
FilledSmallSquare=â¼
FilledVerySmallSquare=âª
fjlig=fj
flat=â­
fllig=ï¬
fltns=â±
fnof=Æ
Fopf=ð½
fopf=ð
forall=â
ForAll=â
fork=â
forkv=â«
Fouriertrf=â±
fpartint=â¨
frac12=Â½
frac13=â
frac14=Â¼
frac15=â
frac16=â
frac18=â
frac23=â
frac25=â
frac34=Â¾
frac35=â
frac38=â
frac45=â
frac56=â
frac58=â
frac78=â
frasl=â
frown=â¢
fscr=ð»
Fscr=â±
gacute=Çµ
Gamma=Î
gamma=Î³
Gammad=Ï
gammad=Ï
gap=âª
Gbreve=Ä
gbreve=Ä
Gcedil=Ä¢
Gcirc=Ä
gcirc=Ä
Gcy=Ð
gcy=Ð³
Gdot=Ä 
gdot=Ä¡
ge=â¥
gE=â§
gEl=âª
gel=â
geq=â¥
geqq=â§
geqslant=â©¾
gescc=âª©
ges=â©¾
gesdot=âª
gesdoto=âª
gesdotol=âª
gesl=âï¸
gesles=âª
Gfr=ð
gfr=ð¤
gg=â«
Gg=â
ggg=â
gimel=â·
GJcy=Ð
gjcy=Ñ
gla=âª¥
gl=â·
glE=âª
glj=âª¤
gnap=âª
gnapprox=âª
gne=âª
gnE=â©
gneq=âª
gneqq=â©
gnsim=â§
Gopf=ð¾
gopf=ð
grave=`
GreaterEqual=â¥
GreaterEqualLess=â
GreaterFullEqual=â§
GreaterGreater=âª¢
GreaterLess=â·
GreaterSlantEqual=â©¾
GreaterTilde=â³
Gscr=ð¢
gscr=â
gsim=â³
gsime=âª
gsiml=âª
gtcc=âª§
gtcir=â©º
gt=>
GT=>
Gt=â«
gtdot=â
gtlPar=â¦
gtquest=â©¼
gtrapprox=âª
gtrarr=â¥¸
gtrdot=â
gtreqless=â
gtreqqless=âª
gtrless=â·
gtrsim=â³
gvertneqq=â©ï¸
gvnE=â©ï¸
Hacek=Ë
hairsp=â
half=Â½
hamilt=â
HARDcy=Ðª
hardcy=Ñ
harrcir=â¥
harr=â
hArr=â
harrw=â­
Hat=^
hbar=â
Hcirc=Ä¤
hcirc=Ä¥
hearts=â¥
heartsuit=â¥
hellip=â¦
hercon=â¹
hfr=ð¥
Hfr=â
HilbertSpace=â
hksearow=â¤¥
hkswarow=â¤¦
hoarr=â¿
homtht=â»
hookleftarrow=â©
hookrightarrow=âª
hopf=ð
Hopf=â
horbar=â
HorizontalLine=â
hscr=ð½
Hscr=â
hslash=â
Hstrok=Ä¦
hstrok=Ä§
HumpDownHump=â
HumpEqual=â
hybull=â
hyphen=â
Iacute=Ã
iacute=Ã­
ic=â£
Icirc=Ã
icirc=Ã®
Icy=Ð
icy=Ð¸
Idot=Ä°
IEcy=Ð
iecy=Ðµ
iexcl=Â¡
iff=â
ifr=ð¦
Ifr=â
Igrave=Ã
igrave=Ã¬
ii=â
iiiint=â¨
iiint=â­
iinfin=â§
iiota=â©
IJlig=Ä²
ijlig=Ä³
Imacr=Äª
imacr=Ä«
image=â
ImaginaryI=â
imagline=â
imagpart=â
imath=Ä±
Im=â
imof=â·
imped=Æµ
Implies=â
incare=â
in=â
infin=â
infintie=â§
inodot=Ä±
intcal=âº
int=â«
Int=â¬
integers=â¤
Integral=â«
intercal=âº
Intersection=â
intlarhk=â¨
intprod=â¨¼
InvisibleComma=â£
InvisibleTimes=â¢
IOcy=Ð
iocy=Ñ
Iogon=Ä®
iogon=Ä¯
Iopf=ð
iopf=ð
Iota=Î
iota=Î¹
iprod=â¨¼
iquest=Â¿
iscr=ð¾
Iscr=â
isin=â
isindot=âµ
isinE=â¹
isins=â´
isinsv=â³
isinv=â
it=â¢
Itilde=Ä¨
itilde=Ä©
Iukcy=Ð
iukcy=Ñ
Iuml=Ã
iuml=Ã¯
Jcirc=Ä´
jcirc=Äµ
Jcy=Ð
jcy=Ð¹
Jfr=ð
jfr=ð§
jmath=È·
Jopf=ð
jopf=ð
Jscr=ð¥
jscr=ð¿
Jsercy=Ð
jsercy=Ñ
Jukcy=Ð
jukcy=Ñ
Kappa=Î
kappa=Îº
kappav=Ï°
Kcedil=Ä¶
kcedil=Ä·
Kcy=Ð
kcy=Ðº
Kfr=ð
kfr=ð¨
kgreen=Ä¸
KHcy=Ð¥
khcy=Ñ
KJcy=Ð
kjcy=Ñ
Kopf=ð
kopf=ð
Kscr=ð¦
kscr=ð
lAarr=â
Lacute=Ä¹
lacute=Äº
laemptyv=â¦´
lagran=â
Lambda=Î
lambda=Î»
lang=â¨
Lang=âª
langd=â¦
langle=â¨
lap=âª
Laplacetrf=â
laquo=Â«
larrb=â¤
larrbfs=â¤
larr=â
Larr=â
lArr=â
larrfs=â¤
larrhk=â©
larrlp=â«
larrpl=â¤¹
larrsim=â¥³
larrtl=â¢
latail=â¤
lAtail=â¤
lat=âª«
late=âª­
lates=âª­ï¸
lbarr=â¤
lBarr=â¤
lbbrk=â²
lbrace={
lbrack=[
lbrke=â¦
lbrksld=â¦
lbrkslu=â¦
Lcaron=Ä½
lcaron=Ä¾
Lcedil=Ä»
lcedil=Ä¼
lceil=â
lcub={
Lcy=Ð
lcy=Ð»
ldca=â¤¶
ldquo=â
ldquor=â
ldrdhar=â¥§
ldrushar=â¥
ldsh=â²
le=â¤
lE=â¦
LeftAngleBracket=â¨
LeftArrowBar=â¤
leftarrow=â
LeftArrow=â
Leftarrow=â
LeftArrowRightArrow=â
leftarrowtail=â¢
LeftCeiling=â
LeftDoubleBracket=â¦
LeftDownTeeVector=â¥¡
LeftDownVectorBar=â¥
LeftDownVector=â
LeftFloor=â
leftharpoondown=â½
leftharpoonup=â¼
leftleftarrows=â
leftrightarrow=â
LeftRightArrow=â
Leftrightarrow=â
leftrightarrows=â
leftrightharpoons=â
leftrightsquigarrow=â­
LeftRightVector=â¥
LeftTeeArrow=â¤
LeftTee=â£
LeftTeeVector=â¥
leftthreetimes=â
LeftTriangleBar=â§
LeftTriangle=â²
LeftTriangleEqual=â´
LeftUpDownVector=â¥
LeftUpTeeVector=â¥ 
LeftUpVectorBar=â¥
LeftUpVector=â¿
LeftVectorBar=â¥
LeftVector=â¼
lEg=âª
leg=â
leq=â¤
leqq=â¦
leqslant=â©½
lescc=âª¨
les=â©½
lesdot=â©¿
lesdoto=âª
lesdotor=âª
lesg=âï¸
lesges=âª
lessapprox=âª
lessdot=â
lesseqgtr=â
lesseqqgtr=âª
LessEqualGreater=â
LessFullEqual=â¦
LessGreater=â¶
lessgtr=â¶
LessLess=âª¡
lesssim=â²
LessSlantEqual=â©½
LessTilde=â²
lfisht=â¥¼
lfloor=â
Lfr=ð
lfr=ð©
lg=â¶
lgE=âª
lHar=â¥¢
lhard=â½
lharu=â¼
lharul=â¥ª
lhblk=â
LJcy=Ð
ljcy=Ñ
llarr=â
ll=âª
Ll=â
llcorner=â
Lleftarrow=â
llhard=â¥«
lltri=âº
Lmidot=Ä¿
lmidot=Å
lmoustache=â°
lmoust=â°
lnap=âª
lnapprox=âª
lne=âª
lnE=â¨
lneq=âª
lneqq=â¨
lnsim=â¦
loang=â¬
loarr=â½
lobrk=â¦
longleftarrow=âµ
LongLeftArrow=âµ
Longleftarrow=â¸
longleftrightarrow=â·
LongLeftRightArrow=â·
Longleftrightarrow=âº
longmapsto=â¼
longrightarrow=â¶
LongRightArrow=â¶
Longrightarrow=â¹
looparrowleft=â«
looparrowright=â¬
lopar=â¦
Lopf=ð
lopf=ð
loplus=â¨­
lotimes=â¨´
lowast=â
lowbar=_
LowerLeftArrow=â
LowerRightArrow=â
loz=â
lozenge=â
lozf=â§«
lpar=(
lparlt=â¦
lrarr=â
lrcorner=â
lrhar=â
lrhard=â¥­
lrm=â
lrtri=â¿
lsaquo=â¹
lscr=ð
Lscr=â
lsh=â°
Lsh=â°
lsim=â²
lsime=âª
lsimg=âª
lsqb=[
lsquo=â
lsquor=â
Lstrok=Å
lstrok=Å
ltcc=âª¦
ltcir=â©¹
lt=<
LT=<
Lt=âª
ltdot=â
lthree=â
ltimes=â
ltlarr=â¥¶
ltquest=â©»
ltri=â
ltrie=â´
ltrif=â
ltrPar=â¦
lurdshar=â¥
luruhar=â¥¦
lvertneqq=â¨ï¸
lvnE=â¨ï¸
macr=Â¯
male=â
malt=â 
maltese=â 
Map=â¤
map=â¦
mapsto=â¦
mapstodown=â§
mapstoleft=â¤
mapstoup=â¥
marker=â®
mcomma=â¨©
Mcy=Ð
mcy=Ð¼
mdash=â
mDDot=âº
measuredangle=â¡
MediumSpace=â
Mellintrf=â³
Mfr=ð
mfr=ðª
mho=â§
micro=Âµ
midast=*
midcir=â«°
mid=â£
middot=Â·
minusb=â
minus=â
minusd=â¸
minusdu=â¨ª
MinusPlus=â
mlcp=â«
mldr=â¦
mnplus=â
models=â§
Mopf=ð
mopf=ð
mp=â
mscr=ð
Mscr=â³
mstpos=â¾
Mu=Î
mu=Î¼
multimap=â¸
mumap=â¸
nabla=â
Nacute=Å
nacute=Å
nang=â â
nap=â
napE=â©°Ì¸
napid=âÌ¸
napos=Å
napprox=â
natural=â®
naturals=â
natur=â®
nbsp=Â 
nbump=âÌ¸
nbumpe=âÌ¸
ncap=â©
Ncaron=Å
ncaron=Å
Ncedil=Å
ncedil=Å
ncong=â
ncongdot=â©­Ì¸
ncup=â©
Ncy=Ð
ncy=Ð½
ndash=â
nearhk=â¤¤
nearr=â
neArr=â
nearrow=â
ne=â 
nedot=âÌ¸
NegativeMediumSpace=â
NegativeThickSpace=â
NegativeThinSpace=â
NegativeVeryThinSpace=â
nequiv=â¢
nesear=â¤¨
nesim=âÌ¸
NestedGreaterGreater=â«
NestedLessLess=âª
NewLine=

nexist=â
nexists=â
Nfr=ð
nfr=ð«
ngE=â§Ì¸
nge=â±
ngeq=â±
ngeqq=â§Ì¸
ngeqslant=â©¾Ì¸
nges=â©¾Ì¸
nGg=âÌ¸
ngsim=âµ
nGt=â«â
ngt=â¯
ngtr=â¯
nGtv=â«Ì¸
nharr=â®
nhArr=â
nhpar=â«²
ni=â
nis=â¼
nisd=âº
niv=â
NJcy=Ð
njcy=Ñ
nlarr=â
nlArr=â
nldr=â¥
nlE=â¦Ì¸
nle=â°
nleftarrow=â
nLeftarrow=â
nleftrightarrow=â®
nLeftrightarrow=â
nleq=â°
nleqq=â¦Ì¸
nleqslant=â©½Ì¸
nles=â©½Ì¸
nless=â®
nLl=âÌ¸
nlsim=â´
nLt=âªâ
nlt=â®
nltri=âª
nltrie=â¬
nLtv=âªÌ¸
nmid=â¤
NoBreak=â 
NonBreakingSpace=Â 
nopf=ð
Nopf=â
Not=â«¬
not=Â¬
NotCongruent=â¢
NotCupCap=â­
NotDoubleVerticalBar=â¦
NotElement=â
NotEqual=â 
NotEqualTilde=âÌ¸
NotExists=â
NotGreater=â¯
NotGreaterEqual=â±
NotGreaterFullEqual=â§Ì¸
NotGreaterGreater=â«Ì¸
NotGreaterLess=â¹
NotGreaterSlantEqual=â©¾Ì¸
NotGreaterTilde=âµ
NotHumpDownHump=âÌ¸
NotHumpEqual=âÌ¸
notin=â
notindot=âµÌ¸
notinE=â¹Ì¸
notinva=â
notinvb=â·
notinvc=â¶
NotLeftTriangleBar=â§Ì¸
NotLeftTriangle=âª
NotLeftTriangleEqual=â¬
NotLess=â®
NotLessEqual=â°
NotLessGreater=â¸
NotLessLess=âªÌ¸
NotLessSlantEqual=â©½Ì¸
NotLessTilde=â´
NotNestedGreaterGreater=âª¢Ì¸
NotNestedLessLess=âª¡Ì¸
notni=â
notniva=â
notnivb=â¾
notnivc=â½
NotPrecedes=â
NotPrecedesEqual=âª¯Ì¸
NotPrecedesSlantEqual=â 
NotReverseElement=â
NotRightTriangleBar=â§Ì¸
NotRightTriangle=â«
NotRightTriangleEqual=â­
NotSquareSubset=âÌ¸
NotSquareSubsetEqual=â¢
NotSquareSuperset=âÌ¸
NotSquareSupersetEqual=â£
NotSubset=ââ
NotSubsetEqual=â
NotSucceeds=â
NotSucceedsEqual=âª°Ì¸
NotSucceedsSlantEqual=â¡
NotSucceedsTilde=â¿Ì¸
NotSuperset=ââ
NotSupersetEqual=â
NotTilde=â
NotTildeEqual=â
NotTildeFullEqual=â
NotTildeTilde=â
NotVerticalBar=â¤
nparallel=â¦
npar=â¦
nparsl=â«½â¥
npart=âÌ¸
npolint=â¨
npr=â
nprcue=â 
nprec=â
npreceq=âª¯Ì¸
npre=âª¯Ì¸
nrarrc=â¤³Ì¸
nrarr=â
nrArr=â
nrarrw=âÌ¸
nrightarrow=â
nRightarrow=â
nrtri=â«
nrtrie=â­
nsc=â
nsccue=â¡
nsce=âª°Ì¸
Nscr=ð©
nscr=ð
nshortmid=â¤
nshortparallel=â¦
nsim=â
nsime=â
nsimeq=â
nsmid=â¤
nspar=â¦
nsqsube=â¢
nsqsupe=â£
nsub=â
nsubE=â«Ì¸
nsube=â
nsubset=ââ
nsubseteq=â
nsubseteqq=â«Ì¸
nsucc=â
nsucceq=âª°Ì¸
nsup=â
nsupE=â«Ì¸
nsupe=â
nsupset=ââ
nsupseteq=â
nsupseteqq=â«Ì¸
ntgl=â¹
Ntilde=Ã
ntilde=Ã±
ntlg=â¸
ntriangleleft=âª
ntrianglelefteq=â¬
ntriangleright=â«
ntrianglerighteq=â­
Nu=Î
nu=Î½
num=#
numero=â
numsp=â
nvap=ââ
nvdash=â¬
nvDash=â­
nVdash=â®
nVDash=â¯
nvge=â¥â
nvgt=>â
nvHarr=â¤
nvinfin=â§
nvlArr=â¤
nvle=â¤â
nvlt=<â
nvltrie=â´â
nvrArr=â¤
nvrtrie=âµâ
nvsim=â¼â
nwarhk=â¤£
nwarr=â
nwArr=â
nwarrow=â
nwnear=â¤§
Oacute=Ã
oacute=Ã³
oast=â
Ocirc=Ã
ocirc=Ã´
ocir=â
Ocy=Ð
ocy=Ð¾
odash=â
Odblac=Å
odblac=Å
odiv=â¨¸
odot=â
odsold=â¦¼
OElig=Å
oelig=Å
ofcir=â¦¿
Ofr=ð
ofr=ð¬
ogon=Ë
Ograve=Ã
ograve=Ã²
ogt=â§
ohbar=â¦µ
ohm=Î©
oint=â®
olarr=âº
olcir=â¦¾
olcross=â¦»
oline=â¾
olt=â§
Omacr=Å
omacr=Å
Omega=Î©
omega=Ï
Omicron=Î
omicron=Î¿
omid=â¦¶
ominus=â
Oopf=ð
oopf=ð 
opar=â¦·
OpenCurlyDoubleQuote=â
OpenCurlyQuote=â
operp=â¦¹
oplus=â
orarr=â»
Or=â©
or=â¨
ord=â©
order=â´
orderof=â´
ordf=Âª
ordm=Âº
origof=â¶
oror=â©
orslope=â©
orv=â©
oS=â
Oscr=ðª
oscr=â´
Oslash=Ã
oslash=Ã¸
osol=â
Otilde=Ã
otilde=Ãµ
otimesas=â¨¶
Otimes=â¨·
otimes=â
Ouml=Ã
ouml=Ã¶
ovbar=â½
OverBar=â¾
OverBrace=â
OverBracket=â´
OverParenthesis=â
para=Â¶
parallel=â¥
par=â¥
parsim=â«³
parsl=â«½
part=â
PartialD=â
Pcy=Ð
pcy=Ð¿
percnt=%
period=.
permil=â°
perp=â¥
pertenk=â±
Pfr=ð
pfr=ð­
Phi=Î¦
phi=Ï
phiv=Ï
phmmat=â³
phone=â
Pi=Î 
pi=Ï
pitchfork=â
piv=Ï
planck=â
planckh=â
plankv=â
plusacir=â¨£
plusb=â
pluscir=â¨¢
plus=+
plusdo=â
plusdu=â¨¥
pluse=â©²
PlusMinus=Â±
plusmn=Â±
plussim=â¨¦
plustwo=â¨§
pm=Â±
Poincareplane=â
pointint=â¨
popf=ð¡
Popf=â
pound=Â£
prap=âª·
Pr=âª»
pr=âº
prcue=â¼
precapprox=âª·
prec=âº
preccurlyeq=â¼
Precedes=âº
PrecedesEqual=âª¯
PrecedesSlantEqual=â¼
PrecedesTilde=â¾
preceq=âª¯
precnapprox=âª¹
precneqq=âªµ
precnsim=â¨
pre=âª¯
prE=âª³
precsim=â¾
prime=â²
Prime=â³
primes=â
prnap=âª¹
prnE=âªµ
prnsim=â¨
prod=â
Product=â
profalar=â®
profline=â
profsurf=â
prop=â
Proportional=â
Proportion=â·
propto=â
prsim=â¾
prurel=â°
Pscr=ð«
pscr=ð
Psi=Î¨
psi=Ï
puncsp=â
Qfr=ð
qfr=ð®
qint=â¨
qopf=ð¢
Qopf=â
qprime=â
Qscr=ð¬
qscr=ð
quaternions=â
quatint=â¨
quest=?
questeq=â
quot="
QUOT="
rAarr=â
race=â½Ì±
Racute=Å
racute=Å
radic=â
raemptyv=â¦³
rang=â©
Rang=â«
rangd=â¦
range=â¦¥
rangle=â©
raquo=Â»
rarrap=â¥µ
rarrb=â¥
rarrbfs=â¤ 
rarrc=â¤³
rarr=â
Rarr=â 
rArr=â
rarrfs=â¤
rarrhk=âª
rarrlp=â¬
rarrpl=â¥
rarrsim=â¥´
Rarrtl=â¤
rarrtl=â£
rarrw=â
ratail=â¤
rAtail=â¤
ratio=â¶
rationals=â
rbarr=â¤
rBarr=â¤
RBarr=â¤
rbbrk=â³
rbrace=}
rbrack=]
rbrke=â¦
rbrksld=â¦
rbrkslu=â¦
Rcaron=Å
rcaron=Å
Rcedil=Å
rcedil=Å
rceil=â
rcub=}
Rcy=Ð 
rcy=Ñ
rdca=â¤·
rdldhar=â¥©
rdquo=â
rdquor=â
rdsh=â³
real=â
realine=â
realpart=â
reals=â
Re=â
rect=â­
reg=Â®
REG=Â®
ReverseElement=â
ReverseEquilibrium=â
ReverseUpEquilibrium=â¥¯
rfisht=â¥½
rfloor=â
rfr=ð¯
Rfr=â
rHar=â¥¤
rhard=â
rharu=â
rharul=â¥¬
Rho=Î¡
rho=Ï
rhov=Ï±
RightAngleBracket=â©
RightArrowBar=â¥
rightarrow=â
RightArrow=â
Rightarrow=â
RightArrowLeftArrow=â
rightarrowtail=â£
RightCeiling=â
RightDoubleBracket=â§
RightDownTeeVector=â¥
RightDownVectorBar=â¥
RightDownVector=â
RightFloor=â
rightharpoondown=â
rightharpoonup=â
rightleftarrows=â
rightleftharpoons=â
rightrightarrows=â
rightsquigarrow=â
RightTeeArrow=â¦
RightTee=â¢
RightTeeVector=â¥
rightthreetimes=â
RightTriangleBar=â§
RightTriangle=â³
RightTriangleEqual=âµ
RightUpDownVector=â¥
RightUpTeeVector=â¥
RightUpVectorBar=â¥
RightUpVector=â¾
RightVectorBar=â¥
RightVector=â
ring=Ë
risingdotseq=â
rlarr=â
rlhar=â
rlm=â
rmoustache=â±
rmoust=â±
rnmid=â«®
roang=â­
roarr=â¾
robrk=â§
ropar=â¦
ropf=ð£
Ropf=â
roplus=â¨®
rotimes=â¨µ
RoundImplies=â¥°
rpar=)
rpargt=â¦
rppolint=â¨
rrarr=â
Rrightarrow=â
rsaquo=âº
rscr=ð
Rscr=â
rsh=â±
Rsh=â±
rsqb=]
rsquo=â
rsquor=â
rthree=â
rtimes=â
rtri=â¹
rtrie=âµ
rtrif=â¸
rtriltri=â§
RuleDelayed=â§´
ruluhar=â¥¨
rx=â
Sacute=Å
sacute=Å
sbquo=â
scap=âª¸
Scaron=Å 
scaron=Å¡
Sc=âª¼
sc=â»
sccue=â½
sce=âª°
scE=âª´
Scedil=Å
scedil=Å
Scirc=Å
scirc=Å
scnap=âªº
scnE=âª¶
scnsim=â©
scpolint=â¨
scsim=â¿
Scy=Ð¡
scy=Ñ
sdotb=â¡
sdot=â
sdote=â©¦
searhk=â¤¥
searr=â
seArr=â
searrow=â
sect=Â§
semi=;
seswar=â¤©
setminus=â
setmn=â
sext=â¶
Sfr=ð
sfr=ð°
sfrown=â¢
sharp=â¯
SHCHcy=Ð©
shchcy=Ñ
SHcy=Ð¨
shcy=Ñ
ShortDownArrow=â
ShortLeftArrow=â
shortmid=â£
shortparallel=â¥
ShortRightArrow=â
ShortUpArrow=â
shy=Â­
Sigma=Î£
sigma=Ï
sigmaf=Ï
sigmav=Ï
sim=â¼
simdot=â©ª
sime=â
simeq=â
simg=âª
simgE=âª 
siml=âª
simlE=âª
simne=â
simplus=â¨¤
simrarr=â¥²
slarr=â
SmallCircle=â
smallsetminus=â
smashp=â¨³
smeparsl=â§¤
smid=â£
smile=â£
smt=âªª
smte=âª¬
smtes=âª¬ï¸
SOFTcy=Ð¬
softcy=Ñ
solbar=â¿
solb=â§
sol=/
Sopf=ð
sopf=ð¤
spades=â 
spadesuit=â 
spar=â¥
sqcap=â
sqcaps=âï¸
sqcup=â
sqcups=âï¸
Sqrt=â
sqsub=â
sqsube=â
sqsubset=â
sqsubseteq=â
sqsup=â
sqsupe=â
sqsupset=â
sqsupseteq=â
square=â¡
Square=â¡
SquareIntersection=â
SquareSubset=â
SquareSubsetEqual=â
SquareSuperset=â
SquareSupersetEqual=â
SquareUnion=â
squarf=âª
squ=â¡
squf=âª
srarr=â
Sscr=ð®
sscr=ð
ssetmn=â
ssmile=â£
sstarf=â
Star=â
star=â
starf=â
straightepsilon=Ïµ
straightphi=Ï
strns=Â¯
sub=â
Sub=â
subdot=âª½
subE=â«
sube=â
subedot=â«
submult=â«
subnE=â«
subne=â
subplus=âª¿
subrarr=â¥¹
subset=â
Subset=â
subseteq=â
subseteqq=â«
SubsetEqual=â
subsetneq=â
subsetneqq=â«
subsim=â«
subsub=â«
subsup=â«
succapprox=âª¸
succ=â»
succcurlyeq=â½
Succeeds=â»
SucceedsEqual=âª°
SucceedsSlantEqual=â½
SucceedsTilde=â¿
succeq=âª°
succnapprox=âªº
succneqq=âª¶
succnsim=â©
succsim=â¿
SuchThat=â
sum=â
Sum=â
sung=âª
sup1=Â¹
sup2=Â²
sup3=Â³
sup=â
Sup=â
supdot=âª¾
supdsub=â«
supE=â«
supe=â
supedot=â«
Superset=â
SupersetEqual=â
suphsol=â
suphsub=â«
suplarr=â¥»
supmult=â«
supnE=â«
supne=â
supplus=â«
supset=â
Supset=â
supseteq=â
supseteqq=â«
supsetneq=â
supsetneqq=â«
supsim=â«
supsub=â«
supsup=â«
swarhk=â¤¦
swarr=â
swArr=â
swarrow=â
swnwar=â¤ª
szlig=Ã
Tab=	
target=â
Tau=Î¤
tau=Ï
tbrk=â´
Tcaron=Å¤
tcaron=Å¥
Tcedil=Å¢
tcedil=Å£
Tcy=Ð¢
tcy=Ñ
tdot=â
telrec=â
Tfr=ð
tfr=ð±
there4=â´
therefore=â´
Therefore=â´
Theta=Î
theta=Î¸
thetasym=Ï
thetav=Ï
thickapprox=â
thicksim=â¼
ThickSpace=ââ
ThinSpace=â
thinsp=â
thkap=â
thksim=â¼
THORN=Ã
thorn=Ã¾
tilde=Ë
Tilde=â¼
TildeEqual=â
TildeFullEqual=â
TildeTilde=â
timesbar=â¨±
timesb=â 
times=Ã
timesd=â¨°
tint=â­
toea=â¤¨
topbot=â¶
topcir=â«±
top=â¤
Topf=ð
topf=ð¥
topfork=â«
tosa=â¤©
tprime=â´
trade=â¢
TRADE=â¢
triangle=âµ
triangledown=â¿
triangleleft=â
trianglelefteq=â´
triangleq=â
triangleright=â¹
trianglerighteq=âµ
tridot=â¬
trie=â
triminus=â¨º
TripleDot=â
triplus=â¨¹
trisb=â§
tritime=â¨»
trpezium=â¢
Tscr=ð¯
tscr=ð
TScy=Ð¦
tscy=Ñ
TSHcy=Ð
tshcy=Ñ
Tstrok=Å¦
tstrok=Å§
twixt=â¬
twoheadleftarrow=â
twoheadrightarrow=â 
Uacute=Ã
uacute=Ãº
uarr=â
Uarr=â
uArr=â
Uarrocir=â¥
Ubrcy=Ð
ubrcy=Ñ
Ubreve=Å¬
ubreve=Å­
Ucirc=Ã
ucirc=Ã»
Ucy=Ð£
ucy=Ñ
udarr=â
Udblac=Å°
udblac=Å±
udhar=â¥®
ufisht=â¥¾
Ufr=ð
ufr=ð²
Ugrave=Ã
ugrave=Ã¹
uHar=â¥£
uharl=â¿
uharr=â¾
uhblk=â
ulcorn=â
ulcorner=â
ulcrop=â
ultri=â¸
Umacr=Åª
umacr=Å«
uml=Â¨
UnderBar=_
UnderBrace=â
UnderBracket=âµ
UnderParenthesis=â
Union=â
UnionPlus=â
Uogon=Å²
uogon=Å³
Uopf=ð
uopf=ð¦
UpArrowBar=â¤
uparrow=â
UpArrow=â
Uparrow=â
UpArrowDownArrow=â
updownarrow=â
UpDownArrow=â
Updownarrow=â
UpEquilibrium=â¥®
upharpoonleft=â¿
upharpoonright=â¾
uplus=â
UpperLeftArrow=â
UpperRightArrow=â
upsi=Ï
Upsi=Ï
upsih=Ï
Upsilon=Î¥
upsilon=Ï
UpTeeArrow=â¥
UpTee=â¥
upuparrows=â
urcorn=â
urcorner=â
urcrop=â
Uring=Å®
uring=Å¯
urtri=â¹
Uscr=ð°
uscr=ð
utdot=â°
Utilde=Å¨
utilde=Å©
utri=âµ
utrif=â´
uuarr=â
Uuml=Ã
uuml=Ã¼
uwangle=â¦§
vangrt=â¦
varepsilon=Ïµ
varkappa=Ï°
varnothing=â
varphi=Ï
varpi=Ï
varpropto=â
varr=â
vArr=â
varrho=Ï±
varsigma=Ï
varsubsetneq=âï¸
varsubsetneqq=â«ï¸
varsupsetneq=âï¸
varsupsetneqq=â«ï¸
vartheta=Ï
vartriangleleft=â²
vartriangleright=â³
vBar=â«¨
Vbar=â««
vBarv=â«©
Vcy=Ð
vcy=Ð²
vdash=â¢
vDash=â¨
Vdash=â©
VDash=â«
Vdashl=â«¦
veebar=â»
vee=â¨
Vee=â
veeeq=â
vellip=â®
verbar=|
Verbar=â
vert=|
Vert=â
VerticalBar=â£
VerticalLine=|
VerticalSeparator=â
VerticalTilde=â
VeryThinSpace=â
Vfr=ð
vfr=ð³
vltri=â²
vnsub=ââ
vnsup=ââ
Vopf=ð
vopf=ð§
vprop=â
vrtri=â³
Vscr=ð±
vscr=ð
vsubnE=â«ï¸
vsubne=âï¸
vsupnE=â«ï¸
vsupne=âï¸
Vvdash=âª
vzigzag=â¦
Wcirc=Å´
wcirc=Åµ
wedbar=â©
wedge=â§
Wedge=â
wedgeq=â
weierp=â
Wfr=ð
wfr=ð´
Wopf=ð
wopf=ð¨
wp=â
wr=â
wreath=â
Wscr=ð²
wscr=ð
xcap=â
xcirc=â¯
xcup=â
xdtri=â½
Xfr=ð
xfr=ðµ
xharr=â·
xhArr=âº
Xi=Î
xi=Î¾
xlarr=âµ
xlArr=â¸
xmap=â¼
xnis=â»
xodot=â¨
Xopf=ð
xopf=ð©
xoplus=â¨
xotime=â¨
xrarr=â¶
xrArr=â¹
Xscr=ð³
xscr=ð
xsqcup=â¨
xuplus=â¨
xutri=â³
xvee=â
xwedge=â
Yacute=Ã
yacute=Ã½
YAcy=Ð¯
yacy=Ñ
Ycirc=Å¶
ycirc=Å·
Ycy=Ð«
ycy=Ñ
yen=Â¥
Yfr=ð
yfr=ð¶
YIcy=Ð
yicy=Ñ
Yopf=ð
yopf=ðª
Yscr=ð´
yscr=ð
YUcy=Ð®
yucy=Ñ
yuml=Ã¿
Yuml=Å¸
Zacute=Å¹
zacute=Åº
Zcaron=Å½
zcaron=Å¾
Zcy=Ð
zcy=Ð·
Zdot=Å»
zdot=Å¼
zeetrf=â¨
ZeroWidthSpace=â
Zeta=Î
zeta=Î¶
zfr=ð·
Zfr=â¨
ZHcy=Ð
zhcy=Ð¶
zigrarr=â
zopf=ð«
Zopf=â¤
Zscr=ðµ
zscr=ð
zwj=â
zwnj=â
