package de.robv.android.xposed;

import android.util.Log;
import de.robv.android.xposed.XC_MethodHook;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Member;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import top.canyie.pine.Pine;
import top.canyie.pine.callback.MethodHook;
import top.canyie.pine.xposed.PineXposed;

/* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
/* loaded from: assets/pine_xposed_api.dex */
public final class XposedBridge {
    public static final String TAG = "PineXposed";
    private static String[] sSupportedFeatures = new String[0];
    public static final ClassLoader BOOTCLASSLOADER = ClassLoader.getSystemClassLoader();

    @Deprecated
    public static int XPOSED_BRIDGE_VERSION = 90;
    private static final Map<Member, CopyOnWriteSortedSet<XC_MethodHook>> sHookedMethodCallbacks = new HashMap();
    private static HookProvider hookProvider = HookProvider.PINE;

    /* loaded from: assets/pine_xposed_api.dex */
    public interface HookProvider {
        public static final HookProvider PINE = new HookProvider() { // from class: de.robv.android.xposed.XposedBridge.HookProvider.1
            @Override // de.robv.android.xposed.XposedBridge.HookProvider
            public void hook(Member member, CopyOnWriteSortedSet<XC_MethodHook> copyOnWriteSortedSet) {
                Pine.hook(member, new Handler(copyOnWriteSortedSet));
            }

            @Override // de.robv.android.xposed.XposedBridge.HookProvider
            public Object invokeOriginal(Member member, Object obj, Object[] objArr) throws NullPointerException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
                return Pine.invokeOriginalMethod(member, obj, objArr);
            }
        };

        void hook(Member member, CopyOnWriteSortedSet<XC_MethodHook> copyOnWriteSortedSet);

        Object invokeOriginal(Member member, Object obj, Object[] objArr) throws NullPointerException, IllegalAccessException, IllegalArgumentException, InvocationTargetException;
    }

    public static HookProvider getHookProvider() {
        return hookProvider;
    }

    public static String[] getSupportedFeatures() {
        return sSupportedFeatures;
    }

    public static int getXposedVersion() {
        return XPOSED_BRIDGE_VERSION;
    }

    public static void setHookProvider(HookProvider hookProvider2) {
        hookProvider = hookProvider2;
    }

    public static void setSupportedFeatures(String[] strArr) {
        sSupportedFeatures = strArr;
    }

    public static void setXposedVersion(int i) {
        XPOSED_BRIDGE_VERSION = i;
    }

    private XposedBridge() {
    }

    public static boolean isFeatureSupported(String str) {
        for (String str2 : sSupportedFeatures) {
            if (str2.equalsIgnoreCase(str)) {
                return true;
            }
        }
        return false;
    }

    public static synchronized void log(String str) {
        synchronized (XposedBridge.class) {
            Log.i("PineXposed", str);
        }
    }

    public static synchronized void log(Throwable th) {
        synchronized (XposedBridge.class) {
            Log.e("PineXposed", Log.getStackTraceString(th));
        }
    }

    public static void deoptimizeMethod(Member member) {
        Pine.decompile(member, true);
    }

    public static XC_MethodHook.Unhook hookMethod(Member member, XC_MethodHook xC_MethodHook) {
        CopyOnWriteSortedSet<XC_MethodHook> copyOnWriteSortedSet;
        boolean z;
        if (!(member instanceof Method) && !(member instanceof Constructor)) {
            throw new IllegalArgumentException("Only methods and constructors can be hooked: " + member.toString());
        }
        if (Modifier.isAbstract(member.getModifiers())) {
            throw new IllegalArgumentException("Cannot hook abstract methods: " + member.toString());
        }
        Map<Member, CopyOnWriteSortedSet<XC_MethodHook>> map = sHookedMethodCallbacks;
        synchronized (map) {
            copyOnWriteSortedSet = map.get(member);
            if (copyOnWriteSortedSet == null) {
                copyOnWriteSortedSet = new CopyOnWriteSortedSet<>();
                map.put(member, copyOnWriteSortedSet);
                z = true;
            } else {
                z = false;
            }
        }
        copyOnWriteSortedSet.add(xC_MethodHook);
        if (z) {
            hookProvider.hook(member, copyOnWriteSortedSet);
        }
        Objects.requireNonNull(xC_MethodHook);
        return new XC_MethodHook.Unhook(xC_MethodHook, member);
    }

    @Deprecated
    public static void unhookMethod(Member member, XC_MethodHook xC_MethodHook) {
        Map<Member, CopyOnWriteSortedSet<XC_MethodHook>> map = sHookedMethodCallbacks;
        synchronized (map) {
            CopyOnWriteSortedSet<XC_MethodHook> copyOnWriteSortedSet = map.get(member);
            if (copyOnWriteSortedSet == null) {
                return;
            }
            copyOnWriteSortedSet.remove(xC_MethodHook);
        }
    }

    public static Set<XC_MethodHook.Unhook> hookAllMethods(Class<?> cls, String str, XC_MethodHook xC_MethodHook) {
        HashSet hashSet = new HashSet();
        for (Method method : cls.getDeclaredMethods()) {
            if (method.getName().equals(str)) {
                hashSet.add(hookMethod(method, xC_MethodHook));
            }
        }
        return hashSet;
    }

    public static Set<XC_MethodHook.Unhook> hookAllConstructors(Class<?> cls, XC_MethodHook xC_MethodHook) {
        HashSet hashSet = new HashSet();
        for (Constructor<?> constructor : cls.getDeclaredConstructors()) {
            hashSet.add(hookMethod(constructor, xC_MethodHook));
        }
        return hashSet;
    }

    public static Object invokeOriginalMethod(Member member, Object obj, Object[] objArr) throws NullPointerException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
        return hookProvider.invokeOriginal(member, obj, objArr);
    }

    /* loaded from: assets/pine_xposed_api.dex */
    static final class Handler extends MethodHook {
        private final CopyOnWriteSortedSet<XC_MethodHook> callbacks;
        private final ThreadLocal<ExtData> extDataHolder = new ThreadLocal<>();

        Handler(CopyOnWriteSortedSet<XC_MethodHook> copyOnWriteSortedSet) {
            this.callbacks = copyOnWriteSortedSet;
        }

        public void beforeCall(Pine.CallFrame callFrame) {
            Object[] snapshot;
            int length;
            if (PineXposed.disableHooks || (length = (snapshot = this.callbacks.getSnapshot()).length) == 0) {
                return;
            }
            ExtData extData = this.extDataHolder.get();
            if (extData == null) {
                extData = new ExtData();
                this.extDataHolder.set(extData);
            }
            XC_MethodHook.MethodHookParam methodHookParam = new XC_MethodHook.MethodHookParam();
            methodHookParam.method = callFrame.method;
            methodHookParam.thisObject = callFrame.thisObject;
            methodHookParam.args = callFrame.args;
            int i = 0;
            while (true) {
                try {
                    ((XC_MethodHook) snapshot[i]).beforeHookedMethod(methodHookParam);
                    if (methodHookParam.returnEarly) {
                        i++;
                        break;
                    }
                } catch (Throwable th) {
                    XposedBridge.log(th);
                    methodHookParam.setResult(null);
                    methodHookParam.returnEarly = false;
                }
                i++;
                if (i >= length) {
                    break;
                }
            }
            callFrame.thisObject = methodHookParam.thisObject;
            callFrame.args = methodHookParam.args;
            if (methodHookParam.returnEarly) {
                if (methodHookParam.hasThrowable()) {
                    callFrame.setThrowable(methodHookParam.getThrowable());
                } else {
                    callFrame.setResult(methodHookParam.getResult());
                }
            }
            extData.callbacks = snapshot;
            extData.param = methodHookParam;
            extData.afterIdx = i - 1;
        }

        public void afterCall(Pine.CallFrame callFrame) {
            Object[] objArr;
            ExtData extData = this.extDataHolder.get();
            if (extData == null || (objArr = extData.callbacks) == null) {
                return;
            }
            XC_MethodHook.MethodHookParam methodHookParam = extData.param;
            int i = extData.afterIdx;
            methodHookParam.thisObject = callFrame.thisObject;
            methodHookParam.args = callFrame.args;
            if (callFrame.hasThrowable()) {
                methodHookParam.setThrowable(callFrame.getThrowable());
            } else {
                methodHookParam.setResult(callFrame.getResult());
            }
            do {
                Object result = methodHookParam.getResult();
                Throwable throwable = methodHookParam.getThrowable();
                try {
                    ((XC_MethodHook) objArr[i]).afterHookedMethod(methodHookParam);
                } catch (Throwable th) {
                    XposedBridge.log(th);
                    if (throwable == null) {
                        methodHookParam.setResult(result);
                    } else {
                        methodHookParam.setThrowable(throwable);
                    }
                }
                i--;
            } while (i >= 0);
            callFrame.thisObject = methodHookParam.thisObject;
            callFrame.args = methodHookParam.args;
            if (methodHookParam.hasThrowable()) {
                callFrame.setThrowable(methodHookParam.getThrowable());
            } else {
                callFrame.setResult(methodHookParam.getResult());
            }
            extData.callbacks = null;
            extData.param = null;
            extData.afterIdx = 0;
        }

        /* loaded from: assets/pine_xposed_api.dex */
        static final class ExtData {
            int afterIdx;
            Object[] callbacks;
            XC_MethodHook.MethodHookParam param;

            ExtData() {
            }
        }
    }

    /* loaded from: assets/aliu_xposed_api.dex */
    public static class HookInfo {
        Member backup;
        final CopyOnWriteSortedSet<XC_MethodHook> callbacks = new CopyOnWriteSortedSet<>();
        private final boolean isStatic;
        private final Member method;
        private final Class<?> returnType;

        public HookInfo(Member member) {
            this.method = member;
            this.isStatic = Modifier.isStatic(member.getModifiers());
            if (member instanceof Method) {
                Class<?> returnType = ((Method) member).getReturnType();
                if (!returnType.isPrimitive()) {
                    this.returnType = returnType;
                    return;
                }
            }
            this.returnType = null;
        }

        public Object callback(Object[] objArr) throws Throwable {
            XC_MethodHook.MethodHookParam methodHookParam = new XC_MethodHook.MethodHookParam();
            methodHookParam.method = this.method;
            if (this.isStatic) {
                methodHookParam.thisObject = null;
                methodHookParam.args = objArr;
            } else {
                methodHookParam.thisObject = objArr[0];
                methodHookParam.args = new Object[objArr.length - 1];
                System.arraycopy(objArr, 1, methodHookParam.args, 0, objArr.length - 1);
            }
            Object[] snapshot = this.callbacks.getSnapshot();
            int length = snapshot.length;
            if (length == 0) {
                try {
                    return XposedBridge.-$$Nest$sminvokeMethod(this.backup, methodHookParam.thisObject, methodHookParam.args);
                } catch (InvocationTargetException e) {
                    throw e.getCause();
                }
            }
            int i = 0;
            while (true) {
                try {
                    ((XC_MethodHook) snapshot[i]).beforeHookedMethod(methodHookParam);
                    if (methodHookParam.returnEarly) {
                        i++;
                        break;
                    }
                } catch (Throwable th) {
                    XposedBridge.log(th);
                    methodHookParam.setResult(null);
                    methodHookParam.returnEarly = false;
                }
                i++;
                if (i >= length) {
                    break;
                }
            }
            if (!methodHookParam.returnEarly) {
                try {
                    methodHookParam.setResult(XposedBridge.-$$Nest$sminvokeMethod(this.backup, methodHookParam.thisObject, methodHookParam.args));
                } catch (InvocationTargetException e2) {
                    methodHookParam.setThrowable(e2.getCause());
                }
            }
            int i2 = i - 1;
            do {
                Object result = methodHookParam.getResult();
                Throwable throwable = methodHookParam.getThrowable();
                try {
                    ((XC_MethodHook) snapshot[i2]).afterHookedMethod(methodHookParam);
                } catch (Throwable th2) {
                    XposedBridge.log(th2);
                    if (throwable == null) {
                        methodHookParam.setResult(result);
                    } else {
                        methodHookParam.setThrowable(throwable);
                    }
                }
                i2--;
            } while (i2 >= 0);
            Object resultOrThrowable = methodHookParam.getResultOrThrowable();
            Class<?> cls = this.returnType;
            return cls != null ? cls.cast(resultOrThrowable) : resultOrThrowable;
        }
    }

    /* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
    /* loaded from: assets/pine_xposed_api.dex */
    public static final class CopyOnWriteSortedSet<E> {
        private volatile transient Object[] elements = Pine.EMPTY_OBJECT_ARRAY;

        public Object[] getSnapshot() {
            return this.elements;
        }

        public synchronized boolean add(E e) {
            if (indexOf(e) >= 0) {
                return false;
            }
            Object[] objArr = new Object[this.elements.length + 1];
            System.arraycopy(this.elements, 0, objArr, 0, this.elements.length);
            objArr[this.elements.length] = e;
            Arrays.sort(objArr);
            this.elements = objArr;
            return true;
        }

        public synchronized boolean remove(E e) {
            int indexOf = indexOf(e);
            if (indexOf == -1) {
                return false;
            }
            Object[] objArr = new Object[this.elements.length - 1];
            System.arraycopy(this.elements, 0, objArr, 0, indexOf);
            System.arraycopy(this.elements, indexOf + 1, objArr, indexOf, (this.elements.length - indexOf) - 1);
            this.elements = objArr;
            return true;
        }

        private int indexOf(Object obj) {
            for (int i = 0; i < this.elements.length; i++) {
                if (obj.equals(this.elements[i])) {
                    return i;
                }
            }
            return -1;
        }
    }
}
