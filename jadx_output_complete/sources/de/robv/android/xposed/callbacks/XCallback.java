package de.robv.android.xposed.callbacks;

import android.os.Bundle;
import de.robv.android.xposed.XposedBridge;
import java.io.Serializable;

/* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
/* loaded from: assets/pine_xposed_api.dex */
public abstract class XCallback implements Comparable<XCallback> {
    public static final int PRIORITY_DEFAULT = 50;
    public static final int PRIORITY_HIGHEST = 10000;
    public static final int PRIORITY_LOWEST = -10000;
    public final int priority;

    protected void call(Param param) throws Throwable {
    }

    @Deprecated
    public XCallback() {
        this.priority = 50;
    }

    public XCallback(int i) {
        this.priority = i;
    }

    /* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
    /* loaded from: assets/pine_xposed_api.dex */
    public static abstract class Param {
        public final Object[] callbacks;
        private Bundle extra;

        /* JADX INFO: Access modifiers changed from: protected */
        @Deprecated
        public Param() {
            this.callbacks = null;
        }

        /* JADX INFO: Access modifiers changed from: protected */
        public Param(XposedBridge.CopyOnWriteSortedSet<? extends XCallback> copyOnWriteSortedSet) {
            this.callbacks = copyOnWriteSortedSet.getSnapshot();
        }

        public synchronized Bundle getExtra() {
            if (this.extra == null) {
                this.extra = new Bundle();
            }
            return this.extra;
        }

        public Object getObjectExtra(String str) {
            Serializable serializable = getExtra().getSerializable(str);
            if (serializable instanceof SerializeWrapper) {
                return ((SerializeWrapper) serializable).object;
            }
            return null;
        }

        public void setObjectExtra(String str, Object obj) {
            getExtra().putSerializable(str, new SerializeWrapper(obj));
        }

        /* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
        /* loaded from: assets/pine_xposed_api.dex */
        private static class SerializeWrapper implements Serializable {
            private static final long serialVersionUID = 1;
            private final Object object;

            public SerializeWrapper(Object obj) {
                this.object = obj;
            }
        }
    }

    public static void callAll(Param param) {
        if (param.callbacks == null) {
            throw new IllegalStateException("This object was not created for use with callAll");
        }
        for (int i = 0; i < param.callbacks.length; i++) {
            try {
                ((XCallback) param.callbacks[i]).call(param);
            } catch (Throwable th) {
                XposedBridge.log(th);
            }
        }
    }

    @Override // java.lang.Comparable
    public int compareTo(XCallback xCallback) {
        if (this == xCallback) {
            return 0;
        }
        int i = xCallback.priority;
        int i2 = this.priority;
        return i != i2 ? i - i2 : System.identityHashCode(this) < System.identityHashCode(xCallback) ? -1 : 1;
    }
}
