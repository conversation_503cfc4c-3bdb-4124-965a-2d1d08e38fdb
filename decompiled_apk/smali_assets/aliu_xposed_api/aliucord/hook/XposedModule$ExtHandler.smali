.class public interface abstract Laliucord/hook/XposedModule$ExtHandler;
.super Ljava/lang/Object;
.source "XposedModule.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Laliucord/hook/XposedModule;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ExtHandler"
.end annotation


# virtual methods
.method public abstract handle(Lde/robv/android/xposed/IXposedMod;)V
.end method
