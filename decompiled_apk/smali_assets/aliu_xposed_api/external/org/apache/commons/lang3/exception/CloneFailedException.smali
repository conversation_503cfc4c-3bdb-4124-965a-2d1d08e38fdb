.class public Lexternal/org/apache/commons/lang3/exception/CloneFailedException;
.super Ljava/lang/RuntimeException;
.source "CloneFailedException.java"


# static fields
.field private static final serialVersionUID:J = 0x1329157L


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 39
    invoke-direct {p0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    .line 60
    invoke-direct {p0, p1, p2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Throwable;)V
    .locals 0

    .line 49
    invoke-direct {p0, p1}, Ljava/lang/RuntimeException;-><init>(<PERSON><PERSON><PERSON>/lang/Throwable;)V

    return-void
.end method
