.class final Lde/robv/android/xposed/XposedBridge$Handler;
.super Ltop/canyie/pine/callback/MethodHook;
.source "XposedBridge.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lde/robv/android/xposed/XposedBridge;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = "Handler"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lde/robv/android/xposed/XposedBridge$Handler$ExtData;
    }
.end annotation


# instance fields
.field private final callbacks:Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet<",
            "Lde/robv/android/xposed/XC_MethodHook;",
            ">;"
        }
    .end annotation
.end field

.field private final extDataHolder:Ljava/lang/ThreadLocal;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ThreadLocal<",
            "Lde/robv/android/xposed/XposedBridge$Handler$ExtData;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet<",
            "Lde/robv/android/xposed/XC_MethodHook;",
            ">;)V"
        }
    .end annotation

    .line 285
    invoke-direct {p0}, Ltop/canyie/pine/callback/MethodHook;-><init>()V

    .line 283
    new-instance v0, Ljava/lang/ThreadLocal;

    invoke-direct {v0}, Ljava/lang/ThreadLocal;-><init>()V

    iput-object v0, p0, Lde/robv/android/xposed/XposedBridge$Handler;->extDataHolder:Ljava/lang/ThreadLocal;

    iput-object p1, p0, Lde/robv/android/xposed/XposedBridge$Handler;->callbacks:Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;

    return-void
.end method


# virtual methods
.method public afterCall(Ltop/canyie/pine/Pine$CallFrame;)V
    .locals 7

    iget-object v0, p0, Lde/robv/android/xposed/XposedBridge$Handler;->extDataHolder:Ljava/lang/ThreadLocal;

    .line 345
    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;

    if-nez v0, :cond_0

    return-void

    .line 348
    :cond_0
    iget-object v1, v0, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->callbacks:[Ljava/lang/Object;

    if-nez v1, :cond_1

    return-void

    .line 350
    :cond_1
    iget-object v2, v0, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->param:Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;

    .line 351
    iget v3, v0, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->afterIdx:I

    .line 354
    iget-object v4, p1, Ltop/canyie/pine/Pine$CallFrame;->thisObject:Ljava/lang/Object;

    iput-object v4, v2, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->thisObject:Ljava/lang/Object;

    .line 355
    iget-object v4, p1, Ltop/canyie/pine/Pine$CallFrame;->args:[Ljava/lang/Object;

    iput-object v4, v2, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->args:[Ljava/lang/Object;

    .line 356
    invoke-virtual {p1}, Ltop/canyie/pine/Pine$CallFrame;->hasThrowable()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 357
    invoke-virtual {p1}, Ltop/canyie/pine/Pine$CallFrame;->getThrowable()Ljava/lang/Throwable;

    move-result-object v4

    invoke-virtual {v2, v4}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->setThrowable(Ljava/lang/Throwable;)V

    goto :goto_0

    .line 359
    :cond_2
    invoke-virtual {p1}, Ltop/canyie/pine/Pine$CallFrame;->getResult()Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {v2, v4}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->setResult(Ljava/lang/Object;)V

    .line 363
    :cond_3
    :goto_0
    invoke-virtual {v2}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->getResult()Ljava/lang/Object;

    move-result-object v4

    .line 364
    invoke-virtual {v2}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->getThrowable()Ljava/lang/Throwable;

    move-result-object v5

    .line 367
    :try_start_0
    aget-object v6, v1, v3

    check-cast v6, Lde/robv/android/xposed/XC_MethodHook;

    invoke-virtual {v6, v2}, Lde/robv/android/xposed/XC_MethodHook;->afterHookedMethod(Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v6

    .line 369
    invoke-static {v6}, Lde/robv/android/xposed/XposedBridge;->log(Ljava/lang/Throwable;)V

    if-nez v5, :cond_4

    .line 373
    invoke-virtual {v2, v4}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->setResult(Ljava/lang/Object;)V

    goto :goto_1

    .line 375
    :cond_4
    invoke-virtual {v2, v5}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->setThrowable(Ljava/lang/Throwable;)V

    :goto_1
    add-int/lit8 v3, v3, -0x1

    if-gez v3, :cond_3

    .line 380
    iget-object v1, v2, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->thisObject:Ljava/lang/Object;

    iput-object v1, p1, Ltop/canyie/pine/Pine$CallFrame;->thisObject:Ljava/lang/Object;

    .line 381
    iget-object v1, v2, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->args:[Ljava/lang/Object;

    iput-object v1, p1, Ltop/canyie/pine/Pine$CallFrame;->args:[Ljava/lang/Object;

    .line 383
    invoke-virtual {v2}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->hasThrowable()Z

    move-result v1

    if-eqz v1, :cond_5

    .line 384
    invoke-virtual {v2}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->getThrowable()Ljava/lang/Throwable;

    move-result-object v1

    invoke-virtual {p1, v1}, Ltop/canyie/pine/Pine$CallFrame;->setThrowable(Ljava/lang/Throwable;)V

    goto :goto_2

    .line 386
    :cond_5
    invoke-virtual {v2}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->getResult()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {p1, v1}, Ltop/canyie/pine/Pine$CallFrame;->setResult(Ljava/lang/Object;)V

    :goto_2
    const/4 p1, 0x0

    .line 389
    iput-object p1, v0, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->callbacks:[Ljava/lang/Object;

    .line 390
    iput-object p1, v0, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->param:Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;

    const/4 p1, 0x0

    .line 391
    iput p1, v0, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->afterIdx:I

    return-void
.end method

.method public beforeCall(Ltop/canyie/pine/Pine$CallFrame;)V
    .locals 7

    .line 291
    sget-boolean v0, Ltop/canyie/pine/xposed/PineXposed;->disableHooks:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lde/robv/android/xposed/XposedBridge$Handler;->callbacks:Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;

    .line 293
    invoke-virtual {v0}, Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;->getSnapshot()[Ljava/lang/Object;

    move-result-object v0

    .line 294
    array-length v1, v0

    if-nez v1, :cond_1

    return-void

    :cond_1
    iget-object v2, p0, Lde/robv/android/xposed/XposedBridge$Handler;->extDataHolder:Ljava/lang/ThreadLocal;

    .line 297
    invoke-virtual {v2}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;

    if-nez v2, :cond_2

    .line 299
    new-instance v2, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;

    invoke-direct {v2}, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;-><init>()V

    iget-object v3, p0, Lde/robv/android/xposed/XposedBridge$Handler;->extDataHolder:Ljava/lang/ThreadLocal;

    .line 300
    invoke-virtual {v3, v2}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    .line 303
    :cond_2
    new-instance v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;

    invoke-direct {v3}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;-><init>()V

    .line 304
    iget-object v4, p1, Ltop/canyie/pine/Pine$CallFrame;->method:Ljava/lang/reflect/Member;

    iput-object v4, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->method:Ljava/lang/reflect/Member;

    .line 305
    iget-object v4, p1, Ltop/canyie/pine/Pine$CallFrame;->thisObject:Ljava/lang/Object;

    iput-object v4, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->thisObject:Ljava/lang/Object;

    .line 306
    iget-object v4, p1, Ltop/canyie/pine/Pine$CallFrame;->args:[Ljava/lang/Object;

    iput-object v4, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->args:[Ljava/lang/Object;

    const/4 v4, 0x0

    const/4 v5, 0x0

    .line 312
    :cond_3
    :try_start_0
    aget-object v6, v0, v5

    check-cast v6, Lde/robv/android/xposed/XC_MethodHook;

    invoke-virtual {v6, v3}, Lde/robv/android/xposed/XC_MethodHook;->beforeHookedMethod(Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 322
    iget-boolean v6, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->returnEarly:Z

    if-eqz v6, :cond_4

    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    :catchall_0
    move-exception v6

    .line 314
    invoke-static {v6}, Lde/robv/android/xposed/XposedBridge;->log(Ljava/lang/Throwable;)V

    const/4 v6, 0x0

    .line 317
    invoke-virtual {v3, v6}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->setResult(Ljava/lang/Object;)V

    .line 318
    iput-boolean v4, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->returnEarly:Z

    :cond_4
    add-int/lit8 v5, v5, 0x1

    if-lt v5, v1, :cond_3

    .line 330
    :goto_0
    iget-object v1, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->thisObject:Ljava/lang/Object;

    iput-object v1, p1, Ltop/canyie/pine/Pine$CallFrame;->thisObject:Ljava/lang/Object;

    .line 331
    iget-object v1, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->args:[Ljava/lang/Object;

    iput-object v1, p1, Ltop/canyie/pine/Pine$CallFrame;->args:[Ljava/lang/Object;

    .line 332
    iget-boolean v1, v3, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->returnEarly:Z

    if-eqz v1, :cond_6

    .line 333
    invoke-virtual {v3}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->hasThrowable()Z

    move-result v1

    if-eqz v1, :cond_5

    .line 334
    invoke-virtual {v3}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->getThrowable()Ljava/lang/Throwable;

    move-result-object v1

    invoke-virtual {p1, v1}, Ltop/canyie/pine/Pine$CallFrame;->setThrowable(Ljava/lang/Throwable;)V

    goto :goto_1

    .line 336
    :cond_5
    invoke-virtual {v3}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->getResult()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {p1, v1}, Ltop/canyie/pine/Pine$CallFrame;->setResult(Ljava/lang/Object;)V

    .line 339
    :cond_6
    :goto_1
    iput-object v0, v2, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->callbacks:[Ljava/lang/Object;

    .line 340
    iput-object v3, v2, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->param:Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;

    add-int/lit8 v5, v5, -0x1

    .line 341
    iput v5, v2, Lde/robv/android/xposed/XposedBridge$Handler$ExtData;->afterIdx:I

    return-void
.end method
