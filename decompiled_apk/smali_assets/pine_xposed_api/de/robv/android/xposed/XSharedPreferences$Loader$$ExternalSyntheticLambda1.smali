.class public final synthetic Lde/robv/android/xposed/XSharedPreferences$Loader$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lde/robv/android/xposed/XSharedPreferences$Loader;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run(Lde/robv/android/xposed/XSharedPreferences;Ljava/lang/Runnable;)V
    .locals 0

    invoke-static {p1, p2}, Lde/robv/android/xposed/XSharedPreferences$Loader$-CC;->lambda$static$1(Lde/robv/android/xposed/XSharedPreferences;Ljava/lang/Runnable;)V

    return-void
.end method
