.class public interface abstract Lde/robv/android/xposed/XposedBridge$HookProvider;
.super Ljava/lang/Object;
.source "XposedBridge.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lde/robv/android/xposed/XposedBridge;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "HookProvider"
.end annotation


# static fields
.field public static final PINE:Lde/robv/android/xposed/XposedBridge$HookProvider;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 57
    new-instance v0, Lde/robv/android/xposed/XposedBridge$HookProvider$1;

    invoke-direct {v0}, Lde/robv/android/xposed/XposedBridge$HookProvider$1;-><init>()V

    sput-object v0, Lde/robv/android/xposed/XposedBridge$HookProvider;->PINE:Lde/robv/android/xposed/XposedBridge$HookProvider;

    return-void
.end method


# virtual methods
.method public abstract hook(Ljava/lang/reflect/Member;Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/reflect/Member;",
            "Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet<",
            "Lde/robv/android/xposed/XC_MethodHook;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract invokeOriginal(Ljava/lang/reflect/Member;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/NullPointerException;,
            Ljava/lang/IllegalAccessException;,
            Ljava/lang/IllegalArgumentException;,
            Ljava/lang/reflect/InvocationTargetException;
        }
    .end annotation
.end method
