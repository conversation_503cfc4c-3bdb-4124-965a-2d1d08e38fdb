.class public final synthetic Lde/robv/android/xposed/XSharedPreferences$Loader$-CC;
.super Ljava/lang/Object;
.source "XSharedPreferences.java"


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Lde/robv/android/xposed/XSharedPreferences$Loader;->SYNC:Lde/robv/android/xposed/XSharedPreferences$Loader;

    return-void
.end method

.method public static synthetic lambda$static$0(Lde/robv/android/xposed/XSharedPreferences;Ljava/lang/Runnable;)V
    .locals 0

    .line 44
    invoke-static {}, Landroid/os/StrictMode;->allowThreadDiskReads()Landroid/os/StrictMode$ThreadPolicy;

    move-result-object p0

    .line 46
    :try_start_0
    invoke-interface {p1}, Ljava/lang/Runnable;->run()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 48
    invoke-static {p0}, Landroid/os/StrictMode;->setThreadPolicy(Landroid/os/StrictMode$ThreadPolicy;)V

    return-void

    :catchall_0
    move-exception p1

    invoke-static {p0}, Landroid/os/StrictMode;->setThreadPolicy(Landroid/os/StrictMode$ThreadPolicy;)V

    .line 49
    throw p1
.end method

.method public static synthetic lambda$static$1(Lde/robv/android/xposed/XSharedPreferences;Ljava/lang/Runnable;)V
    .locals 2

    .line 51
    new-instance v0, Lde/robv/android/xposed/XSharedPreferences$Loader$1;

    const-string v1, "XSharedPreferences-load"

    invoke-direct {v0, v1, p0, p1}, Lde/robv/android/xposed/XSharedPreferences$Loader$1;-><init>(Ljava/lang/String;Lde/robv/android/xposed/XSharedPreferences;Ljava/lang/Runnable;)V

    .line 57
    invoke-virtual {v0}, Lde/robv/android/xposed/XSharedPreferences$Loader$1;->start()V

    return-void
.end method
