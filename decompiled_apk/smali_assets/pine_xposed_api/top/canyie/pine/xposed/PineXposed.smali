.class public final Ltop/canyie/pine/xposed/PineXposed;
.super Ljava/lang/Object;
.source "PineXposed.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ltop/canyie/pine/xposed/PineXposed$ExtHandler;
    }
.end annotation


# static fields
.field public static final TAG:Ljava/lang/String; = "PineXposed"

.field public static disableHooks:Z

.field public static disableZygoteInitCallbacks:Z

.field private static sExtHandler:Ltop/canyie/pine/xposed/PineXposed$ExtHandler;

.field private static final sLoadedPackageCallbacks:Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet<",
            "Lde/robv/android/xposed/callbacks/XC_LoadPackage;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 35
    new-instance v0, Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;

    invoke-direct {v0}, Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;-><init>()V

    sput-object v0, Ltop/canyie/pine/xposed/PineXposed;->sLoadedPackageCallbacks:Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 37
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static checkModule(Ljava/lang/ClassLoader;)Z
    .locals 5

    const-string v0, "PineXposed"

    .line 131
    instance-of v1, p0, Ltop/canyie/pine/xposed/ModuleClassLoader;

    const/4 v2, 0x0

    :try_start_0
    const-string v3, "com.android.tools.fd.runtime.BootstrapApplication"

    if-eqz v1, :cond_0

    .line 134
    move-object v4, p0

    check-cast v4, Ltop/canyie/pine/xposed/ModuleClassLoader;

    invoke-virtual {v4, v3}, Ltop/canyie/pine/xposed/ModuleClassLoader;->findClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v3

    goto :goto_0

    :cond_0
    invoke-virtual {p0, v3}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v3

    :goto_0
    if-eqz v3, :cond_1

    const-string v3, "  Cannot load module, please disable \"Instant Run\" in Android Studio."

    .line 136
    invoke-static {v0, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return v2

    :catch_0
    nop

    :cond_1
    if-eqz v1, :cond_2

    .line 145
    :try_start_1
    check-cast p0, Ltop/canyie/pine/xposed/ModuleClassLoader;

    const-class v1, Lde/robv/android/xposed/XposedBridge;

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ltop/canyie/pine/xposed/ModuleClassLoader;->findClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p0
    :try_end_1
    .catch Ljava/lang/ClassNotFoundException; {:try_start_1 .. :try_end_1} :catch_1

    if-eqz p0, :cond_3

    goto :goto_1

    .line 151
    :cond_2
    :try_start_2
    const-class v1, Lde/robv/android/xposed/XposedBridge;

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p0

    const-class v1, Lde/robv/android/xposed/XposedBridge;
    :try_end_2
    .catch Ljava/lang/ClassNotFoundException; {:try_start_2 .. :try_end_2} :catch_2

    if-eq p0, v1, :cond_3

    :goto_1
    const-string p0, "  Cannot load module:"

    .line 159
    invoke-static {v0, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    const-string p0, "  The Xposed API classes are compiled into the module\'s APK."

    .line 160
    invoke-static {v0, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    const-string p0, "  This may cause strange issues and must be fixed by the module developer."

    .line 161
    invoke-static {v0, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    const-string p0, "  For details, see: http://api.xposed.info/using.html"

    .line 162
    invoke-static {v0, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    return v2

    :catch_1
    :cond_3
    const/4 p0, 0x1

    return p0

    :catch_2
    move-exception p0

    const-string v1, "  Cannot load module, XposedBridge is not available on the class loader"

    .line 153
    invoke-static {v0, v1, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    const-string p0, "  Make sure you have set parent of the class loader"

    .line 154
    invoke-static {v0, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    return v2
.end method

.method private static closeQuietly(Ljava/io/Closeable;)V
    .locals 0

    if-eqz p0, :cond_0

    .line 186
    :try_start_0
    invoke-interface {p0}, Ljava/io/Closeable;->close()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_0
    return-void
.end method

.method public static getExtHandler()Ltop/canyie/pine/xposed/PineXposed$ExtHandler;
    .locals 1

    sget-object v0, Ltop/canyie/pine/xposed/PineXposed;->sExtHandler:Ltop/canyie/pine/xposed/PineXposed$ExtHandler;

    return-object v0
.end method

.method public static hookLoadPackage(Lde/robv/android/xposed/IXposedHookLoadPackage;)V
    .locals 2

    sget-object v0, Ltop/canyie/pine/xposed/PineXposed;->sLoadedPackageCallbacks:Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;

    .line 169
    new-instance v1, Lde/robv/android/xposed/IXposedHookLoadPackage$Wrapper;

    invoke-direct {v1, p0}, Lde/robv/android/xposed/IXposedHookLoadPackage$Wrapper;-><init>(Lde/robv/android/xposed/IXposedHookLoadPackage;)V

    invoke-virtual {v0, v1}, Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static loadModule(Ljava/io/File;)V
    .locals 1

    const/4 v0, 0x0

    .line 45
    invoke-static {p0, v0}, Ltop/canyie/pine/xposed/PineXposed;->loadModule(Ljava/io/File;Z)V

    return-void
.end method

.method public static loadModule(Ljava/io/File;Ljava/lang/String;Z)V
    .locals 2

    .line 53
    invoke-virtual {p0}, Ljava/io/File;->exists()Z

    move-result v0

    if-nez v0, :cond_0

    .line 54
    new-instance p1, Ljava/lang/StringBuilder;

    const-string p2, "  File "

    invoke-direct {p1, p2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string p0, " does not exist"

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string p1, "PineXposed"

    invoke-static {p1, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    :cond_0
    const-class v0, Ltop/canyie/pine/xposed/PineXposed;

    .line 57
    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 58
    invoke-virtual {p0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object p0

    .line 59
    new-instance v1, Ltop/canyie/pine/xposed/ModuleClassLoader;

    invoke-direct {v1, p0, p1, v0}, Ltop/canyie/pine/xposed/ModuleClassLoader;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/ClassLoader;)V

    .line 60
    invoke-static {p0, v1, p2}, Ltop/canyie/pine/xposed/PineXposed;->loadOpenedModule(Ljava/lang/String;Ljava/lang/ClassLoader;Z)V

    return-void
.end method

.method public static loadModule(Ljava/io/File;Z)V
    .locals 1

    const/4 v0, 0x0

    .line 49
    invoke-static {p0, v0, p1}, Ltop/canyie/pine/xposed/PineXposed;->loadModule(Ljava/io/File;Ljava/lang/String;Z)V

    return-void
.end method

.method public static loadModule(Ljava/lang/String;)V
    .locals 1

    .line 41
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v0}, Ltop/canyie/pine/xposed/PineXposed;->loadModule(Ljava/io/File;)V

    return-void
.end method

.method public static loadOpenedModule(Ljava/lang/String;Ljava/lang/ClassLoader;Z)V
    .locals 8

    const-string v0, " :"

    const-string v1, "PineXposed"

    const-string v2, "  Failed to load module "

    .line 64
    invoke-static {p1}, Ltop/canyie/pine/xposed/PineXposed;->checkModule(Ljava/lang/ClassLoader;)Z

    move-result v3

    if-nez v3, :cond_0

    return-void

    .line 68
    :cond_0
    :try_start_0
    instance-of v3, p1, Ltop/canyie/pine/xposed/ModuleClassLoader;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1

    const-string v4, "assets/xposed_init"

    if-eqz v3, :cond_2

    .line 70
    :try_start_1
    move-object v3, p1

    check-cast v3, Ltop/canyie/pine/xposed/ModuleClassLoader;

    invoke-virtual {v3, v4}, Ltop/canyie/pine/xposed/ModuleClassLoader;->findResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object v3

    if-eqz v3, :cond_1

    .line 71
    invoke-virtual {v3}, Ljava/net/URL;->openStream()Ljava/io/InputStream;

    move-result-object v3

    goto :goto_0

    :cond_1
    const/4 v3, 0x0

    goto :goto_0

    .line 73
    :cond_2
    invoke-virtual {p1, v4}, Ljava/lang/ClassLoader;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v3

    :goto_0
    if-nez v3, :cond_3

    .line 76
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    const-string p1, "  assets/xposed_init not found in the module APK"

    .line 77
    invoke-static {v1, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_1

    return-void

    .line 86
    :cond_3
    new-instance v4, Ljava/io/BufferedReader;

    new-instance v5, Ljava/io/InputStreamReader;

    invoke-direct {v5, v3}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v4, v5}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 89
    :cond_4
    :goto_1
    :try_start_2
    invoke-virtual {v4}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_9

    .line 90
    invoke-virtual {v3}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v3

    .line 91
    invoke-virtual {v3}, Ljava/lang/String;->isEmpty()Z

    move-result v5

    if-nez v5, :cond_4

    const-string v5, "#"

    invoke-virtual {v3, v5}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v5
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    if-eqz v5, :cond_5

    goto :goto_1

    .line 95
    :cond_5
    :try_start_3
    invoke-virtual {p1, v3}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v5

    .line 97
    const-class v6, Lde/robv/android/xposed/IXposedMod;

    invoke-virtual {v6, v5}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v6

    if-nez v6, :cond_6

    .line 98
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "    Cannot load callback class "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, " in module "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v1, v5}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    const-string v5, "    This class doesn\'t implement any sub-interface of IXposedMod, skipping it"

    .line 99
    invoke-static {v1, v5}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 103
    :cond_6
    invoke-virtual {v5}, Ljava/lang/Class;->newInstance()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lde/robv/android/xposed/IXposedMod;

    .line 105
    instance-of v6, v5, Lde/robv/android/xposed/IXposedHookZygoteInit;

    if-eqz v6, :cond_7

    sget-boolean v6, Ltop/canyie/pine/xposed/PineXposed;->disableZygoteInitCallbacks:Z

    if-nez v6, :cond_7

    .line 106
    new-instance v6, Lde/robv/android/xposed/IXposedHookZygoteInit$StartupParam;

    invoke-direct {v6}, Lde/robv/android/xposed/IXposedHookZygoteInit$StartupParam;-><init>()V

    .line 107
    iput-object p0, v6, Lde/robv/android/xposed/IXposedHookZygoteInit$StartupParam;->modulePath:Ljava/lang/String;

    .line 108
    iput-boolean p2, v6, Lde/robv/android/xposed/IXposedHookZygoteInit$StartupParam;->startsSystemServer:Z

    .line 109
    move-object v7, v5

    check-cast v7, Lde/robv/android/xposed/IXposedHookZygoteInit;

    invoke-interface {v7, v6}, Lde/robv/android/xposed/IXposedHookZygoteInit;->initZygote(Lde/robv/android/xposed/IXposedHookZygoteInit$StartupParam;)V

    .line 112
    :cond_7
    instance-of v6, v5, Lde/robv/android/xposed/IXposedHookLoadPackage;

    if-eqz v6, :cond_8

    .line 113
    move-object v6, v5

    check-cast v6, Lde/robv/android/xposed/IXposedHookLoadPackage;

    invoke-static {v6}, Ltop/canyie/pine/xposed/PineXposed;->hookLoadPackage(Lde/robv/android/xposed/IXposedHookLoadPackage;)V

    :cond_8
    sget-object v6, Ltop/canyie/pine/xposed/PineXposed;->sExtHandler:Ltop/canyie/pine/xposed/PineXposed$ExtHandler;

    if-eqz v6, :cond_4

    .line 117
    invoke-interface {v6, v5}, Ltop/canyie/pine/xposed/PineXposed$ExtHandler;->handle(Lde/robv/android/xposed/IXposedMod;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v5

    .line 119
    :try_start_4
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "    Failed to load class "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, " from module "

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v1, v3, v5}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_0
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    goto/16 :goto_1

    .line 126
    :cond_9
    :goto_2
    invoke-static {v4}, Ltop/canyie/pine/xposed/PineXposed;->closeQuietly(Ljava/io/Closeable;)V

    goto :goto_3

    :catchall_1
    move-exception p0

    goto :goto_4

    :catch_0
    move-exception p1

    .line 123
    :try_start_5
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v1, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    const-string p0, "  Cannot read assets/xposed_init in the module APK"

    .line 124
    invoke-static {v1, p0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    goto :goto_2

    :goto_3
    return-void

    .line 126
    :goto_4
    invoke-static {v4}, Ltop/canyie/pine/xposed/PineXposed;->closeQuietly(Ljava/io/Closeable;)V

    .line 127
    throw p0

    :catch_1
    move-exception p1

    .line 81
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v1, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    const-string p0, "  Cannot open assets/xposed_init in the module APK"

    .line 82
    invoke-static {v1, p0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    return-void
.end method

.method public static onPackageLoad(Ljava/lang/String;Ljava/lang/String;Landroid/content/pm/ApplicationInfo;ZLjava/lang/ClassLoader;)V
    .locals 2

    .line 174
    new-instance v0, Lde/robv/android/xposed/callbacks/XC_LoadPackage$LoadPackageParam;

    sget-object v1, Ltop/canyie/pine/xposed/PineXposed;->sLoadedPackageCallbacks:Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;

    invoke-direct {v0, v1}, Lde/robv/android/xposed/callbacks/XC_LoadPackage$LoadPackageParam;-><init>(Lde/robv/android/xposed/XposedBridge$CopyOnWriteSortedSet;)V

    .line 175
    iput-object p0, v0, Lde/robv/android/xposed/callbacks/XC_LoadPackage$LoadPackageParam;->packageName:Ljava/lang/String;

    .line 176
    iput-object p1, v0, Lde/robv/android/xposed/callbacks/XC_LoadPackage$LoadPackageParam;->processName:Ljava/lang/String;

    .line 177
    iput-object p2, v0, Lde/robv/android/xposed/callbacks/XC_LoadPackage$LoadPackageParam;->appInfo:Landroid/content/pm/ApplicationInfo;

    .line 178
    iput-boolean p3, v0, Lde/robv/android/xposed/callbacks/XC_LoadPackage$LoadPackageParam;->isFirstApplication:Z

    .line 179
    iput-object p4, v0, Lde/robv/android/xposed/callbacks/XC_LoadPackage$LoadPackageParam;->classLoader:Ljava/lang/ClassLoader;

    .line 180
    invoke-static {v0}, Lde/robv/android/xposed/callbacks/XC_LoadPackage;->callAll(Lde/robv/android/xposed/callbacks/XCallback$Param;)V

    return-void
.end method

.method public static setExtHandler(Ltop/canyie/pine/xposed/PineXposed$ExtHandler;)V
    .locals 0

    sput-object p0, Ltop/canyie/pine/xposed/PineXposed;->sExtHandler:Ltop/canyie/pine/xposed/PineXposed$ExtHandler;

    return-void
.end method
