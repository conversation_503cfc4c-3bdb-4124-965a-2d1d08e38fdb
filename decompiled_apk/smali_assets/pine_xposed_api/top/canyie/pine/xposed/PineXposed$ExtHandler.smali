.class public interface abstract Ltop/canyie/pine/xposed/PineXposed$ExtHandler;
.super Ljava/lang/Object;
.source "PineXposed.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltop/canyie/pine/xposed/PineXposed;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ExtHandler"
.end annotation


# virtual methods
.method public abstract handle(Lde/robv/android/xposed/IXposedMod;)V
.end method
