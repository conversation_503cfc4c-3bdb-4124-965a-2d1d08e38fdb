.class public Ltop/canyie/pine/xposed/CompoundEnumeration;
.super Ljava/lang/Object;
.source "CompoundEnumeration.java"

# interfaces
.implements Ljava/util/Enumeration;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Enumeration<",
        "TE;>;"
    }
.end annotation


# instance fields
.field private enums:[Ljava/util/Enumeration;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Ljava/util/Enumeration<",
            "TE;>;"
        }
    .end annotation
.end field

.field private index:I


# direct methods
.method public constructor <init>([Ljava/util/Enumeration;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/util/Enumeration<",
            "TE;>;)V"
        }
    .end annotation

    .line 40
    invoke-direct {p0}, Lja<PERSON>/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->index:I

    iput-object p1, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->enums:[Ljava/util/Enumeration;

    return-void
.end method

.method private next()Z
    .locals 3

    :goto_0
    iget v0, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->index:I

    iget-object v1, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->enums:[Ljava/util/Enumeration;

    .line 45
    array-length v2, v1

    if-ge v0, v2, :cond_1

    .line 46
    aget-object v0, v1, v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    iget v0, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->index:I

    add-int/2addr v0, v1

    iput v0, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->index:I

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    return v0
.end method


# virtual methods
.method public hasMoreElements()Z
    .locals 1

    .line 55
    invoke-direct {p0}, Ltop/canyie/pine/xposed/CompoundEnumeration;->next()Z

    move-result v0

    return v0
.end method

.method public nextElement()Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TE;"
        }
    .end annotation

    .line 59
    invoke-direct {p0}, Ltop/canyie/pine/xposed/CompoundEnumeration;->next()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->enums:[Ljava/util/Enumeration;

    iget v1, p0, Ltop/canyie/pine/xposed/CompoundEnumeration;->index:I

    .line 62
    aget-object v0, v0, v1

    invoke-interface {v0}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v0

    return-object v0

    .line 60
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method
