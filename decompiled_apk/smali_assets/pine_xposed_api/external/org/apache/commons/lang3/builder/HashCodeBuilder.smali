.class public Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
.super Ljava/lang/Object;
.source "HashCodeBuilder.java"

# interfaces
.implements Lexternal/org/apache/commons/lang3/builder/Builder;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lexternal/org/apache/commons/lang3/builder/Builder<",
        "Ljava/lang/Integer;",
        ">;"
    }
.end annotation


# static fields
.field private static final REGISTRY:Ljava/lang/ThreadLocal;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ThreadLocal<",
            "Ljava/util/Set<",
            "Lexternal/org/apache/commons/lang3/builder/IDKey;",
            ">;>;"
        }
    .end annotation
.end field


# instance fields
.field private final iConstant:I

.field private iTotal:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 108
    new-instance v0, Ljava/lang/ThreadLocal;

    invoke-direct {v0}, Ljava/lang/ThreadLocal;-><init>()V

    sput-object v0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->REGISTRY:Ljava/lang/ThreadLocal;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 516
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x25

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    const/16 v0, 0x11

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-void
.end method

.method public constructor <init>(II)V
    .locals 1

    .line 538
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    if-eqz p1, :cond_3

    .line 542
    rem-int/lit8 v0, p1, 0x2

    if-eqz v0, :cond_2

    if-eqz p2, :cond_1

    .line 548
    rem-int/lit8 v0, p2, 0x2

    if-eqz v0, :cond_0

    iput p2, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-void

    .line 549
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "HashCodeBuilder requires an odd multiplier"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 546
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "HashCodeBuilder requires a non zero multiplier"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 543
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "HashCodeBuilder requires an odd initial value"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 540
    :cond_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "HashCodeBuilder requires a non zero initial value"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method static getRegistry()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lexternal/org/apache/commons/lang3/builder/IDKey;",
            ">;"
        }
    .end annotation

    sget-object v0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->REGISTRY:Ljava/lang/ThreadLocal;

    .line 136
    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Set;

    return-object v0
.end method

.method static isRegistered(Ljava/lang/Object;)Z
    .locals 2

    .line 151
    invoke-static {}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->getRegistry()Ljava/util/Set;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 152
    new-instance v1, Lexternal/org/apache/commons/lang3/builder/IDKey;

    invoke-direct {v1, p0}, Lexternal/org/apache/commons/lang3/builder/IDKey;-><init>(Ljava/lang/Object;)V

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private static reflectionAppend(Ljava/lang/Object;Ljava/lang/Class;Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;Z[Ljava/lang/String;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Class<",
            "*>;",
            "Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;",
            "Z[",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 173
    invoke-static {p0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->isRegistered(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 177
    :cond_0
    :try_start_0
    invoke-static {p0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->register(Ljava/lang/Object;)V

    .line 178
    invoke-virtual {p1}, Ljava/lang/Class;->getDeclaredFields()[Ljava/lang/reflect/Field;

    move-result-object p1

    const/4 v0, 0x1

    .line 179
    invoke-static {p1, v0}, Ljava/lang/reflect/AccessibleObject;->setAccessible([Ljava/lang/reflect/AccessibleObject;Z)V

    .line 180
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_3

    aget-object v2, p1, v1

    .line 181
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {p4, v3}, Lexternal/org/apache/commons/lang3/ArrayUtils;->contains([Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_2

    .line 182
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x24

    invoke-virtual {v3, v4}, Ljava/lang/String;->indexOf(I)I

    move-result v3

    const/4 v4, -0x1

    if-ne v3, v4, :cond_2

    if-nez p3, :cond_1

    .line 183
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->getModifiers()I

    move-result v3

    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isTransient(I)Z

    move-result v3

    if-nez v3, :cond_2

    .line 184
    :cond_1
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->getModifiers()I

    move-result v3

    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v3, :cond_2

    .line 186
    :try_start_1
    invoke-virtual {v2, p0}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 187
    invoke-virtual {p2, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(Ljava/lang/Object;)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    :try_end_1
    .catch Ljava/lang/IllegalAccessException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    .line 191
    :catch_0
    :try_start_2
    new-instance p1, Ljava/lang/InternalError;

    const-string p2, "Unexpected IllegalAccessException"

    invoke-direct {p1, p2}, Ljava/lang/InternalError;-><init>(Ljava/lang/String;)V

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :cond_2
    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 196
    :cond_3
    invoke-static {p0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->unregister(Ljava/lang/Object;)V

    return-void

    :catchall_0
    move-exception p1

    invoke-static {p0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->unregister(Ljava/lang/Object;)V

    .line 197
    goto :goto_3

    :goto_2
    throw p1

    :goto_3
    goto :goto_2
.end method

.method public static reflectionHashCode(IILjava/lang/Object;)I
    .locals 6

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v0, 0x0

    new-array v5, v0, [Ljava/lang/String;

    move v0, p0

    move v1, p1

    move-object v2, p2

    .line 238
    invoke-static/range {v0 .. v5}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->reflectionHashCode(IILjava/lang/Object;ZLjava/lang/Class;[Ljava/lang/String;)I

    move-result p0

    return p0
.end method

.method public static reflectionHashCode(IILjava/lang/Object;Z)I
    .locals 6

    const/4 v4, 0x0

    const/4 v0, 0x0

    new-array v5, v0, [Ljava/lang/String;

    move v0, p0

    move v1, p1

    move-object v2, p2

    move v3, p3

    .line 282
    invoke-static/range {v0 .. v5}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->reflectionHashCode(IILjava/lang/Object;ZLjava/lang/Class;[Ljava/lang/String;)I

    move-result p0

    return p0
.end method

.method public static varargs reflectionHashCode(IILjava/lang/Object;ZLjava/lang/Class;[Ljava/lang/String;)I
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(IITT;Z",
            "Ljava/lang/Class<",
            "-TT;>;[",
            "Ljava/lang/String;",
            ")I"
        }
    .end annotation

    if-eqz p2, :cond_1

    .line 338
    new-instance v0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    invoke-direct {v0, p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;-><init>(II)V

    .line 339
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    .line 340
    invoke-static {p2, p0, v0, p3, p5}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->reflectionAppend(Ljava/lang/Object;Ljava/lang/Class;Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;Z[Ljava/lang/String;)V

    .line 341
    :goto_0
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p1

    if-eqz p1, :cond_0

    if-eq p0, p4, :cond_0

    .line 342
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    .line 343
    invoke-static {p2, p0, v0, p3, p5}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->reflectionAppend(Ljava/lang/Object;Ljava/lang/Class;Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;Z[Ljava/lang/String;)V

    goto :goto_0

    .line 345
    :cond_0
    invoke-virtual {v0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->toHashCode()I

    move-result p0

    return p0

    .line 336
    :cond_1
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string p1, "The object to build a hash code for must not be null"

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    goto :goto_2

    :goto_1
    throw p0

    :goto_2
    goto :goto_1
.end method

.method public static reflectionHashCode(Ljava/lang/Object;Ljava/util/Collection;)I
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;)I"
        }
    .end annotation

    .line 417
    invoke-static {p1}, Lexternal/org/apache/commons/lang3/builder/ReflectionToStringBuilder;->toNoNullStringArray(Ljava/util/Collection;)[Ljava/lang/String;

    move-result-object p1

    invoke-static {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->reflectionHashCode(Ljava/lang/Object;[Ljava/lang/String;)I

    move-result p0

    return p0
.end method

.method public static reflectionHashCode(Ljava/lang/Object;Z)I
    .locals 6

    const/16 v0, 0x11

    const/16 v1, 0x25

    const/4 v4, 0x0

    const/4 v2, 0x0

    new-array v5, v2, [Ljava/lang/String;

    move-object v2, p0

    move v3, p1

    .line 381
    invoke-static/range {v0 .. v5}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->reflectionHashCode(IILjava/lang/Object;ZLjava/lang/Class;[Ljava/lang/String;)I

    move-result p0

    return p0
.end method

.method public static varargs reflectionHashCode(Ljava/lang/Object;[Ljava/lang/String;)I
    .locals 6

    const/16 v0, 0x11

    const/16 v1, 0x25

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v2, p0

    move-object v5, p1

    .line 455
    invoke-static/range {v0 .. v5}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->reflectionHashCode(IILjava/lang/Object;ZLjava/lang/Class;[Ljava/lang/String;)I

    move-result p0

    return p0
.end method

.method static register(Ljava/lang/Object;)V
    .locals 3

    const-class v0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    .line 467
    monitor-enter v0

    .line 468
    :try_start_0
    invoke-static {}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->getRegistry()Ljava/util/Set;

    move-result-object v1

    if-nez v1, :cond_0

    sget-object v1, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->REGISTRY:Ljava/lang/ThreadLocal;

    .line 469
    new-instance v2, Ljava/util/HashSet;

    invoke-direct {v2}, Ljava/util/HashSet;-><init>()V

    invoke-virtual {v1, v2}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    .line 471
    :cond_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 472
    invoke-static {}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->getRegistry()Ljava/util/Set;

    move-result-object v0

    new-instance v1, Lexternal/org/apache/commons/lang3/builder/IDKey;

    invoke-direct {v1, p0}, Lexternal/org/apache/commons/lang3/builder/IDKey;-><init>(Ljava/lang/Object;)V

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void

    :catchall_0
    move-exception p0

    .line 471
    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p0
.end method

.method static unregister(Ljava/lang/Object;)V
    .locals 2

    .line 488
    invoke-static {}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->getRegistry()Ljava/util/Set;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 490
    new-instance v1, Lexternal/org/apache/commons/lang3/builder/IDKey;

    invoke-direct {v1, p0}, Lexternal/org/apache/commons/lang3/builder/IDKey;-><init>(Ljava/lang/Object;)V

    invoke-interface {v0, v1}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    const-class p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    .line 491
    monitor-enter p0

    .line 493
    :try_start_0
    invoke-static {}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->getRegistry()Ljava/util/Set;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 494
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object v0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->REGISTRY:Ljava/lang/ThreadLocal;

    .line 495
    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->remove()V

    .line 497
    :cond_0
    monitor-exit p0

    goto :goto_0

    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0

    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public append(B)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public append(C)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public append(D)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 0

    .line 683
    invoke-static {p1, p2}, Ljava/lang/Double;->doubleToLongBits(D)J

    move-result-wide p1

    invoke-virtual {p0, p1, p2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(J)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    move-result-object p1

    return-object p1
.end method

.method public append(F)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    .line 716
    invoke-static {p1}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result p1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public append(I)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public append(J)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    const/16 v1, 0x20

    shr-long v1, p1, v1

    xor-long/2addr p1, v1

    long-to-int p2, p1

    add-int/2addr v0, p2

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public append(Ljava/lang/Object;)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto/16 :goto_0

    .line 826
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->isArray()Z

    move-result v0

    if-eqz v0, :cond_9

    .line 829
    instance-of v0, p1, [J

    if-eqz v0, :cond_1

    .line 830
    check-cast p1, [J

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([J)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 831
    :cond_1
    instance-of v0, p1, [I

    if-eqz v0, :cond_2

    .line 832
    check-cast p1, [I

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([I)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 833
    :cond_2
    instance-of v0, p1, [S

    if-eqz v0, :cond_3

    .line 834
    check-cast p1, [S

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([S)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 835
    :cond_3
    instance-of v0, p1, [C

    if-eqz v0, :cond_4

    .line 836
    check-cast p1, [C

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([C)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 837
    :cond_4
    instance-of v0, p1, [B

    if-eqz v0, :cond_5

    .line 838
    check-cast p1, [B

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([B)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 839
    :cond_5
    instance-of v0, p1, [D

    if-eqz v0, :cond_6

    .line 840
    check-cast p1, [D

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([D)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 841
    :cond_6
    instance-of v0, p1, [F

    if-eqz v0, :cond_7

    .line 842
    check-cast p1, [F

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([F)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 843
    :cond_7
    instance-of v0, p1, [Z

    if-eqz v0, :cond_8

    .line 844
    check-cast p1, [Z

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([Z)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    .line 847
    :cond_8
    check-cast p1, [Ljava/lang/Object;

    invoke-virtual {p0, p1}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append([Ljava/lang/Object;)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    goto :goto_0

    :cond_9
    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    .line 850
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result p1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    :goto_0
    return-object p0
.end method

.method public append(S)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public append(Z)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    xor-int/lit8 p1, p1, 0x1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public append([B)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 632
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-byte v2, p1, v1

    .line 633
    invoke-virtual {p0, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(B)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([C)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 666
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-char v2, p1, v1

    .line 667
    invoke-virtual {p0, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(C)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([D)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 4

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 699
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-wide v2, p1, v1

    .line 700
    invoke-virtual {p0, v2, v3}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(D)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([F)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 733
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget v2, p1, v1

    .line 734
    invoke-virtual {p0, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(F)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([I)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 767
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget v2, p1, v1

    .line 768
    invoke-virtual {p0, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(I)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([J)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 4

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 805
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-wide v2, p1, v1

    .line 806
    invoke-virtual {p0, v2, v3}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(J)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([Ljava/lang/Object;)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 869
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v2, p1, v1

    .line 870
    invoke-virtual {p0, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(Ljava/lang/Object;)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([S)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 903
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-short v2, p1, v1

    .line 904
    invoke-virtual {p0, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(S)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public append([Z)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 3

    if-nez p1, :cond_0

    iget p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int p1, p1, v0

    iput p1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    goto :goto_1

    .line 594
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-boolean v2, p1, v1

    .line 595
    invoke-virtual {p0, v2}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->append(Z)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-object p0
.end method

.method public appendSuper(I)Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;
    .locals 2

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    iget v1, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iConstant:I

    mul-int v0, v0, v1

    add-int/2addr v0, p1

    iput v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return-object p0
.end method

.method public build()Ljava/lang/Integer;
    .locals 1

    .line 944
    invoke-virtual {p0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->toHashCode()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic build()Ljava/lang/Object;
    .locals 1

    .line 100
    invoke-virtual {p0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->build()Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public hashCode()I
    .locals 1

    .line 958
    invoke-virtual {p0}, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->toHashCode()I

    move-result v0

    return v0
.end method

.method public toHashCode()I
    .locals 1

    iget v0, p0, Lexternal/org/apache/commons/lang3/builder/HashCodeBuilder;->iTotal:I

    return v0
.end method
