<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="anim_fade_in" id="0x7f01000c" />
    <public type="anim" name="anim_fade_out" id="0x7f01000d" />
    <public type="anim" name="btn_checkbox_to_checked_box_inner_merged_animation" id="0x7f01000e" />
    <public type="anim" name="btn_checkbox_to_checked_box_outer_merged_animation" id="0x7f01000f" />
    <public type="anim" name="btn_checkbox_to_checked_icon_null_animation" id="0x7f010010" />
    <public type="anim" name="btn_checkbox_to_unchecked_box_inner_merged_animation" id="0x7f010011" />
    <public type="anim" name="btn_checkbox_to_unchecked_check_path_merged_animation" id="0x7f010012" />
    <public type="anim" name="btn_checkbox_to_unchecked_icon_null_animation" id="0x7f010013" />
    <public type="anim" name="btn_radio_to_off_mtrl_dot_group_animation" id="0x7f010014" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_animation" id="0x7f010015" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_path_animation" id="0x7f010016" />
    <public type="anim" name="btn_radio_to_on_mtrl_dot_group_animation" id="0x7f010017" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_animation" id="0x7f010018" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_path_animation" id="0x7f010019" />
    <public type="anim" name="design_bottom_sheet_slide_in" id="0x7f01001a" />
    <public type="anim" name="design_bottom_sheet_slide_out" id="0x7f01001b" />
    <public type="anim" name="design_snackbar_in" id="0x7f01001c" />
    <public type="anim" name="design_snackbar_out" id="0x7f01001d" />
    <public type="anim" name="dialog_enter" id="0x7f01001e" />
    <public type="anim" name="dialog_out" id="0x7f01001f" />
    <public type="anim" name="fragment_fast_out_extra_slow_in" id="0x7f010020" />
    <public type="anim" name="mtrl_bottom_sheet_slide_in" id="0x7f010021" />
    <public type="anim" name="mtrl_bottom_sheet_slide_out" id="0x7f010022" />
    <public type="anim" name="mtrl_card_lowers_interpolator" id="0x7f010023" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="fragment_close_enter" id="0x7f020003" />
    <public type="animator" name="fragment_close_exit" id="0x7f020004" />
    <public type="animator" name="fragment_fade_enter" id="0x7f020005" />
    <public type="animator" name="fragment_fade_exit" id="0x7f020006" />
    <public type="animator" name="fragment_open_enter" id="0x7f020007" />
    <public type="animator" name="fragment_open_exit" id="0x7f020008" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f020009" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f02000a" />
    <public type="animator" name="mtrl_card_state_list_anim" id="0x7f02000b" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f02000c" />
    <public type="animator" name="mtrl_extended_fab_change_size_motion_spec" id="0x7f02000d" />
    <public type="animator" name="mtrl_extended_fab_hide_motion_spec" id="0x7f02000e" />
    <public type="animator" name="mtrl_extended_fab_show_motion_spec" id="0x7f02000f" />
    <public type="animator" name="mtrl_extended_fab_state_list_animator" id="0x7f020010" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f020011" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f020012" />
    <public type="animator" name="mtrl_fab_transformation_sheet_collapse_spec" id="0x7f020013" />
    <public type="animator" name="mtrl_fab_transformation_sheet_expand_spec" id="0x7f020014" />
    <public type="array" name="array_joystick_type" id="0x7f030000" />
    <public type="array" name="array_joystick_type_values" id="0x7f030001" />
    <public type="array" name="weeks" id="0x7f030002" />
    <public type="attr" name="SharedValue" id="0x7f040000" />
    <public type="attr" name="SharedValueId" id="0x7f040001" />
    <public type="attr" name="actionBarDivider" id="0x7f040002" />
    <public type="attr" name="actionBarItemBackground" id="0x7f040003" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f040004" />
    <public type="attr" name="actionBarSize" id="0x7f040005" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f040006" />
    <public type="attr" name="actionBarStyle" id="0x7f040007" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f040008" />
    <public type="attr" name="actionBarTabStyle" id="0x7f040009" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f04000a" />
    <public type="attr" name="actionBarTheme" id="0x7f04000b" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f04000c" />
    <public type="attr" name="actionButtonStyle" id="0x7f04000d" />
    <public type="attr" name="actionDropDownStyle" id="0x7f04000e" />
    <public type="attr" name="actionLayout" id="0x7f04000f" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f040010" />
    <public type="attr" name="actionMenuTextColor" id="0x7f040011" />
    <public type="attr" name="actionModeBackground" id="0x7f040012" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f040013" />
    <public type="attr" name="actionModeCloseContentDescription" id="0x7f040014" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f040015" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f040016" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f040017" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f040018" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f040019" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f04001a" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f04001b" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f04001c" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f04001d" />
    <public type="attr" name="actionModeStyle" id="0x7f04001e" />
    <public type="attr" name="actionModeTheme" id="0x7f04001f" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f040020" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f040021" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f040022" />
    <public type="attr" name="actionProviderClass" id="0x7f040023" />
    <public type="attr" name="actionTextColorAlpha" id="0x7f040024" />
    <public type="attr" name="actionViewClass" id="0x7f040025" />
    <public type="attr" name="activityAction" id="0x7f040026" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f040027" />
    <public type="attr" name="activityName" id="0x7f040028" />
    <public type="attr" name="adjustable" id="0x7f040029" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f04002a" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f04002b" />
    <public type="attr" name="alertDialogStyle" id="0x7f04002c" />
    <public type="attr" name="alertDialogTheme" id="0x7f04002d" />
    <public type="attr" name="allowDividerAbove" id="0x7f04002e" />
    <public type="attr" name="allowDividerAfterLastItem" id="0x7f04002f" />
    <public type="attr" name="allowDividerBelow" id="0x7f040030" />
    <public type="attr" name="allowStacking" id="0x7f040031" />
    <public type="attr" name="alpha" id="0x7f040032" />
    <public type="attr" name="alphabeticModifiers" id="0x7f040033" />
    <public type="attr" name="altSrc" id="0x7f040034" />
    <public type="attr" name="alwaysExpand" id="0x7f040035" />
    <public type="attr" name="animateCircleAngleTo" id="0x7f040036" />
    <public type="attr" name="animateRelativeTo" id="0x7f040037" />
    <public type="attr" name="animationMode" id="0x7f040038" />
    <public type="attr" name="appBarLayoutStyle" id="0x7f040039" />
    <public type="attr" name="applyMotionScene" id="0x7f04003a" />
    <public type="attr" name="arcMode" id="0x7f04003b" />
    <public type="attr" name="arrowHeadLength" id="0x7f04003c" />
    <public type="attr" name="arrowShaftLength" id="0x7f04003d" />
    <public type="attr" name="attributeName" id="0x7f04003e" />
    <public type="attr" name="autoCompleteMode" id="0x7f04003f" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f040040" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f040041" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f040042" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f040043" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f040044" />
    <public type="attr" name="autoSizeTextType" id="0x7f040045" />
    <public type="attr" name="autoTransition" id="0x7f040046" />
    <public type="attr" name="background" id="0x7f040047" />
    <public type="attr" name="backgroundColor" id="0x7f040048" />
    <public type="attr" name="backgroundInsetBottom" id="0x7f040049" />
    <public type="attr" name="backgroundInsetEnd" id="0x7f04004a" />
    <public type="attr" name="backgroundInsetStart" id="0x7f04004b" />
    <public type="attr" name="backgroundInsetTop" id="0x7f04004c" />
    <public type="attr" name="backgroundOverlayColorAlpha" id="0x7f04004d" />
    <public type="attr" name="backgroundSplit" id="0x7f04004e" />
    <public type="attr" name="backgroundStacked" id="0x7f04004f" />
    <public type="attr" name="backgroundTint" id="0x7f040050" />
    <public type="attr" name="backgroundTintMode" id="0x7f040051" />
    <public type="attr" name="badgeGravity" id="0x7f040052" />
    <public type="attr" name="badgeStyle" id="0x7f040053" />
    <public type="attr" name="badgeTextColor" id="0x7f040054" />
    <public type="attr" name="barLength" id="0x7f040055" />
    <public type="attr" name="barrierAllowsGoneWidgets" id="0x7f040056" />
    <public type="attr" name="barrierDirection" id="0x7f040057" />
    <public type="attr" name="barrierMargin" id="0x7f040058" />
    <public type="attr" name="behavior_autoHide" id="0x7f040059" />
    <public type="attr" name="behavior_autoShrink" id="0x7f04005a" />
    <public type="attr" name="behavior_draggable" id="0x7f04005b" />
    <public type="attr" name="behavior_expandedOffset" id="0x7f04005c" />
    <public type="attr" name="behavior_fitToContents" id="0x7f04005d" />
    <public type="attr" name="behavior_halfExpandedRatio" id="0x7f04005e" />
    <public type="attr" name="behavior_hideable" id="0x7f04005f" />
    <public type="attr" name="behavior_overlapTop" id="0x7f040060" />
    <public type="attr" name="behavior_peekHeight" id="0x7f040061" />
    <public type="attr" name="behavior_saveFlags" id="0x7f040062" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f040063" />
    <public type="attr" name="blendSrc" id="0x7f040064" />
    <public type="attr" name="borderRound" id="0x7f040065" />
    <public type="attr" name="borderRoundPercent" id="0x7f040066" />
    <public type="attr" name="borderWidth" id="0x7f040067" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f040068" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f040069" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f04006a" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f04006b" />
    <public type="attr" name="bottomSheetStyle" id="0x7f04006c" />
    <public type="attr" name="boxBackgroundColor" id="0x7f04006d" />
    <public type="attr" name="boxBackgroundMode" id="0x7f04006e" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f04006f" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f040070" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f040071" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f040072" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f040073" />
    <public type="attr" name="boxStrokeColor" id="0x7f040074" />
    <public type="attr" name="boxStrokeErrorColor" id="0x7f040075" />
    <public type="attr" name="boxStrokeWidth" id="0x7f040076" />
    <public type="attr" name="boxStrokeWidthFocused" id="0x7f040077" />
    <public type="attr" name="brightness" id="0x7f040078" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f040079" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f04007a" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f04007b" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f04007c" />
    <public type="attr" name="buttonBarStyle" id="0x7f04007d" />
    <public type="attr" name="buttonCompat" id="0x7f04007e" />
    <public type="attr" name="buttonGravity" id="0x7f04007f" />
    <public type="attr" name="buttonIconDimen" id="0x7f040080" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f040081" />
    <public type="attr" name="buttonStyle" id="0x7f040082" />
    <public type="attr" name="buttonStyleSmall" id="0x7f040083" />
    <public type="attr" name="buttonTint" id="0x7f040084" />
    <public type="attr" name="buttonTintMode" id="0x7f040085" />
    <public type="attr" name="cardBackgroundColor" id="0x7f040086" />
    <public type="attr" name="cardCornerRadius" id="0x7f040087" />
    <public type="attr" name="cardElevation" id="0x7f040088" />
    <public type="attr" name="cardForegroundColor" id="0x7f040089" />
    <public type="attr" name="cardMaxElevation" id="0x7f04008a" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f04008b" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f04008c" />
    <public type="attr" name="cardViewStyle" id="0x7f04008d" />
    <public type="attr" name="carousel_backwardTransition" id="0x7f04008e" />
    <public type="attr" name="carousel_emptyViewsBehavior" id="0x7f04008f" />
    <public type="attr" name="carousel_firstView" id="0x7f040090" />
    <public type="attr" name="carousel_forwardTransition" id="0x7f040091" />
    <public type="attr" name="carousel_infinite" id="0x7f040092" />
    <public type="attr" name="carousel_nextState" id="0x7f040093" />
    <public type="attr" name="carousel_previousState" id="0x7f040094" />
    <public type="attr" name="carousel_touchUpMode" id="0x7f040095" />
    <public type="attr" name="carousel_touchUp_dampeningFactor" id="0x7f040096" />
    <public type="attr" name="carousel_touchUp_velocityThreshold" id="0x7f040097" />
    <public type="attr" name="chainUseRtl" id="0x7f040098" />
    <public type="attr" name="checkBoxPreferenceStyle" id="0x7f040099" />
    <public type="attr" name="checkMarkCompat" id="0x7f04009a" />
    <public type="attr" name="checkMarkTint" id="0x7f04009b" />
    <public type="attr" name="checkMarkTintMode" id="0x7f04009c" />
    <public type="attr" name="checkboxStyle" id="0x7f04009d" />
    <public type="attr" name="checkedButton" id="0x7f04009e" />
    <public type="attr" name="checkedChip" id="0x7f04009f" />
    <public type="attr" name="checkedIcon" id="0x7f0400a0" />
    <public type="attr" name="checkedIconEnabled" id="0x7f0400a1" />
    <public type="attr" name="checkedIconTint" id="0x7f0400a2" />
    <public type="attr" name="checkedIconVisible" id="0x7f0400a3" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f0400a4" />
    <public type="attr" name="chipBackgroundColor" id="0x7f0400a5" />
    <public type="attr" name="chipCornerRadius" id="0x7f0400a6" />
    <public type="attr" name="chipEndPadding" id="0x7f0400a7" />
    <public type="attr" name="chipGroupStyle" id="0x7f0400a8" />
    <public type="attr" name="chipIcon" id="0x7f0400a9" />
    <public type="attr" name="chipIconEnabled" id="0x7f0400aa" />
    <public type="attr" name="chipIconSize" id="0x7f0400ab" />
    <public type="attr" name="chipIconTint" id="0x7f0400ac" />
    <public type="attr" name="chipIconVisible" id="0x7f0400ad" />
    <public type="attr" name="chipMinHeight" id="0x7f0400ae" />
    <public type="attr" name="chipMinTouchTargetSize" id="0x7f0400af" />
    <public type="attr" name="chipSpacing" id="0x7f0400b0" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f0400b1" />
    <public type="attr" name="chipSpacingVertical" id="0x7f0400b2" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f0400b3" />
    <public type="attr" name="chipStartPadding" id="0x7f0400b4" />
    <public type="attr" name="chipStrokeColor" id="0x7f0400b5" />
    <public type="attr" name="chipStrokeWidth" id="0x7f0400b6" />
    <public type="attr" name="chipStyle" id="0x7f0400b7" />
    <public type="attr" name="chipSurfaceColor" id="0x7f0400b8" />
    <public type="attr" name="circleRadius" id="0x7f0400b9" />
    <public type="attr" name="circularflow_angles" id="0x7f0400ba" />
    <public type="attr" name="circularflow_defaultAngle" id="0x7f0400bb" />
    <public type="attr" name="circularflow_defaultRadius" id="0x7f0400bc" />
    <public type="attr" name="circularflow_radiusInDP" id="0x7f0400bd" />
    <public type="attr" name="circularflow_viewCenter" id="0x7f0400be" />
    <public type="attr" name="clearTop" id="0x7f0400bf" />
    <public type="attr" name="clearsTag" id="0x7f0400c0" />
    <public type="attr" name="clickAction" id="0x7f0400c1" />
    <public type="attr" name="closeIcon" id="0x7f0400c2" />
    <public type="attr" name="closeIconEnabled" id="0x7f0400c3" />
    <public type="attr" name="closeIconEndPadding" id="0x7f0400c4" />
    <public type="attr" name="closeIconSize" id="0x7f0400c5" />
    <public type="attr" name="closeIconStartPadding" id="0x7f0400c6" />
    <public type="attr" name="closeIconTint" id="0x7f0400c7" />
    <public type="attr" name="closeIconVisible" id="0x7f0400c8" />
    <public type="attr" name="closeItemLayout" id="0x7f0400c9" />
    <public type="attr" name="collapseContentDescription" id="0x7f0400ca" />
    <public type="attr" name="collapseIcon" id="0x7f0400cb" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f0400cc" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f0400cd" />
    <public type="attr" name="color" id="0x7f0400ce" />
    <public type="attr" name="colorAccent" id="0x7f0400cf" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f0400d0" />
    <public type="attr" name="colorButtonNormal" id="0x7f0400d1" />
    <public type="attr" name="colorControlActivated" id="0x7f0400d2" />
    <public type="attr" name="colorControlHighlight" id="0x7f0400d3" />
    <public type="attr" name="colorControlNormal" id="0x7f0400d4" />
    <public type="attr" name="colorError" id="0x7f0400d5" />
    <public type="attr" name="colorOnBackground" id="0x7f0400d6" />
    <public type="attr" name="colorOnError" id="0x7f0400d7" />
    <public type="attr" name="colorOnPrimary" id="0x7f0400d8" />
    <public type="attr" name="colorOnPrimarySurface" id="0x7f0400d9" />
    <public type="attr" name="colorOnSecondary" id="0x7f0400da" />
    <public type="attr" name="colorOnSurface" id="0x7f0400db" />
    <public type="attr" name="colorPrimary" id="0x7f0400dc" />
    <public type="attr" name="colorPrimaryDark" id="0x7f0400dd" />
    <public type="attr" name="colorPrimarySurface" id="0x7f0400de" />
    <public type="attr" name="colorPrimaryVariant" id="0x7f0400df" />
    <public type="attr" name="colorSecondary" id="0x7f0400e0" />
    <public type="attr" name="colorSecondaryVariant" id="0x7f0400e1" />
    <public type="attr" name="colorSurface" id="0x7f0400e2" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f0400e3" />
    <public type="attr" name="commitIcon" id="0x7f0400e4" />
    <public type="attr" name="constraintRotate" id="0x7f0400e5" />
    <public type="attr" name="constraintSet" id="0x7f0400e6" />
    <public type="attr" name="constraintSetEnd" id="0x7f0400e7" />
    <public type="attr" name="constraintSetStart" id="0x7f0400e8" />
    <public type="attr" name="constraint_referenced_ids" id="0x7f0400e9" />
    <public type="attr" name="constraint_referenced_tags" id="0x7f0400ea" />
    <public type="attr" name="constraints" id="0x7f0400eb" />
    <public type="attr" name="content" id="0x7f0400ec" />
    <public type="attr" name="contentDescription" id="0x7f0400ed" />
    <public type="attr" name="contentInsetEnd" id="0x7f0400ee" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f0400ef" />
    <public type="attr" name="contentInsetLeft" id="0x7f0400f0" />
    <public type="attr" name="contentInsetRight" id="0x7f0400f1" />
    <public type="attr" name="contentInsetStart" id="0x7f0400f2" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f0400f3" />
    <public type="attr" name="contentPadding" id="0x7f0400f4" />
    <public type="attr" name="contentPaddingBottom" id="0x7f0400f5" />
    <public type="attr" name="contentPaddingLeft" id="0x7f0400f6" />
    <public type="attr" name="contentPaddingRight" id="0x7f0400f7" />
    <public type="attr" name="contentPaddingTop" id="0x7f0400f8" />
    <public type="attr" name="contentScrim" id="0x7f0400f9" />
    <public type="attr" name="contrast" id="0x7f0400fa" />
    <public type="attr" name="controlBackground" id="0x7f0400fb" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f0400fc" />
    <public type="attr" name="cornerFamily" id="0x7f0400fd" />
    <public type="attr" name="cornerFamilyBottomLeft" id="0x7f0400fe" />
    <public type="attr" name="cornerFamilyBottomRight" id="0x7f0400ff" />
    <public type="attr" name="cornerFamilyTopLeft" id="0x7f040100" />
    <public type="attr" name="cornerFamilyTopRight" id="0x7f040101" />
    <public type="attr" name="cornerRadius" id="0x7f040102" />
    <public type="attr" name="cornerSize" id="0x7f040103" />
    <public type="attr" name="cornerSizeBottomLeft" id="0x7f040104" />
    <public type="attr" name="cornerSizeBottomRight" id="0x7f040105" />
    <public type="attr" name="cornerSizeTopLeft" id="0x7f040106" />
    <public type="attr" name="cornerSizeTopRight" id="0x7f040107" />
    <public type="attr" name="counterEnabled" id="0x7f040108" />
    <public type="attr" name="counterMaxLength" id="0x7f040109" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f04010a" />
    <public type="attr" name="counterOverflowTextColor" id="0x7f04010b" />
    <public type="attr" name="counterTextAppearance" id="0x7f04010c" />
    <public type="attr" name="counterTextColor" id="0x7f04010d" />
    <public type="attr" name="crossfade" id="0x7f04010e" />
    <public type="attr" name="currentState" id="0x7f04010f" />
    <public type="attr" name="curveFit" id="0x7f040110" />
    <public type="attr" name="customBoolean" id="0x7f040111" />
    <public type="attr" name="customColorDrawableValue" id="0x7f040112" />
    <public type="attr" name="customColorValue" id="0x7f040113" />
    <public type="attr" name="customDimension" id="0x7f040114" />
    <public type="attr" name="customFloatValue" id="0x7f040115" />
    <public type="attr" name="customIntegerValue" id="0x7f040116" />
    <public type="attr" name="customNavigationLayout" id="0x7f040117" />
    <public type="attr" name="customPixelDimension" id="0x7f040118" />
    <public type="attr" name="customReference" id="0x7f040119" />
    <public type="attr" name="customStringValue" id="0x7f04011a" />
    <public type="attr" name="dayInvalidStyle" id="0x7f04011b" />
    <public type="attr" name="daySelectedStyle" id="0x7f04011c" />
    <public type="attr" name="dayStyle" id="0x7f04011d" />
    <public type="attr" name="dayTodayStyle" id="0x7f04011e" />
    <public type="attr" name="defaultDuration" id="0x7f04011f" />
    <public type="attr" name="defaultQueryHint" id="0x7f040120" />
    <public type="attr" name="defaultState" id="0x7f040121" />
    <public type="attr" name="defaultValue" id="0x7f040122" />
    <public type="attr" name="deltaPolarAngle" id="0x7f040123" />
    <public type="attr" name="deltaPolarRadius" id="0x7f040124" />
    <public type="attr" name="dependency" id="0x7f040125" />
    <public type="attr" name="deriveConstraintsFrom" id="0x7f040126" />
    <public type="attr" name="dialogCornerRadius" id="0x7f040127" />
    <public type="attr" name="dialogIcon" id="0x7f040128" />
    <public type="attr" name="dialogLayout" id="0x7f040129" />
    <public type="attr" name="dialogMessage" id="0x7f04012a" />
    <public type="attr" name="dialogPreferenceStyle" id="0x7f04012b" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f04012c" />
    <public type="attr" name="dialogTheme" id="0x7f04012d" />
    <public type="attr" name="dialogTitle" id="0x7f04012e" />
    <public type="attr" name="disableDependentsState" id="0x7f04012f" />
    <public type="attr" name="displayOptions" id="0x7f040130" />
    <public type="attr" name="divider" id="0x7f040131" />
    <public type="attr" name="dividerHorizontal" id="0x7f040132" />
    <public type="attr" name="dividerPadding" id="0x7f040133" />
    <public type="attr" name="dividerVertical" id="0x7f040134" />
    <public type="attr" name="dragDirection" id="0x7f040135" />
    <public type="attr" name="dragScale" id="0x7f040136" />
    <public type="attr" name="dragThreshold" id="0x7f040137" />
    <public type="attr" name="drawPath" id="0x7f040138" />
    <public type="attr" name="drawableBottomCompat" id="0x7f040139" />
    <public type="attr" name="drawableEndCompat" id="0x7f04013a" />
    <public type="attr" name="drawableLeftCompat" id="0x7f04013b" />
    <public type="attr" name="drawableRightCompat" id="0x7f04013c" />
    <public type="attr" name="drawableSize" id="0x7f04013d" />
    <public type="attr" name="drawableStartCompat" id="0x7f04013e" />
    <public type="attr" name="drawableTint" id="0x7f04013f" />
    <public type="attr" name="drawableTintMode" id="0x7f040140" />
    <public type="attr" name="drawableTopCompat" id="0x7f040141" />
    <public type="attr" name="drawerArrowStyle" id="0x7f040142" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f040143" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f040144" />
    <public type="attr" name="dropdownPreferenceStyle" id="0x7f040145" />
    <public type="attr" name="duration" id="0x7f040146" />
    <public type="attr" name="editTextBackground" id="0x7f040147" />
    <public type="attr" name="editTextColor" id="0x7f040148" />
    <public type="attr" name="editTextPreferenceStyle" id="0x7f040149" />
    <public type="attr" name="editTextStyle" id="0x7f04014a" />
    <public type="attr" name="elevation" id="0x7f04014b" />
    <public type="attr" name="elevationOverlayColor" id="0x7f04014c" />
    <public type="attr" name="elevationOverlayEnabled" id="0x7f04014d" />
    <public type="attr" name="emojiCompatEnabled" id="0x7f04014e" />
    <public type="attr" name="enableCopying" id="0x7f04014f" />
    <public type="attr" name="enabled" id="0x7f040150" />
    <public type="attr" name="endIconCheckable" id="0x7f040151" />
    <public type="attr" name="endIconContentDescription" id="0x7f040152" />
    <public type="attr" name="endIconDrawable" id="0x7f040153" />
    <public type="attr" name="endIconMode" id="0x7f040154" />
    <public type="attr" name="endIconTint" id="0x7f040155" />
    <public type="attr" name="endIconTintMode" id="0x7f040156" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f040157" />
    <public type="attr" name="enforceTextAppearance" id="0x7f040158" />
    <public type="attr" name="ensureMinTouchTargetSize" id="0x7f040159" />
    <public type="attr" name="entries" id="0x7f04015a" />
    <public type="attr" name="entryValues" id="0x7f04015b" />
    <public type="attr" name="errorContentDescription" id="0x7f04015c" />
    <public type="attr" name="errorEnabled" id="0x7f04015d" />
    <public type="attr" name="errorIconDrawable" id="0x7f04015e" />
    <public type="attr" name="errorIconTint" id="0x7f04015f" />
    <public type="attr" name="errorIconTintMode" id="0x7f040160" />
    <public type="attr" name="errorTextAppearance" id="0x7f040161" />
    <public type="attr" name="errorTextColor" id="0x7f040162" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f040163" />
    <public type="attr" name="expanded" id="0x7f040164" />
    <public type="attr" name="expandedTitleGravity" id="0x7f040165" />
    <public type="attr" name="expandedTitleMargin" id="0x7f040166" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f040167" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f040168" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f040169" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f04016a" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f04016b" />
    <public type="attr" name="extendMotionSpec" id="0x7f04016c" />
    <public type="attr" name="extendedFloatingActionButtonStyle" id="0x7f04016d" />
    <public type="attr" name="fabAlignmentMode" id="0x7f04016e" />
    <public type="attr" name="fabAnimationMode" id="0x7f04016f" />
    <public type="attr" name="fabCradleMargin" id="0x7f040170" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f040171" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f040172" />
    <public type="attr" name="fabCustomSize" id="0x7f040173" />
    <public type="attr" name="fabSize" id="0x7f040174" />
    <public type="attr" name="fastScrollEnabled" id="0x7f040175" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f040176" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f040177" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f040178" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f040179" />
    <public type="attr" name="finishPrimaryWithSecondary" id="0x7f04017a" />
    <public type="attr" name="finishSecondaryWithPrimary" id="0x7f04017b" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f04017c" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f04017d" />
    <public type="attr" name="flow_firstHorizontalBias" id="0x7f04017e" />
    <public type="attr" name="flow_firstHorizontalStyle" id="0x7f04017f" />
    <public type="attr" name="flow_firstVerticalBias" id="0x7f040180" />
    <public type="attr" name="flow_firstVerticalStyle" id="0x7f040181" />
    <public type="attr" name="flow_horizontalAlign" id="0x7f040182" />
    <public type="attr" name="flow_horizontalBias" id="0x7f040183" />
    <public type="attr" name="flow_horizontalGap" id="0x7f040184" />
    <public type="attr" name="flow_horizontalStyle" id="0x7f040185" />
    <public type="attr" name="flow_lastHorizontalBias" id="0x7f040186" />
    <public type="attr" name="flow_lastHorizontalStyle" id="0x7f040187" />
    <public type="attr" name="flow_lastVerticalBias" id="0x7f040188" />
    <public type="attr" name="flow_lastVerticalStyle" id="0x7f040189" />
    <public type="attr" name="flow_maxElementsWrap" id="0x7f04018a" />
    <public type="attr" name="flow_padding" id="0x7f04018b" />
    <public type="attr" name="flow_verticalAlign" id="0x7f04018c" />
    <public type="attr" name="flow_verticalBias" id="0x7f04018d" />
    <public type="attr" name="flow_verticalGap" id="0x7f04018e" />
    <public type="attr" name="flow_verticalStyle" id="0x7f04018f" />
    <public type="attr" name="flow_wrapMode" id="0x7f040190" />
    <public type="attr" name="font" id="0x7f040191" />
    <public type="attr" name="fontFamily" id="0x7f040192" />
    <public type="attr" name="fontProviderAuthority" id="0x7f040193" />
    <public type="attr" name="fontProviderCerts" id="0x7f040194" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f040195" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f040196" />
    <public type="attr" name="fontProviderPackage" id="0x7f040197" />
    <public type="attr" name="fontProviderQuery" id="0x7f040198" />
    <public type="attr" name="fontProviderSystemFontFamily" id="0x7f040199" />
    <public type="attr" name="fontStyle" id="0x7f04019a" />
    <public type="attr" name="fontVariationSettings" id="0x7f04019b" />
    <public type="attr" name="fontWeight" id="0x7f04019c" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f04019d" />
    <public type="attr" name="fragment" id="0x7f04019e" />
    <public type="attr" name="framePosition" id="0x7f04019f" />
    <public type="attr" name="gapBetweenBars" id="0x7f0401a0" />
    <public type="attr" name="gestureInsetBottomIgnored" id="0x7f0401a1" />
    <public type="attr" name="goIcon" id="0x7f0401a2" />
    <public type="attr" name="guidelineUseRtl" id="0x7f0401a3" />
    <public type="attr" name="haloColor" id="0x7f0401a4" />
    <public type="attr" name="haloRadius" id="0x7f0401a5" />
    <public type="attr" name="headerLayout" id="0x7f0401a6" />
    <public type="attr" name="height" id="0x7f0401a7" />
    <public type="attr" name="helperText" id="0x7f0401a8" />
    <public type="attr" name="helperTextEnabled" id="0x7f0401a9" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f0401aa" />
    <public type="attr" name="helperTextTextColor" id="0x7f0401ab" />
    <public type="attr" name="hideMotionSpec" id="0x7f0401ac" />
    <public type="attr" name="hideOnContentScroll" id="0x7f0401ad" />
    <public type="attr" name="hideOnScroll" id="0x7f0401ae" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f0401af" />
    <public type="attr" name="hintEnabled" id="0x7f0401b0" />
    <public type="attr" name="hintTextAppearance" id="0x7f0401b1" />
    <public type="attr" name="hintTextColor" id="0x7f0401b2" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f0401b3" />
    <public type="attr" name="homeLayout" id="0x7f0401b4" />
    <public type="attr" name="horizontalOffset" id="0x7f0401b5" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f0401b6" />
    <public type="attr" name="icon" id="0x7f0401b7" />
    <public type="attr" name="iconEndPadding" id="0x7f0401b8" />
    <public type="attr" name="iconGravity" id="0x7f0401b9" />
    <public type="attr" name="iconPadding" id="0x7f0401ba" />
    <public type="attr" name="iconSize" id="0x7f0401bb" />
    <public type="attr" name="iconSpaceReserved" id="0x7f0401bc" />
    <public type="attr" name="iconStartPadding" id="0x7f0401bd" />
    <public type="attr" name="iconTint" id="0x7f0401be" />
    <public type="attr" name="iconTintMode" id="0x7f0401bf" />
    <public type="attr" name="iconifiedByDefault" id="0x7f0401c0" />
    <public type="attr" name="ifTagNotSet" id="0x7f0401c1" />
    <public type="attr" name="ifTagSet" id="0x7f0401c2" />
    <public type="attr" name="imageButtonStyle" id="0x7f0401c3" />
    <public type="attr" name="imagePanX" id="0x7f0401c4" />
    <public type="attr" name="imagePanY" id="0x7f0401c5" />
    <public type="attr" name="imageRotate" id="0x7f0401c6" />
    <public type="attr" name="imageZoom" id="0x7f0401c7" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f0401c8" />
    <public type="attr" name="initialActivityCount" id="0x7f0401c9" />
    <public type="attr" name="initialExpandedChildrenCount" id="0x7f0401ca" />
    <public type="attr" name="insetForeground" id="0x7f0401cb" />
    <public type="attr" name="isLightTheme" id="0x7f0401cc" />
    <public type="attr" name="isMaterialTheme" id="0x7f0401cd" />
    <public type="attr" name="isPreferenceVisible" id="0x7f0401ce" />
    <public type="attr" name="itemBackground" id="0x7f0401cf" />
    <public type="attr" name="itemFillColor" id="0x7f0401d0" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f0401d1" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f0401d2" />
    <public type="attr" name="itemIconPadding" id="0x7f0401d3" />
    <public type="attr" name="itemIconSize" id="0x7f0401d4" />
    <public type="attr" name="itemIconTint" id="0x7f0401d5" />
    <public type="attr" name="itemMaxLines" id="0x7f0401d6" />
    <public type="attr" name="itemPadding" id="0x7f0401d7" />
    <public type="attr" name="itemRippleColor" id="0x7f0401d8" />
    <public type="attr" name="itemShapeAppearance" id="0x7f0401d9" />
    <public type="attr" name="itemShapeAppearanceOverlay" id="0x7f0401da" />
    <public type="attr" name="itemShapeFillColor" id="0x7f0401db" />
    <public type="attr" name="itemShapeInsetBottom" id="0x7f0401dc" />
    <public type="attr" name="itemShapeInsetEnd" id="0x7f0401dd" />
    <public type="attr" name="itemShapeInsetStart" id="0x7f0401de" />
    <public type="attr" name="itemShapeInsetTop" id="0x7f0401df" />
    <public type="attr" name="itemSpacing" id="0x7f0401e0" />
    <public type="attr" name="itemStrokeColor" id="0x7f0401e1" />
    <public type="attr" name="itemStrokeWidth" id="0x7f0401e2" />
    <public type="attr" name="itemTextAppearance" id="0x7f0401e3" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f0401e4" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f0401e5" />
    <public type="attr" name="itemTextColor" id="0x7f0401e6" />
    <public type="attr" name="key" id="0x7f0401e7" />
    <public type="attr" name="keyPositionType" id="0x7f0401e8" />
    <public type="attr" name="keylines" id="0x7f0401e9" />
    <public type="attr" name="lStar" id="0x7f0401ea" />
    <public type="attr" name="labelBehavior" id="0x7f0401eb" />
    <public type="attr" name="labelStyle" id="0x7f0401ec" />
    <public type="attr" name="labelVisibilityMode" id="0x7f0401ed" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f0401ee" />
    <public type="attr" name="layout" id="0x7f0401ef" />
    <public type="attr" name="layoutDescription" id="0x7f0401f0" />
    <public type="attr" name="layoutDuringTransition" id="0x7f0401f1" />
    <public type="attr" name="layoutManager" id="0x7f0401f2" />
    <public type="attr" name="layout_anchor" id="0x7f0401f3" />
    <public type="attr" name="layout_anchorGravity" id="0x7f0401f4" />
    <public type="attr" name="layout_behavior" id="0x7f0401f5" />
    <public type="attr" name="layout_collapseMode" id="0x7f0401f6" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f0401f7" />
    <public type="attr" name="layout_constrainedHeight" id="0x7f0401f8" />
    <public type="attr" name="layout_constrainedWidth" id="0x7f0401f9" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f0401fa" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f0401fb" />
    <public type="attr" name="layout_constraintBaseline_toBottomOf" id="0x7f0401fc" />
    <public type="attr" name="layout_constraintBaseline_toTopOf" id="0x7f0401fd" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f0401fe" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f0401ff" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f040200" />
    <public type="attr" name="layout_constraintCircle" id="0x7f040201" />
    <public type="attr" name="layout_constraintCircleAngle" id="0x7f040202" />
    <public type="attr" name="layout_constraintCircleRadius" id="0x7f040203" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f040204" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f040205" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f040206" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f040207" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f040208" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f040209" />
    <public type="attr" name="layout_constraintHeight" id="0x7f04020a" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f04020b" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f04020c" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f04020d" />
    <public type="attr" name="layout_constraintHeight_percent" id="0x7f04020e" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f04020f" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f040210" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f040211" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f040212" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f040213" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f040214" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f040215" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f040216" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f040217" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f040218" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f040219" />
    <public type="attr" name="layout_constraintTag" id="0x7f04021a" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f04021b" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f04021c" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f04021d" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f04021e" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f04021f" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f040220" />
    <public type="attr" name="layout_constraintWidth" id="0x7f040221" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f040222" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f040223" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f040224" />
    <public type="attr" name="layout_constraintWidth_percent" id="0x7f040225" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f040226" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f040227" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f040228" />
    <public type="attr" name="layout_goneMarginBaseline" id="0x7f040229" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f04022a" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f04022b" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f04022c" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f04022d" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f04022e" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f04022f" />
    <public type="attr" name="layout_insetEdge" id="0x7f040230" />
    <public type="attr" name="layout_keyline" id="0x7f040231" />
    <public type="attr" name="layout_marginBaseline" id="0x7f040232" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f040233" />
    <public type="attr" name="layout_scrollFlags" id="0x7f040234" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f040235" />
    <public type="attr" name="layout_wrapBehaviorInParent" id="0x7f040236" />
    <public type="attr" name="liftOnScroll" id="0x7f040237" />
    <public type="attr" name="liftOnScrollTargetViewId" id="0x7f040238" />
    <public type="attr" name="limitBoundsTo" id="0x7f040239" />
    <public type="attr" name="lineHeight" id="0x7f04023a" />
    <public type="attr" name="lineSpacing" id="0x7f04023b" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f04023c" />
    <public type="attr" name="listChoiceIndicatorMultipleAnimated" id="0x7f04023d" />
    <public type="attr" name="listChoiceIndicatorSingleAnimated" id="0x7f04023e" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f04023f" />
    <public type="attr" name="listItemLayout" id="0x7f040240" />
    <public type="attr" name="listLayout" id="0x7f040241" />
    <public type="attr" name="listMenuViewStyle" id="0x7f040242" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f040243" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f040244" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f040245" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f040246" />
    <public type="attr" name="listPreferredItemPaddingEnd" id="0x7f040247" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f040248" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f040249" />
    <public type="attr" name="listPreferredItemPaddingStart" id="0x7f04024a" />
    <public type="attr" name="logo" id="0x7f04024b" />
    <public type="attr" name="logoDescription" id="0x7f04024c" />
    <public type="attr" name="materialAlertDialogBodyTextStyle" id="0x7f04024d" />
    <public type="attr" name="materialAlertDialogTheme" id="0x7f04024e" />
    <public type="attr" name="materialAlertDialogTitleIconStyle" id="0x7f04024f" />
    <public type="attr" name="materialAlertDialogTitlePanelStyle" id="0x7f040250" />
    <public type="attr" name="materialAlertDialogTitleTextStyle" id="0x7f040251" />
    <public type="attr" name="materialButtonOutlinedStyle" id="0x7f040252" />
    <public type="attr" name="materialButtonStyle" id="0x7f040253" />
    <public type="attr" name="materialButtonToggleGroupStyle" id="0x7f040254" />
    <public type="attr" name="materialCalendarDay" id="0x7f040255" />
    <public type="attr" name="materialCalendarFullscreenTheme" id="0x7f040256" />
    <public type="attr" name="materialCalendarHeaderConfirmButton" id="0x7f040257" />
    <public type="attr" name="materialCalendarHeaderDivider" id="0x7f040258" />
    <public type="attr" name="materialCalendarHeaderLayout" id="0x7f040259" />
    <public type="attr" name="materialCalendarHeaderSelection" id="0x7f04025a" />
    <public type="attr" name="materialCalendarHeaderTitle" id="0x7f04025b" />
    <public type="attr" name="materialCalendarHeaderToggleButton" id="0x7f04025c" />
    <public type="attr" name="materialCalendarStyle" id="0x7f04025d" />
    <public type="attr" name="materialCalendarTheme" id="0x7f04025e" />
    <public type="attr" name="materialCardViewStyle" id="0x7f04025f" />
    <public type="attr" name="materialThemeOverlay" id="0x7f040260" />
    <public type="attr" name="maxAcceleration" id="0x7f040261" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f040262" />
    <public type="attr" name="maxButtonHeight" id="0x7f040263" />
    <public type="attr" name="maxCharacterCount" id="0x7f040264" />
    <public type="attr" name="maxHeight" id="0x7f040265" />
    <public type="attr" name="maxImageSize" id="0x7f040266" />
    <public type="attr" name="maxLines" id="0x7f040267" />
    <public type="attr" name="maxVelocity" id="0x7f040268" />
    <public type="attr" name="maxWidth" id="0x7f040269" />
    <public type="attr" name="measureWithLargestChild" id="0x7f04026a" />
    <public type="attr" name="menu" id="0x7f04026b" />
    <public type="attr" name="methodName" id="0x7f04026c" />
    <public type="attr" name="min" id="0x7f04026d" />
    <public type="attr" name="minHeight" id="0x7f04026e" />
    <public type="attr" name="minTouchTargetSize" id="0x7f04026f" />
    <public type="attr" name="minWidth" id="0x7f040270" />
    <public type="attr" name="mock_diagonalsColor" id="0x7f040271" />
    <public type="attr" name="mock_label" id="0x7f040272" />
    <public type="attr" name="mock_labelBackgroundColor" id="0x7f040273" />
    <public type="attr" name="mock_labelColor" id="0x7f040274" />
    <public type="attr" name="mock_showDiagonals" id="0x7f040275" />
    <public type="attr" name="mock_showLabel" id="0x7f040276" />
    <public type="attr" name="motionDebug" id="0x7f040277" />
    <public type="attr" name="motionEffect_alpha" id="0x7f040278" />
    <public type="attr" name="motionEffect_end" id="0x7f040279" />
    <public type="attr" name="motionEffect_move" id="0x7f04027a" />
    <public type="attr" name="motionEffect_start" id="0x7f04027b" />
    <public type="attr" name="motionEffect_strict" id="0x7f04027c" />
    <public type="attr" name="motionEffect_translationX" id="0x7f04027d" />
    <public type="attr" name="motionEffect_translationY" id="0x7f04027e" />
    <public type="attr" name="motionEffect_viewTransition" id="0x7f04027f" />
    <public type="attr" name="motionInterpolator" id="0x7f040280" />
    <public type="attr" name="motionPathRotate" id="0x7f040281" />
    <public type="attr" name="motionProgress" id="0x7f040282" />
    <public type="attr" name="motionStagger" id="0x7f040283" />
    <public type="attr" name="motionTarget" id="0x7f040284" />
    <public type="attr" name="motion_postLayoutCollision" id="0x7f040285" />
    <public type="attr" name="motion_triggerOnCollision" id="0x7f040286" />
    <public type="attr" name="moveWhenScrollAtTop" id="0x7f040287" />
    <public type="attr" name="mrl_rippleAlpha" id="0x7f040288" />
    <public type="attr" name="mrl_rippleBackground" id="0x7f040289" />
    <public type="attr" name="mrl_rippleColor" id="0x7f04028a" />
    <public type="attr" name="mrl_rippleDelayClick" id="0x7f04028b" />
    <public type="attr" name="mrl_rippleDimension" id="0x7f04028c" />
    <public type="attr" name="mrl_rippleDuration" id="0x7f04028d" />
    <public type="attr" name="mrl_rippleFadeDuration" id="0x7f04028e" />
    <public type="attr" name="mrl_rippleHover" id="0x7f04028f" />
    <public type="attr" name="mrl_rippleInAdapter" id="0x7f040290" />
    <public type="attr" name="mrl_rippleOverlay" id="0x7f040291" />
    <public type="attr" name="mrl_ripplePersistent" id="0x7f040292" />
    <public type="attr" name="mrl_rippleRoundedCorners" id="0x7f040293" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f040294" />
    <public type="attr" name="navigationContentDescription" id="0x7f040295" />
    <public type="attr" name="navigationIcon" id="0x7f040296" />
    <public type="attr" name="navigationMode" id="0x7f040297" />
    <public type="attr" name="navigationViewStyle" id="0x7f040298" />
    <public type="attr" name="negativeButtonText" id="0x7f040299" />
    <public type="attr" name="nestedScrollFlags" id="0x7f04029a" />
    <public type="attr" name="nestedScrollViewStyle" id="0x7f04029b" />
    <public type="attr" name="number" id="0x7f04029c" />
    <public type="attr" name="numericModifiers" id="0x7f04029d" />
    <public type="attr" name="onCross" id="0x7f04029e" />
    <public type="attr" name="onHide" id="0x7f04029f" />
    <public type="attr" name="onNegativeCross" id="0x7f0402a0" />
    <public type="attr" name="onPositiveCross" id="0x7f0402a1" />
    <public type="attr" name="onShow" id="0x7f0402a2" />
    <public type="attr" name="onStateTransition" id="0x7f0402a3" />
    <public type="attr" name="onTouchUp" id="0x7f0402a4" />
    <public type="attr" name="order" id="0x7f0402a5" />
    <public type="attr" name="orderingFromXml" id="0x7f0402a6" />
    <public type="attr" name="overlapAnchor" id="0x7f0402a7" />
    <public type="attr" name="overlay" id="0x7f0402a8" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f0402a9" />
    <public type="attr" name="paddingBottomSystemWindowInsets" id="0x7f0402aa" />
    <public type="attr" name="paddingEnd" id="0x7f0402ab" />
    <public type="attr" name="paddingLeftSystemWindowInsets" id="0x7f0402ac" />
    <public type="attr" name="paddingRightSystemWindowInsets" id="0x7f0402ad" />
    <public type="attr" name="paddingStart" id="0x7f0402ae" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f0402af" />
    <public type="attr" name="panelBackground" id="0x7f0402b0" />
    <public type="attr" name="panelMenuListTheme" id="0x7f0402b1" />
    <public type="attr" name="panelMenuListWidth" id="0x7f0402b2" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f0402b3" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f0402b4" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f0402b5" />
    <public type="attr" name="passwordToggleTint" id="0x7f0402b6" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f0402b7" />
    <public type="attr" name="pathMotionArc" id="0x7f0402b8" />
    <public type="attr" name="path_percent" id="0x7f0402b9" />
    <public type="attr" name="percentHeight" id="0x7f0402ba" />
    <public type="attr" name="percentWidth" id="0x7f0402bb" />
    <public type="attr" name="percentX" id="0x7f0402bc" />
    <public type="attr" name="percentY" id="0x7f0402bd" />
    <public type="attr" name="perpendicularPath_percent" id="0x7f0402be" />
    <public type="attr" name="persistent" id="0x7f0402bf" />
    <public type="attr" name="pivotAnchor" id="0x7f0402c0" />
    <public type="attr" name="placeholderActivityName" id="0x7f0402c1" />
    <public type="attr" name="placeholderText" id="0x7f0402c2" />
    <public type="attr" name="placeholderTextAppearance" id="0x7f0402c3" />
    <public type="attr" name="placeholderTextColor" id="0x7f0402c4" />
    <public type="attr" name="placeholder_emptyVisibility" id="0x7f0402c5" />
    <public type="attr" name="polarRelativeTo" id="0x7f0402c6" />
    <public type="attr" name="popupMenuBackground" id="0x7f0402c7" />
    <public type="attr" name="popupMenuStyle" id="0x7f0402c8" />
    <public type="attr" name="popupTheme" id="0x7f0402c9" />
    <public type="attr" name="popupWindowStyle" id="0x7f0402ca" />
    <public type="attr" name="positiveButtonText" id="0x7f0402cb" />
    <public type="attr" name="preferenceCategoryStyle" id="0x7f0402cc" />
    <public type="attr" name="preferenceCategoryTitleTextAppearance" id="0x7f0402cd" />
    <public type="attr" name="preferenceCategoryTitleTextColor" id="0x7f0402ce" />
    <public type="attr" name="preferenceFragmentCompatStyle" id="0x7f0402cf" />
    <public type="attr" name="preferenceFragmentListStyle" id="0x7f0402d0" />
    <public type="attr" name="preferenceFragmentStyle" id="0x7f0402d1" />
    <public type="attr" name="preferenceInformationStyle" id="0x7f0402d2" />
    <public type="attr" name="preferenceScreenStyle" id="0x7f0402d3" />
    <public type="attr" name="preferenceStyle" id="0x7f0402d4" />
    <public type="attr" name="preferenceTheme" id="0x7f0402d5" />
    <public type="attr" name="prefixText" id="0x7f0402d6" />
    <public type="attr" name="prefixTextAppearance" id="0x7f0402d7" />
    <public type="attr" name="prefixTextColor" id="0x7f0402d8" />
    <public type="attr" name="preserveIconSpacing" id="0x7f0402d9" />
    <public type="attr" name="pressedTranslationZ" id="0x7f0402da" />
    <public type="attr" name="primaryActivityName" id="0x7f0402db" />
    <public type="attr" name="progressBarPadding" id="0x7f0402dc" />
    <public type="attr" name="progressBarStyle" id="0x7f0402dd" />
    <public type="attr" name="quantizeMotionInterpolator" id="0x7f0402de" />
    <public type="attr" name="quantizeMotionPhase" id="0x7f0402df" />
    <public type="attr" name="quantizeMotionSteps" id="0x7f0402e0" />
    <public type="attr" name="queryBackground" id="0x7f0402e1" />
    <public type="attr" name="queryHint" id="0x7f0402e2" />
    <public type="attr" name="queryPatterns" id="0x7f0402e3" />
    <public type="attr" name="radioButtonStyle" id="0x7f0402e4" />
    <public type="attr" name="rangeFillColor" id="0x7f0402e5" />
    <public type="attr" name="ratingBarStyle" id="0x7f0402e6" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f0402e7" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f0402e8" />
    <public type="attr" name="reactiveGuide_animateChange" id="0x7f0402e9" />
    <public type="attr" name="reactiveGuide_applyToAllConstraintSets" id="0x7f0402ea" />
    <public type="attr" name="reactiveGuide_applyToConstraintSet" id="0x7f0402eb" />
    <public type="attr" name="reactiveGuide_valueId" id="0x7f0402ec" />
    <public type="attr" name="recyclerViewStyle" id="0x7f0402ed" />
    <public type="attr" name="region_heightLessThan" id="0x7f0402ee" />
    <public type="attr" name="region_heightMoreThan" id="0x7f0402ef" />
    <public type="attr" name="region_widthLessThan" id="0x7f0402f0" />
    <public type="attr" name="region_widthMoreThan" id="0x7f0402f1" />
    <public type="attr" name="reverseLayout" id="0x7f0402f2" />
    <public type="attr" name="rippleColor" id="0x7f0402f3" />
    <public type="attr" name="rotationCenterId" id="0x7f0402f4" />
    <public type="attr" name="round" id="0x7f0402f5" />
    <public type="attr" name="roundPercent" id="0x7f0402f6" />
    <public type="attr" name="saturation" id="0x7f0402f7" />
    <public type="attr" name="sb_background" id="0x7f0402f8" />
    <public type="attr" name="sb_border_width" id="0x7f0402f9" />
    <public type="attr" name="sb_button_color" id="0x7f0402fa" />
    <public type="attr" name="sb_checked" id="0x7f0402fb" />
    <public type="attr" name="sb_checked_color" id="0x7f0402fc" />
    <public type="attr" name="sb_checkline_color" id="0x7f0402fd" />
    <public type="attr" name="sb_checkline_width" id="0x7f0402fe" />
    <public type="attr" name="sb_effect_duration" id="0x7f0402ff" />
    <public type="attr" name="sb_enable_effect" id="0x7f040300" />
    <public type="attr" name="sb_shadow_color" id="0x7f040301" />
    <public type="attr" name="sb_shadow_effect" id="0x7f040302" />
    <public type="attr" name="sb_shadow_offset" id="0x7f040303" />
    <public type="attr" name="sb_shadow_radius" id="0x7f040304" />
    <public type="attr" name="sb_show_indicator" id="0x7f040305" />
    <public type="attr" name="sb_uncheck_color" id="0x7f040306" />
    <public type="attr" name="sb_uncheckcircle_color" id="0x7f040307" />
    <public type="attr" name="sb_uncheckcircle_radius" id="0x7f040308" />
    <public type="attr" name="sb_uncheckcircle_width" id="0x7f040309" />
    <public type="attr" name="scaleFromTextSize" id="0x7f04030a" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f04030b" />
    <public type="attr" name="scrimBackground" id="0x7f04030c" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f04030d" />
    <public type="attr" name="searchHintIcon" id="0x7f04030e" />
    <public type="attr" name="searchIcon" id="0x7f04030f" />
    <public type="attr" name="searchViewStyle" id="0x7f040310" />
    <public type="attr" name="secondaryActivityAction" id="0x7f040311" />
    <public type="attr" name="secondaryActivityName" id="0x7f040312" />
    <public type="attr" name="seekBarIncrement" id="0x7f040313" />
    <public type="attr" name="seekBarPreferenceStyle" id="0x7f040314" />
    <public type="attr" name="seekBarStyle" id="0x7f040315" />
    <public type="attr" name="selectable" id="0x7f040316" />
    <public type="attr" name="selectableItemBackground" id="0x7f040317" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f040318" />
    <public type="attr" name="selectionRequired" id="0x7f040319" />
    <public type="attr" name="setsTag" id="0x7f04031a" />
    <public type="attr" name="shapeAppearance" id="0x7f04031b" />
    <public type="attr" name="shapeAppearanceLargeComponent" id="0x7f04031c" />
    <public type="attr" name="shapeAppearanceMediumComponent" id="0x7f04031d" />
    <public type="attr" name="shapeAppearanceOverlay" id="0x7f04031e" />
    <public type="attr" name="shapeAppearanceSmallComponent" id="0x7f04031f" />
    <public type="attr" name="shortcutMatchRequired" id="0x7f040320" />
    <public type="attr" name="shouldDisableView" id="0x7f040321" />
    <public type="attr" name="showAsAction" id="0x7f040322" />
    <public type="attr" name="showDividers" id="0x7f040323" />
    <public type="attr" name="showMotionSpec" id="0x7f040324" />
    <public type="attr" name="showPaths" id="0x7f040325" />
    <public type="attr" name="showSeekBarValue" id="0x7f040326" />
    <public type="attr" name="showText" id="0x7f040327" />
    <public type="attr" name="showTitle" id="0x7f040328" />
    <public type="attr" name="shrinkMotionSpec" id="0x7f040329" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f04032a" />
    <public type="attr" name="singleLine" id="0x7f04032b" />
    <public type="attr" name="singleLineTitle" id="0x7f04032c" />
    <public type="attr" name="singleSelection" id="0x7f04032d" />
    <public type="attr" name="sizePercent" id="0x7f04032e" />
    <public type="attr" name="sliderStyle" id="0x7f04032f" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f040330" />
    <public type="attr" name="snackbarStyle" id="0x7f040331" />
    <public type="attr" name="snackbarTextViewStyle" id="0x7f040332" />
    <public type="attr" name="spanCount" id="0x7f040333" />
    <public type="attr" name="spinBars" id="0x7f040334" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f040335" />
    <public type="attr" name="spinnerStyle" id="0x7f040336" />
    <public type="attr" name="splitLayoutDirection" id="0x7f040337" />
    <public type="attr" name="splitMinSmallestWidth" id="0x7f040338" />
    <public type="attr" name="splitMinWidth" id="0x7f040339" />
    <public type="attr" name="splitRatio" id="0x7f04033a" />
    <public type="attr" name="splitTrack" id="0x7f04033b" />
    <public type="attr" name="springBoundary" id="0x7f04033c" />
    <public type="attr" name="springDamping" id="0x7f04033d" />
    <public type="attr" name="springMass" id="0x7f04033e" />
    <public type="attr" name="springStiffness" id="0x7f04033f" />
    <public type="attr" name="springStopThreshold" id="0x7f040340" />
    <public type="attr" name="srcCompat" id="0x7f040341" />
    <public type="attr" name="stackFromEnd" id="0x7f040342" />
    <public type="attr" name="staggered" id="0x7f040343" />
    <public type="attr" name="startIconCheckable" id="0x7f040344" />
    <public type="attr" name="startIconContentDescription" id="0x7f040345" />
    <public type="attr" name="startIconDrawable" id="0x7f040346" />
    <public type="attr" name="startIconTint" id="0x7f040347" />
    <public type="attr" name="startIconTintMode" id="0x7f040348" />
    <public type="attr" name="state_above_anchor" id="0x7f040349" />
    <public type="attr" name="state_collapsed" id="0x7f04034a" />
    <public type="attr" name="state_collapsible" id="0x7f04034b" />
    <public type="attr" name="state_dragged" id="0x7f04034c" />
    <public type="attr" name="state_liftable" id="0x7f04034d" />
    <public type="attr" name="state_lifted" id="0x7f04034e" />
    <public type="attr" name="statusBarBackground" id="0x7f04034f" />
    <public type="attr" name="statusBarForeground" id="0x7f040350" />
    <public type="attr" name="statusBarScrim" id="0x7f040351" />
    <public type="attr" name="strokeColor" id="0x7f040352" />
    <public type="attr" name="strokeWidth" id="0x7f040353" />
    <public type="attr" name="subMenuArrow" id="0x7f040354" />
    <public type="attr" name="submitBackground" id="0x7f040355" />
    <public type="attr" name="subtitle" id="0x7f040356" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f040357" />
    <public type="attr" name="subtitleTextColor" id="0x7f040358" />
    <public type="attr" name="subtitleTextStyle" id="0x7f040359" />
    <public type="attr" name="suffixText" id="0x7f04035a" />
    <public type="attr" name="suffixTextAppearance" id="0x7f04035b" />
    <public type="attr" name="suffixTextColor" id="0x7f04035c" />
    <public type="attr" name="suggestionRowLayout" id="0x7f04035d" />
    <public type="attr" name="summary" id="0x7f04035e" />
    <public type="attr" name="summaryOff" id="0x7f04035f" />
    <public type="attr" name="summaryOn" id="0x7f040360" />
    <public type="attr" name="switchMinWidth" id="0x7f040361" />
    <public type="attr" name="switchPadding" id="0x7f040362" />
    <public type="attr" name="switchPreferenceCompatStyle" id="0x7f040363" />
    <public type="attr" name="switchPreferenceStyle" id="0x7f040364" />
    <public type="attr" name="switchStyle" id="0x7f040365" />
    <public type="attr" name="switchTextAppearance" id="0x7f040366" />
    <public type="attr" name="switchTextOff" id="0x7f040367" />
    <public type="attr" name="switchTextOn" id="0x7f040368" />
    <public type="attr" name="tabBackground" id="0x7f040369" />
    <public type="attr" name="tabContentStart" id="0x7f04036a" />
    <public type="attr" name="tabGravity" id="0x7f04036b" />
    <public type="attr" name="tabIconTint" id="0x7f04036c" />
    <public type="attr" name="tabIconTintMode" id="0x7f04036d" />
    <public type="attr" name="tabIndicator" id="0x7f04036e" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f04036f" />
    <public type="attr" name="tabIndicatorColor" id="0x7f040370" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f040371" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f040372" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f040373" />
    <public type="attr" name="tabInlineLabel" id="0x7f040374" />
    <public type="attr" name="tabMaxWidth" id="0x7f040375" />
    <public type="attr" name="tabMinWidth" id="0x7f040376" />
    <public type="attr" name="tabMode" id="0x7f040377" />
    <public type="attr" name="tabPadding" id="0x7f040378" />
    <public type="attr" name="tabPaddingBottom" id="0x7f040379" />
    <public type="attr" name="tabPaddingEnd" id="0x7f04037a" />
    <public type="attr" name="tabPaddingStart" id="0x7f04037b" />
    <public type="attr" name="tabPaddingTop" id="0x7f04037c" />
    <public type="attr" name="tabRippleColor" id="0x7f04037d" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f04037e" />
    <public type="attr" name="tabStyle" id="0x7f04037f" />
    <public type="attr" name="tabTextAppearance" id="0x7f040380" />
    <public type="attr" name="tabTextColor" id="0x7f040381" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f040382" />
    <public type="attr" name="targetId" id="0x7f040383" />
    <public type="attr" name="telltales_tailColor" id="0x7f040384" />
    <public type="attr" name="telltales_tailScale" id="0x7f040385" />
    <public type="attr" name="telltales_velocityMode" id="0x7f040386" />
    <public type="attr" name="textAllCaps" id="0x7f040387" />
    <public type="attr" name="textAppearanceBody1" id="0x7f040388" />
    <public type="attr" name="textAppearanceBody2" id="0x7f040389" />
    <public type="attr" name="textAppearanceButton" id="0x7f04038a" />
    <public type="attr" name="textAppearanceCaption" id="0x7f04038b" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f04038c" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f04038d" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f04038e" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f04038f" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f040390" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f040391" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f040392" />
    <public type="attr" name="textAppearanceLineHeightEnabled" id="0x7f040393" />
    <public type="attr" name="textAppearanceListItem" id="0x7f040394" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f040395" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f040396" />
    <public type="attr" name="textAppearanceOverline" id="0x7f040397" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f040398" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f040399" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f04039a" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f04039b" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f04039c" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f04039d" />
    <public type="attr" name="textBackground" id="0x7f04039e" />
    <public type="attr" name="textBackgroundPanX" id="0x7f04039f" />
    <public type="attr" name="textBackgroundPanY" id="0x7f0403a0" />
    <public type="attr" name="textBackgroundRotate" id="0x7f0403a1" />
    <public type="attr" name="textBackgroundZoom" id="0x7f0403a2" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f0403a3" />
    <public type="attr" name="textColorSearchUrl" id="0x7f0403a4" />
    <public type="attr" name="textEndPadding" id="0x7f0403a5" />
    <public type="attr" name="textFillColor" id="0x7f0403a6" />
    <public type="attr" name="textInputLayoutFocusedRectEnabled" id="0x7f0403a7" />
    <public type="attr" name="textInputStyle" id="0x7f0403a8" />
    <public type="attr" name="textLocale" id="0x7f0403a9" />
    <public type="attr" name="textOutlineColor" id="0x7f0403aa" />
    <public type="attr" name="textOutlineThickness" id="0x7f0403ab" />
    <public type="attr" name="textPanX" id="0x7f0403ac" />
    <public type="attr" name="textPanY" id="0x7f0403ad" />
    <public type="attr" name="textStartPadding" id="0x7f0403ae" />
    <public type="attr" name="textureBlurFactor" id="0x7f0403af" />
    <public type="attr" name="textureEffect" id="0x7f0403b0" />
    <public type="attr" name="textureHeight" id="0x7f0403b1" />
    <public type="attr" name="textureWidth" id="0x7f0403b2" />
    <public type="attr" name="theme" id="0x7f0403b3" />
    <public type="attr" name="themeLineHeight" id="0x7f0403b4" />
    <public type="attr" name="thickness" id="0x7f0403b5" />
    <public type="attr" name="thumbColor" id="0x7f0403b6" />
    <public type="attr" name="thumbElevation" id="0x7f0403b7" />
    <public type="attr" name="thumbRadius" id="0x7f0403b8" />
    <public type="attr" name="thumbTextPadding" id="0x7f0403b9" />
    <public type="attr" name="thumbTint" id="0x7f0403ba" />
    <public type="attr" name="thumbTintMode" id="0x7f0403bb" />
    <public type="attr" name="tickColor" id="0x7f0403bc" />
    <public type="attr" name="tickColorActive" id="0x7f0403bd" />
    <public type="attr" name="tickColorInactive" id="0x7f0403be" />
    <public type="attr" name="tickMark" id="0x7f0403bf" />
    <public type="attr" name="tickMarkTint" id="0x7f0403c0" />
    <public type="attr" name="tickMarkTintMode" id="0x7f0403c1" />
    <public type="attr" name="tint" id="0x7f0403c2" />
    <public type="attr" name="tintMode" id="0x7f0403c3" />
    <public type="attr" name="title" id="0x7f0403c4" />
    <public type="attr" name="titleEnabled" id="0x7f0403c5" />
    <public type="attr" name="titleMargin" id="0x7f0403c6" />
    <public type="attr" name="titleMarginBottom" id="0x7f0403c7" />
    <public type="attr" name="titleMarginEnd" id="0x7f0403c8" />
    <public type="attr" name="titleMarginStart" id="0x7f0403c9" />
    <public type="attr" name="titleMarginTop" id="0x7f0403ca" />
    <public type="attr" name="titleMargins" id="0x7f0403cb" />
    <public type="attr" name="titleTextAppearance" id="0x7f0403cc" />
    <public type="attr" name="titleTextColor" id="0x7f0403cd" />
    <public type="attr" name="titleTextStyle" id="0x7f0403ce" />
    <public type="attr" name="toolbarId" id="0x7f0403cf" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f0403d0" />
    <public type="attr" name="toolbarStyle" id="0x7f0403d1" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f0403d2" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f0403d3" />
    <public type="attr" name="tooltipStyle" id="0x7f0403d4" />
    <public type="attr" name="tooltipText" id="0x7f0403d5" />
    <public type="attr" name="touchAnchorId" id="0x7f0403d6" />
    <public type="attr" name="touchAnchorSide" id="0x7f0403d7" />
    <public type="attr" name="touchRegionId" id="0x7f0403d8" />
    <public type="attr" name="track" id="0x7f0403d9" />
    <public type="attr" name="trackColor" id="0x7f0403da" />
    <public type="attr" name="trackColorActive" id="0x7f0403db" />
    <public type="attr" name="trackColorInactive" id="0x7f0403dc" />
    <public type="attr" name="trackHeight" id="0x7f0403dd" />
    <public type="attr" name="trackTint" id="0x7f0403de" />
    <public type="attr" name="trackTintMode" id="0x7f0403df" />
    <public type="attr" name="transformPivotTarget" id="0x7f0403e0" />
    <public type="attr" name="transitionDisable" id="0x7f0403e1" />
    <public type="attr" name="transitionEasing" id="0x7f0403e2" />
    <public type="attr" name="transitionFlags" id="0x7f0403e3" />
    <public type="attr" name="transitionPathRotate" id="0x7f0403e4" />
    <public type="attr" name="transitionShapeAppearance" id="0x7f0403e5" />
    <public type="attr" name="triggerId" id="0x7f0403e6" />
    <public type="attr" name="triggerReceiver" id="0x7f0403e7" />
    <public type="attr" name="triggerSlack" id="0x7f0403e8" />
    <public type="attr" name="ttcIndex" id="0x7f0403e9" />
    <public type="attr" name="upDuration" id="0x7f0403ea" />
    <public type="attr" name="updatesContinuously" id="0x7f0403eb" />
    <public type="attr" name="useCompatPadding" id="0x7f0403ec" />
    <public type="attr" name="useMaterialThemeColors" id="0x7f0403ed" />
    <public type="attr" name="useSimpleSummaryProvider" id="0x7f0403ee" />
    <public type="attr" name="values" id="0x7f0403ef" />
    <public type="attr" name="verticalOffset" id="0x7f0403f0" />
    <public type="attr" name="viewInflaterClass" id="0x7f0403f1" />
    <public type="attr" name="viewTransitionMode" id="0x7f0403f2" />
    <public type="attr" name="viewTransitionOnCross" id="0x7f0403f3" />
    <public type="attr" name="viewTransitionOnNegativeCross" id="0x7f0403f4" />
    <public type="attr" name="viewTransitionOnPositiveCross" id="0x7f0403f5" />
    <public type="attr" name="visibilityMode" id="0x7f0403f6" />
    <public type="attr" name="voiceIcon" id="0x7f0403f7" />
    <public type="attr" name="warmth" id="0x7f0403f8" />
    <public type="attr" name="waveDecay" id="0x7f0403f9" />
    <public type="attr" name="waveOffset" id="0x7f0403fa" />
    <public type="attr" name="wavePeriod" id="0x7f0403fb" />
    <public type="attr" name="wavePhase" id="0x7f0403fc" />
    <public type="attr" name="waveShape" id="0x7f0403fd" />
    <public type="attr" name="waveVariesBy" id="0x7f0403fe" />
    <public type="attr" name="widgetLayout" id="0x7f0403ff" />
    <public type="attr" name="windowActionBar" id="0x7f040400" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f040401" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f040402" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f040403" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f040404" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f040405" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f040406" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f040407" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f040408" />
    <public type="attr" name="windowNoTitle" id="0x7f040409" />
    <public type="attr" name="yearSelectedStyle" id="0x7f04040a" />
    <public type="attr" name="yearStyle" id="0x7f04040b" />
    <public type="attr" name="yearTodayStyle" id="0x7f04040c" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f050000" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f050001" />
    <public type="bool" name="config_materialPreferenceIconSpaceReserved" id="0x7f050002" />
    <public type="bool" name="mtrl_btn_textappearance_all_caps" id="0x7f050003" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f060000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f060001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f060002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f060003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f060004" />
    <public type="color" name="abc_decor_view_status_guard" id="0x7f060005" />
    <public type="color" name="abc_decor_view_status_guard_light" id="0x7f060006" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f060007" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f060008" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f060009" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f06000a" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f06000b" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f06000c" />
    <public type="color" name="abc_search_url_text" id="0x7f06000d" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f06000e" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f06000f" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f060010" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f060011" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f060012" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f060013" />
    <public type="color" name="abc_tint_default" id="0x7f060014" />
    <public type="color" name="abc_tint_edittext" id="0x7f060015" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f060016" />
    <public type="color" name="abc_tint_spinner" id="0x7f060017" />
    <public type="color" name="abc_tint_switch_track" id="0x7f060018" />
    <public type="color" name="accent_material_dark" id="0x7f060019" />
    <public type="color" name="accent_material_light" id="0x7f06001a" />
    <public type="color" name="aliceblue" id="0x7f06001b" />
    <public type="color" name="androidx_core_ripple_material_light" id="0x7f06001c" />
    <public type="color" name="androidx_core_secondary_text_default_material_light" id="0x7f06001d" />
    <public type="color" name="antiquewhite" id="0x7f06001e" />
    <public type="color" name="aqua" id="0x7f06001f" />
    <public type="color" name="aquamarine" id="0x7f060020" />
    <public type="color" name="azure" id="0x7f060021" />
    <public type="color" name="background_floating_material_dark" id="0x7f060022" />
    <public type="color" name="background_floating_material_light" id="0x7f060023" />
    <public type="color" name="background_material_dark" id="0x7f060024" />
    <public type="color" name="background_material_light" id="0x7f060025" />
    <public type="color" name="beige" id="0x7f060026" />
    <public type="color" name="bisque" id="0x7f060027" />
    <public type="color" name="black" id="0x7f060028" />
    <public type="color" name="black_semi_transparent" id="0x7f060029" />
    <public type="color" name="blanchedalmond" id="0x7f06002a" />
    <public type="color" name="blue" id="0x7f06002b" />
    <public type="color" name="blue_semi_transparent" id="0x7f06002c" />
    <public type="color" name="blue_semi_transparent_pressed" id="0x7f06002d" />
    <public type="color" name="blueviolet" id="0x7f06002e" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f06002f" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f060030" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f060031" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f060032" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f060033" />
    <public type="color" name="bright_foreground_material_light" id="0x7f060034" />
    <public type="color" name="brown" id="0x7f060035" />
    <public type="color" name="burlywood" id="0x7f060036" />
    <public type="color" name="button_material_dark" id="0x7f060037" />
    <public type="color" name="button_material_light" id="0x7f060038" />
    <public type="color" name="cadetblue" id="0x7f060039" />
    <public type="color" name="cardview_dark_background" id="0x7f06003a" />
    <public type="color" name="cardview_light_background" id="0x7f06003b" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f06003c" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f06003d" />
    <public type="color" name="chartreuse" id="0x7f06003e" />
    <public type="color" name="checkbox_themeable_attribute_color" id="0x7f06003f" />
    <public type="color" name="chocolate" id="0x7f060040" />
    <public type="color" name="colorAccent" id="0x7f060041" />
    <public type="color" name="colorPrimary" id="0x7f060042" />
    <public type="color" name="colorPrimaryDark" id="0x7f060043" />
    <public type="color" name="coral" id="0x7f060044" />
    <public type="color" name="cornflowerblue" id="0x7f060045" />
    <public type="color" name="cornsilk" id="0x7f060046" />
    <public type="color" name="crimson" id="0x7f060047" />
    <public type="color" name="cyan" id="0x7f060048" />
    <public type="color" name="darkblue" id="0x7f060049" />
    <public type="color" name="darkcyan" id="0x7f06004a" />
    <public type="color" name="darkgoldenrod" id="0x7f06004b" />
    <public type="color" name="darkgray" id="0x7f06004c" />
    <public type="color" name="darkgreen" id="0x7f06004d" />
    <public type="color" name="darkgrey" id="0x7f06004e" />
    <public type="color" name="darkkhaki" id="0x7f06004f" />
    <public type="color" name="darkmagenta" id="0x7f060050" />
    <public type="color" name="darkolivegreen" id="0x7f060051" />
    <public type="color" name="darkorange" id="0x7f060052" />
    <public type="color" name="darkorchid" id="0x7f060053" />
    <public type="color" name="darkred" id="0x7f060054" />
    <public type="color" name="darksalmon" id="0x7f060055" />
    <public type="color" name="darkseagreen" id="0x7f060056" />
    <public type="color" name="darkslateblue" id="0x7f060057" />
    <public type="color" name="darkslategray" id="0x7f060058" />
    <public type="color" name="darkslategrey" id="0x7f060059" />
    <public type="color" name="darkturquoise" id="0x7f06005a" />
    <public type="color" name="darkviolet" id="0x7f06005b" />
    <public type="color" name="deeppink" id="0x7f06005c" />
    <public type="color" name="deepskyblue" id="0x7f06005d" />
    <public type="color" name="design_bottom_navigation_shadow_color" id="0x7f06005e" />
    <public type="color" name="design_box_stroke_color" id="0x7f06005f" />
    <public type="color" name="design_dark_default_color_background" id="0x7f060060" />
    <public type="color" name="design_dark_default_color_error" id="0x7f060061" />
    <public type="color" name="design_dark_default_color_on_background" id="0x7f060062" />
    <public type="color" name="design_dark_default_color_on_error" id="0x7f060063" />
    <public type="color" name="design_dark_default_color_on_primary" id="0x7f060064" />
    <public type="color" name="design_dark_default_color_on_secondary" id="0x7f060065" />
    <public type="color" name="design_dark_default_color_on_surface" id="0x7f060066" />
    <public type="color" name="design_dark_default_color_primary" id="0x7f060067" />
    <public type="color" name="design_dark_default_color_primary_dark" id="0x7f060068" />
    <public type="color" name="design_dark_default_color_primary_variant" id="0x7f060069" />
    <public type="color" name="design_dark_default_color_secondary" id="0x7f06006a" />
    <public type="color" name="design_dark_default_color_secondary_variant" id="0x7f06006b" />
    <public type="color" name="design_dark_default_color_surface" id="0x7f06006c" />
    <public type="color" name="design_default_color_background" id="0x7f06006d" />
    <public type="color" name="design_default_color_error" id="0x7f06006e" />
    <public type="color" name="design_default_color_on_background" id="0x7f06006f" />
    <public type="color" name="design_default_color_on_error" id="0x7f060070" />
    <public type="color" name="design_default_color_on_primary" id="0x7f060071" />
    <public type="color" name="design_default_color_on_secondary" id="0x7f060072" />
    <public type="color" name="design_default_color_on_surface" id="0x7f060073" />
    <public type="color" name="design_default_color_primary" id="0x7f060074" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f060075" />
    <public type="color" name="design_default_color_primary_variant" id="0x7f060076" />
    <public type="color" name="design_default_color_secondary" id="0x7f060077" />
    <public type="color" name="design_default_color_secondary_variant" id="0x7f060078" />
    <public type="color" name="design_default_color_surface" id="0x7f060079" />
    <public type="color" name="design_error" id="0x7f06007a" />
    <public type="color" name="design_fab_shadow_end_color" id="0x7f06007b" />
    <public type="color" name="design_fab_shadow_mid_color" id="0x7f06007c" />
    <public type="color" name="design_fab_shadow_start_color" id="0x7f06007d" />
    <public type="color" name="design_fab_stroke_end_inner_color" id="0x7f06007e" />
    <public type="color" name="design_fab_stroke_end_outer_color" id="0x7f06007f" />
    <public type="color" name="design_fab_stroke_top_inner_color" id="0x7f060080" />
    <public type="color" name="design_fab_stroke_top_outer_color" id="0x7f060081" />
    <public type="color" name="design_icon_tint" id="0x7f060082" />
    <public type="color" name="design_snackbar_background_color" id="0x7f060083" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f060084" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f060085" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f060086" />
    <public type="color" name="dim_foreground_material_light" id="0x7f060087" />
    <public type="color" name="dimgray" id="0x7f060088" />
    <public type="color" name="dimgrey" id="0x7f060089" />
    <public type="color" name="dodgerblue" id="0x7f06008a" />
    <public type="color" name="eggplant" id="0x7f06008b" />
    <public type="color" name="error_color_material_dark" id="0x7f06008c" />
    <public type="color" name="error_color_material_light" id="0x7f06008d" />
    <public type="color" name="firebrick" id="0x7f06008e" />
    <public type="color" name="floralwhite" id="0x7f06008f" />
    <public type="color" name="foreground_material_dark" id="0x7f060090" />
    <public type="color" name="foreground_material_light" id="0x7f060091" />
    <public type="color" name="forestgreen" id="0x7f060092" />
    <public type="color" name="fuchsia" id="0x7f060093" />
    <public type="color" name="gainsboro" id="0x7f060094" />
    <public type="color" name="ghostwhite" id="0x7f060095" />
    <public type="color" name="gold" id="0x7f060096" />
    <public type="color" name="goldenrod" id="0x7f060097" />
    <public type="color" name="gray" id="0x7f060098" />
    <public type="color" name="green" id="0x7f060099" />
    <public type="color" name="greenyellow" id="0x7f06009a" />
    <public type="color" name="grey" id="0x7f06009b" />
    <public type="color" name="half_black" id="0x7f06009c" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f06009d" />
    <public type="color" name="highlighted_text_material_light" id="0x7f06009e" />
    <public type="color" name="honeydew" id="0x7f06009f" />
    <public type="color" name="hotpink" id="0x7f0600a0" />
    <public type="color" name="indianred" id="0x7f0600a1" />
    <public type="color" name="indigo" id="0x7f0600a2" />
    <public type="color" name="ivory" id="0x7f0600a3" />
    <public type="color" name="khaki" id="0x7f0600a4" />
    <public type="color" name="lavender" id="0x7f0600a5" />
    <public type="color" name="lavenderblush" id="0x7f0600a6" />
    <public type="color" name="lawngreen" id="0x7f0600a7" />
    <public type="color" name="lemonchiffon" id="0x7f0600a8" />
    <public type="color" name="lightblue" id="0x7f0600a9" />
    <public type="color" name="lightcoral" id="0x7f0600aa" />
    <public type="color" name="lightcyan" id="0x7f0600ab" />
    <public type="color" name="lightgoldenrodyellow" id="0x7f0600ac" />
    <public type="color" name="lightgray" id="0x7f0600ad" />
    <public type="color" name="lightgreen" id="0x7f0600ae" />
    <public type="color" name="lightgrey" id="0x7f0600af" />
    <public type="color" name="lightpink" id="0x7f0600b0" />
    <public type="color" name="lightsalmon" id="0x7f0600b1" />
    <public type="color" name="lightseagreen" id="0x7f0600b2" />
    <public type="color" name="lightskyblue" id="0x7f0600b3" />
    <public type="color" name="lightslategray" id="0x7f0600b4" />
    <public type="color" name="lightslategrey" id="0x7f0600b5" />
    <public type="color" name="lightsteelblue" id="0x7f0600b6" />
    <public type="color" name="lightyellow" id="0x7f0600b7" />
    <public type="color" name="lime" id="0x7f0600b8" />
    <public type="color" name="limegreen" id="0x7f0600b9" />
    <public type="color" name="linen" id="0x7f0600ba" />
    <public type="color" name="magenta" id="0x7f0600bb" />
    <public type="color" name="maroon" id="0x7f0600bc" />
    <public type="color" name="material_blue_grey_800" id="0x7f0600bd" />
    <public type="color" name="material_blue_grey_900" id="0x7f0600be" />
    <public type="color" name="material_blue_grey_950" id="0x7f0600bf" />
    <public type="color" name="material_deep_teal_200" id="0x7f0600c0" />
    <public type="color" name="material_deep_teal_500" id="0x7f0600c1" />
    <public type="color" name="material_grey_100" id="0x7f0600c2" />
    <public type="color" name="material_grey_300" id="0x7f0600c3" />
    <public type="color" name="material_grey_50" id="0x7f0600c4" />
    <public type="color" name="material_grey_600" id="0x7f0600c5" />
    <public type="color" name="material_grey_800" id="0x7f0600c6" />
    <public type="color" name="material_grey_850" id="0x7f0600c7" />
    <public type="color" name="material_grey_900" id="0x7f0600c8" />
    <public type="color" name="material_on_background_disabled" id="0x7f0600c9" />
    <public type="color" name="material_on_background_emphasis_high_type" id="0x7f0600ca" />
    <public type="color" name="material_on_background_emphasis_medium" id="0x7f0600cb" />
    <public type="color" name="material_on_primary_disabled" id="0x7f0600cc" />
    <public type="color" name="material_on_primary_emphasis_high_type" id="0x7f0600cd" />
    <public type="color" name="material_on_primary_emphasis_medium" id="0x7f0600ce" />
    <public type="color" name="material_on_surface_disabled" id="0x7f0600cf" />
    <public type="color" name="material_on_surface_emphasis_high_type" id="0x7f0600d0" />
    <public type="color" name="material_on_surface_emphasis_medium" id="0x7f0600d1" />
    <public type="color" name="material_on_surface_stroke" id="0x7f0600d2" />
    <public type="color" name="material_slider_active_tick_marks_color" id="0x7f0600d3" />
    <public type="color" name="material_slider_active_track_color" id="0x7f0600d4" />
    <public type="color" name="material_slider_halo_color" id="0x7f0600d5" />
    <public type="color" name="material_slider_inactive_tick_marks_color" id="0x7f0600d6" />
    <public type="color" name="material_slider_inactive_track_color" id="0x7f0600d7" />
    <public type="color" name="material_slider_thumb_color" id="0x7f0600d8" />
    <public type="color" name="mediumaquamarine" id="0x7f0600d9" />
    <public type="color" name="mediumblue" id="0x7f0600da" />
    <public type="color" name="mediumorchid" id="0x7f0600db" />
    <public type="color" name="mediumpurple" id="0x7f0600dc" />
    <public type="color" name="mediumseagreen" id="0x7f0600dd" />
    <public type="color" name="mediumslateblue" id="0x7f0600de" />
    <public type="color" name="mediumspringgreen" id="0x7f0600df" />
    <public type="color" name="mediumturquoise" id="0x7f0600e0" />
    <public type="color" name="mediumvioletred" id="0x7f0600e1" />
    <public type="color" name="midnightblue" id="0x7f0600e2" />
    <public type="color" name="mintcream" id="0x7f0600e3" />
    <public type="color" name="mistyrose" id="0x7f0600e4" />
    <public type="color" name="moccasin" id="0x7f0600e5" />
    <public type="color" name="mtrl_bottom_nav_colored_item_tint" id="0x7f0600e6" />
    <public type="color" name="mtrl_bottom_nav_colored_ripple_color" id="0x7f0600e7" />
    <public type="color" name="mtrl_bottom_nav_item_tint" id="0x7f0600e8" />
    <public type="color" name="mtrl_bottom_nav_ripple_color" id="0x7f0600e9" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f0600ea" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f0600eb" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f0600ec" />
    <public type="color" name="mtrl_btn_text_btn_bg_color_selector" id="0x7f0600ed" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f0600ee" />
    <public type="color" name="mtrl_btn_text_color_disabled" id="0x7f0600ef" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f0600f0" />
    <public type="color" name="mtrl_btn_transparent_bg_color" id="0x7f0600f1" />
    <public type="color" name="mtrl_calendar_item_stroke_color" id="0x7f0600f2" />
    <public type="color" name="mtrl_calendar_selected_range" id="0x7f0600f3" />
    <public type="color" name="mtrl_card_view_foreground" id="0x7f0600f4" />
    <public type="color" name="mtrl_card_view_ripple" id="0x7f0600f5" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f0600f6" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f0600f7" />
    <public type="color" name="mtrl_chip_ripple_color" id="0x7f0600f8" />
    <public type="color" name="mtrl_chip_surface_color" id="0x7f0600f9" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f0600fa" />
    <public type="color" name="mtrl_choice_chip_background_color" id="0x7f0600fb" />
    <public type="color" name="mtrl_choice_chip_ripple_color" id="0x7f0600fc" />
    <public type="color" name="mtrl_choice_chip_text_color" id="0x7f0600fd" />
    <public type="color" name="mtrl_error" id="0x7f0600fe" />
    <public type="color" name="mtrl_fab_bg_color_selector" id="0x7f0600ff" />
    <public type="color" name="mtrl_fab_icon_text_color_selector" id="0x7f060100" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f060101" />
    <public type="color" name="mtrl_filled_background_color" id="0x7f060102" />
    <public type="color" name="mtrl_filled_icon_tint" id="0x7f060103" />
    <public type="color" name="mtrl_filled_stroke_color" id="0x7f060104" />
    <public type="color" name="mtrl_indicator_text_color" id="0x7f060105" />
    <public type="color" name="mtrl_navigation_item_background_color" id="0x7f060106" />
    <public type="color" name="mtrl_navigation_item_icon_tint" id="0x7f060107" />
    <public type="color" name="mtrl_navigation_item_text_color" id="0x7f060108" />
    <public type="color" name="mtrl_on_primary_text_btn_text_color_selector" id="0x7f060109" />
    <public type="color" name="mtrl_outlined_icon_tint" id="0x7f06010a" />
    <public type="color" name="mtrl_outlined_stroke_color" id="0x7f06010b" />
    <public type="color" name="mtrl_popupmenu_overlay_color" id="0x7f06010c" />
    <public type="color" name="mtrl_scrim_color" id="0x7f06010d" />
    <public type="color" name="mtrl_tabs_colored_ripple_color" id="0x7f06010e" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f06010f" />
    <public type="color" name="mtrl_tabs_icon_color_selector_colored" id="0x7f060110" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f060111" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f060112" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f060113" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f060114" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f060115" />
    <public type="color" name="mtrl_textinput_filled_box_default_background_color" id="0x7f060116" />
    <public type="color" name="mtrl_textinput_focused_box_stroke_color" id="0x7f060117" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f060118" />
    <public type="color" name="navajowhite" id="0x7f060119" />
    <public type="color" name="navy" id="0x7f06011a" />
    <public type="color" name="notification_action_color_filter" id="0x7f06011b" />
    <public type="color" name="notification_icon_bg_color" id="0x7f06011c" />
    <public type="color" name="oldlace" id="0x7f06011d" />
    <public type="color" name="olive" id="0x7f06011e" />
    <public type="color" name="olivedrab" id="0x7f06011f" />
    <public type="color" name="orange" id="0x7f060120" />
    <public type="color" name="orangered" id="0x7f060121" />
    <public type="color" name="orchid" id="0x7f060122" />
    <public type="color" name="palegoldenrod" id="0x7f060123" />
    <public type="color" name="palegreen" id="0x7f060124" />
    <public type="color" name="paleturquoise" id="0x7f060125" />
    <public type="color" name="palevioletred" id="0x7f060126" />
    <public type="color" name="papayawhip" id="0x7f060127" />
    <public type="color" name="peachpuff" id="0x7f060128" />
    <public type="color" name="peru" id="0x7f060129" />
    <public type="color" name="pink" id="0x7f06012a" />
    <public type="color" name="plum" id="0x7f06012b" />
    <public type="color" name="powderblue" id="0x7f06012c" />
    <public type="color" name="preference_fallback_accent_color" id="0x7f06012d" />
    <public type="color" name="primary_dark_material_dark" id="0x7f06012e" />
    <public type="color" name="primary_dark_material_light" id="0x7f06012f" />
    <public type="color" name="primary_material_dark" id="0x7f060130" />
    <public type="color" name="primary_material_light" id="0x7f060131" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f060132" />
    <public type="color" name="primary_text_default_material_light" id="0x7f060133" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f060134" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f060135" />
    <public type="color" name="purple" id="0x7f060136" />
    <public type="color" name="radiobutton_themeable_attribute_color" id="0x7f060137" />
    <public type="color" name="red" id="0x7f060138" />
    <public type="color" name="ripple_material_dark" id="0x7f060139" />
    <public type="color" name="ripple_material_light" id="0x7f06013a" />
    <public type="color" name="rosybrown" id="0x7f06013b" />
    <public type="color" name="royalblue" id="0x7f06013c" />
    <public type="color" name="saddlebrown" id="0x7f06013d" />
    <public type="color" name="saffron" id="0x7f06013e" />
    <public type="color" name="salmon" id="0x7f06013f" />
    <public type="color" name="sandybrown" id="0x7f060140" />
    <public type="color" name="seaShell" id="0x7f060141" />
    <public type="color" name="seagreen" id="0x7f060142" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f060143" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f060144" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f060145" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f060146" />
    <public type="color" name="sienna" id="0x7f060147" />
    <public type="color" name="silver" id="0x7f060148" />
    <public type="color" name="skyblue" id="0x7f060149" />
    <public type="color" name="slateblue" id="0x7f06014a" />
    <public type="color" name="slategray" id="0x7f06014b" />
    <public type="color" name="slategrey" id="0x7f06014c" />
    <public type="color" name="snow" id="0x7f06014d" />
    <public type="color" name="springgreen" id="0x7f06014e" />
    <public type="color" name="steelblue" id="0x7f06014f" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f060150" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f060151" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f060152" />
    <public type="color" name="switch_thumb_material_light" id="0x7f060153" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f060154" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f060155" />
    <public type="color" name="tan" id="0x7f060156" />
    <public type="color" name="teal" id="0x7f060157" />
    <public type="color" name="test_mtrl_calendar_day" id="0x7f060158" />
    <public type="color" name="test_mtrl_calendar_day_selected" id="0x7f060159" />
    <public type="color" name="thistle" id="0x7f06015a" />
    <public type="color" name="tomato" id="0x7f06015b" />
    <public type="color" name="tooltip_background_dark" id="0x7f06015c" />
    <public type="color" name="tooltip_background_light" id="0x7f06015d" />
    <public type="color" name="turquoise" id="0x7f06015e" />
    <public type="color" name="violet" id="0x7f06015f" />
    <public type="color" name="wheat" id="0x7f060160" />
    <public type="color" name="white" id="0x7f060161" />
    <public type="color" name="white_pressed" id="0x7f060162" />
    <public type="color" name="whitesmoke" id="0x7f060163" />
    <public type="color" name="yellow" id="0x7f060164" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f070000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f070001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f070002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f070003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f070004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f070005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f070006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f070007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f070008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f070009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f07000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f07000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f07000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f07000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f07000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f07000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f070010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f070011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f070012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f070013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f070014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f070015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f070016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f070017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f070018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f070019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f07001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f07001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f07001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f07001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f07001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f07001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f070020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f070021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f070022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f070023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f070024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f070025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f070026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f070027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f070028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f070029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f07002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f07002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f07002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f07002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f07002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f07002f" />
    <public type="dimen" name="abc_list_item_height_large_material" id="0x7f070030" />
    <public type="dimen" name="abc_list_item_height_material" id="0x7f070031" />
    <public type="dimen" name="abc_list_item_height_small_material" id="0x7f070032" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f070033" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f070034" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f070035" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f070036" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f070037" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f070038" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f070039" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f07003a" />
    <public type="dimen" name="abc_star_big" id="0x7f07003b" />
    <public type="dimen" name="abc_star_medium" id="0x7f07003c" />
    <public type="dimen" name="abc_star_small" id="0x7f07003d" />
    <public type="dimen" name="abc_switch_padding" id="0x7f07003e" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f07003f" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f070040" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f070041" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f070042" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f070043" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f070044" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f070045" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f070046" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f070047" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f070048" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f070049" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f07004a" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f07004b" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f07004c" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f07004d" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f07004e" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f07004f" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f070050" />
    <public type="dimen" name="action_bar_size" id="0x7f070051" />
    <public type="dimen" name="activity_horizontal_margin" id="0x7f070052" />
    <public type="dimen" name="activity_vertical_margin" id="0x7f070053" />
    <public type="dimen" name="appcompat_dialog_background_inset" id="0x7f070054" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f070055" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f070056" />
    <public type="dimen" name="cardview_default_radius" id="0x7f070057" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f070058" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f070059" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f07005a" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f07005b" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f07005c" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f07005d" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f07005e" />
    <public type="dimen" name="default_dimension" id="0x7f07005f" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f070060" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f070061" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f070062" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f070063" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f070064" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f070065" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f070066" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f070067" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f070068" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f070069" />
    <public type="dimen" name="design_bottom_navigation_shadow_height" id="0x7f07006a" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f07006b" />
    <public type="dimen" name="design_bottom_sheet_elevation" id="0x7f07006c" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f07006d" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f07006e" />
    <public type="dimen" name="design_fab_border_width" id="0x7f07006f" />
    <public type="dimen" name="design_fab_elevation" id="0x7f070070" />
    <public type="dimen" name="design_fab_image_size" id="0x7f070071" />
    <public type="dimen" name="design_fab_size_mini" id="0x7f070072" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f070073" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f070074" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f070075" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f070076" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f070077" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f070078" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f070079" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f07007a" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f07007b" />
    <public type="dimen" name="design_navigation_padding_bottom" id="0x7f07007c" />
    <public type="dimen" name="design_navigation_separator_vertical_padding" id="0x7f07007d" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f07007e" />
    <public type="dimen" name="design_snackbar_action_text_color_alpha" id="0x7f07007f" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f070080" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f070081" />
    <public type="dimen" name="design_snackbar_extra_spacing_horizontal" id="0x7f070082" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f070083" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f070084" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f070085" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f070086" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f070087" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f070088" />
    <public type="dimen" name="design_tab_max_width" id="0x7f070089" />
    <public type="dimen" name="design_tab_scrollable_min_width" id="0x7f07008a" />
    <public type="dimen" name="design_tab_text_size" id="0x7f07008b" />
    <public type="dimen" name="design_tab_text_size_2line" id="0x7f07008c" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f07008d" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f07008e" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f07008f" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f070090" />
    <public type="dimen" name="fastscroll_margin" id="0x7f070091" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f070092" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f070093" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f070094" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f070095" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f070096" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f070097" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f070098" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f070099" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f07009a" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f07009b" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f07009c" />
    <public type="dimen" name="joystick_btn_height" id="0x7f07009d" />
    <public type="dimen" name="joystick_btn_width" id="0x7f07009e" />
    <public type="dimen" name="joystick_ctrl_btn_height" id="0x7f07009f" />
    <public type="dimen" name="joystick_ctrl_btn_width" id="0x7f0700a0" />
    <public type="dimen" name="joystick_rockerview_height" id="0x7f0700a1" />
    <public type="dimen" name="joystick_rockerview_width" id="0x7f0700a2" />
    <public type="dimen" name="material_emphasis_disabled" id="0x7f0700a3" />
    <public type="dimen" name="material_emphasis_high_type" id="0x7f0700a4" />
    <public type="dimen" name="material_emphasis_medium" id="0x7f0700a5" />
    <public type="dimen" name="material_text_view_test_line_height" id="0x7f0700a6" />
    <public type="dimen" name="material_text_view_test_line_height_override" id="0x7f0700a7" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_bottom" id="0x7f0700a8" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_end" id="0x7f0700a9" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_start" id="0x7f0700aa" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_top" id="0x7f0700ab" />
    <public type="dimen" name="mtrl_alert_dialog_picker_background_inset" id="0x7f0700ac" />
    <public type="dimen" name="mtrl_badge_horizontal_edge_offset" id="0x7f0700ad" />
    <public type="dimen" name="mtrl_badge_long_text_horizontal_padding" id="0x7f0700ae" />
    <public type="dimen" name="mtrl_badge_radius" id="0x7f0700af" />
    <public type="dimen" name="mtrl_badge_text_horizontal_edge_offset" id="0x7f0700b0" />
    <public type="dimen" name="mtrl_badge_text_size" id="0x7f0700b1" />
    <public type="dimen" name="mtrl_badge_with_text_radius" id="0x7f0700b2" />
    <public type="dimen" name="mtrl_bottomappbar_fabOffsetEndMode" id="0x7f0700b3" />
    <public type="dimen" name="mtrl_bottomappbar_fab_bottom_margin" id="0x7f0700b4" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f0700b5" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f0700b6" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f0700b7" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f0700b8" />
    <public type="dimen" name="mtrl_btn_corner_radius" id="0x7f0700b9" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f0700ba" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f0700bb" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f0700bc" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f0700bd" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f0700be" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f0700bf" />
    <public type="dimen" name="mtrl_btn_icon_btn_padding_left" id="0x7f0700c0" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f0700c1" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f0700c2" />
    <public type="dimen" name="mtrl_btn_letter_spacing" id="0x7f0700c3" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f0700c4" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f0700c5" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f0700c6" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f0700c7" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f0700c8" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f0700c9" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f0700ca" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f0700cb" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f0700cc" />
    <public type="dimen" name="mtrl_btn_text_size" id="0x7f0700cd" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f0700ce" />
    <public type="dimen" name="mtrl_calendar_action_height" id="0x7f0700cf" />
    <public type="dimen" name="mtrl_calendar_action_padding" id="0x7f0700d0" />
    <public type="dimen" name="mtrl_calendar_bottom_padding" id="0x7f0700d1" />
    <public type="dimen" name="mtrl_calendar_content_padding" id="0x7f0700d2" />
    <public type="dimen" name="mtrl_calendar_day_corner" id="0x7f0700d3" />
    <public type="dimen" name="mtrl_calendar_day_height" id="0x7f0700d4" />
    <public type="dimen" name="mtrl_calendar_day_horizontal_padding" id="0x7f0700d5" />
    <public type="dimen" name="mtrl_calendar_day_today_stroke" id="0x7f0700d6" />
    <public type="dimen" name="mtrl_calendar_day_vertical_padding" id="0x7f0700d7" />
    <public type="dimen" name="mtrl_calendar_day_width" id="0x7f0700d8" />
    <public type="dimen" name="mtrl_calendar_days_of_week_height" id="0x7f0700d9" />
    <public type="dimen" name="mtrl_calendar_dialog_background_inset" id="0x7f0700da" />
    <public type="dimen" name="mtrl_calendar_header_content_padding" id="0x7f0700db" />
    <public type="dimen" name="mtrl_calendar_header_content_padding_fullscreen" id="0x7f0700dc" />
    <public type="dimen" name="mtrl_calendar_header_divider_thickness" id="0x7f0700dd" />
    <public type="dimen" name="mtrl_calendar_header_height" id="0x7f0700de" />
    <public type="dimen" name="mtrl_calendar_header_height_fullscreen" id="0x7f0700df" />
    <public type="dimen" name="mtrl_calendar_header_selection_line_height" id="0x7f0700e0" />
    <public type="dimen" name="mtrl_calendar_header_text_padding" id="0x7f0700e1" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_bottom" id="0x7f0700e2" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_top" id="0x7f0700e3" />
    <public type="dimen" name="mtrl_calendar_landscape_header_width" id="0x7f0700e4" />
    <public type="dimen" name="mtrl_calendar_maximum_default_fullscreen_minor_axis" id="0x7f0700e5" />
    <public type="dimen" name="mtrl_calendar_month_horizontal_padding" id="0x7f0700e6" />
    <public type="dimen" name="mtrl_calendar_month_vertical_padding" id="0x7f0700e7" />
    <public type="dimen" name="mtrl_calendar_navigation_bottom_padding" id="0x7f0700e8" />
    <public type="dimen" name="mtrl_calendar_navigation_height" id="0x7f0700e9" />
    <public type="dimen" name="mtrl_calendar_navigation_top_padding" id="0x7f0700ea" />
    <public type="dimen" name="mtrl_calendar_pre_l_text_clip_padding" id="0x7f0700eb" />
    <public type="dimen" name="mtrl_calendar_selection_baseline_to_top_fullscreen" id="0x7f0700ec" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom" id="0x7f0700ed" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen" id="0x7f0700ee" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_top" id="0x7f0700ef" />
    <public type="dimen" name="mtrl_calendar_text_input_padding_top" id="0x7f0700f0" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top" id="0x7f0700f1" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top_fullscreen" id="0x7f0700f2" />
    <public type="dimen" name="mtrl_calendar_year_corner" id="0x7f0700f3" />
    <public type="dimen" name="mtrl_calendar_year_height" id="0x7f0700f4" />
    <public type="dimen" name="mtrl_calendar_year_horizontal_padding" id="0x7f0700f5" />
    <public type="dimen" name="mtrl_calendar_year_vertical_padding" id="0x7f0700f6" />
    <public type="dimen" name="mtrl_calendar_year_width" id="0x7f0700f7" />
    <public type="dimen" name="mtrl_card_checked_icon_margin" id="0x7f0700f8" />
    <public type="dimen" name="mtrl_card_checked_icon_size" id="0x7f0700f9" />
    <public type="dimen" name="mtrl_card_corner_radius" id="0x7f0700fa" />
    <public type="dimen" name="mtrl_card_dragged_z" id="0x7f0700fb" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f0700fc" />
    <public type="dimen" name="mtrl_card_spacing" id="0x7f0700fd" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f0700fe" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f0700ff" />
    <public type="dimen" name="mtrl_edittext_rectangle_top_offset" id="0x7f070100" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_elevation" id="0x7f070101" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_offset" id="0x7f070102" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_padding" id="0x7f070103" />
    <public type="dimen" name="mtrl_extended_fab_bottom_padding" id="0x7f070104" />
    <public type="dimen" name="mtrl_extended_fab_corner_radius" id="0x7f070105" />
    <public type="dimen" name="mtrl_extended_fab_disabled_elevation" id="0x7f070106" />
    <public type="dimen" name="mtrl_extended_fab_disabled_translation_z" id="0x7f070107" />
    <public type="dimen" name="mtrl_extended_fab_elevation" id="0x7f070108" />
    <public type="dimen" name="mtrl_extended_fab_end_padding" id="0x7f070109" />
    <public type="dimen" name="mtrl_extended_fab_end_padding_icon" id="0x7f07010a" />
    <public type="dimen" name="mtrl_extended_fab_icon_size" id="0x7f07010b" />
    <public type="dimen" name="mtrl_extended_fab_icon_text_spacing" id="0x7f07010c" />
    <public type="dimen" name="mtrl_extended_fab_min_height" id="0x7f07010d" />
    <public type="dimen" name="mtrl_extended_fab_min_width" id="0x7f07010e" />
    <public type="dimen" name="mtrl_extended_fab_start_padding" id="0x7f07010f" />
    <public type="dimen" name="mtrl_extended_fab_start_padding_icon" id="0x7f070110" />
    <public type="dimen" name="mtrl_extended_fab_top_padding" id="0x7f070111" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_base" id="0x7f070112" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_hovered_focused" id="0x7f070113" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_pressed" id="0x7f070114" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f070115" />
    <public type="dimen" name="mtrl_fab_min_touch_target" id="0x7f070116" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f070117" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f070118" />
    <public type="dimen" name="mtrl_high_ripple_default_alpha" id="0x7f070119" />
    <public type="dimen" name="mtrl_high_ripple_focused_alpha" id="0x7f07011a" />
    <public type="dimen" name="mtrl_high_ripple_hovered_alpha" id="0x7f07011b" />
    <public type="dimen" name="mtrl_high_ripple_pressed_alpha" id="0x7f07011c" />
    <public type="dimen" name="mtrl_large_touch_target" id="0x7f07011d" />
    <public type="dimen" name="mtrl_low_ripple_default_alpha" id="0x7f07011e" />
    <public type="dimen" name="mtrl_low_ripple_focused_alpha" id="0x7f07011f" />
    <public type="dimen" name="mtrl_low_ripple_hovered_alpha" id="0x7f070120" />
    <public type="dimen" name="mtrl_low_ripple_pressed_alpha" id="0x7f070121" />
    <public type="dimen" name="mtrl_min_touch_target_size" id="0x7f070122" />
    <public type="dimen" name="mtrl_navigation_elevation" id="0x7f070123" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f070124" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f070125" />
    <public type="dimen" name="mtrl_navigation_item_icon_size" id="0x7f070126" />
    <public type="dimen" name="mtrl_navigation_item_shape_horizontal_margin" id="0x7f070127" />
    <public type="dimen" name="mtrl_navigation_item_shape_vertical_margin" id="0x7f070128" />
    <public type="dimen" name="mtrl_shape_corner_size_large_component" id="0x7f070129" />
    <public type="dimen" name="mtrl_shape_corner_size_medium_component" id="0x7f07012a" />
    <public type="dimen" name="mtrl_shape_corner_size_small_component" id="0x7f07012b" />
    <public type="dimen" name="mtrl_slider_halo_radius" id="0x7f07012c" />
    <public type="dimen" name="mtrl_slider_label_padding" id="0x7f07012d" />
    <public type="dimen" name="mtrl_slider_label_radius" id="0x7f07012e" />
    <public type="dimen" name="mtrl_slider_label_square_side" id="0x7f07012f" />
    <public type="dimen" name="mtrl_slider_thumb_elevation" id="0x7f070130" />
    <public type="dimen" name="mtrl_slider_thumb_radius" id="0x7f070131" />
    <public type="dimen" name="mtrl_slider_track_height" id="0x7f070132" />
    <public type="dimen" name="mtrl_slider_track_side_padding" id="0x7f070133" />
    <public type="dimen" name="mtrl_slider_track_top" id="0x7f070134" />
    <public type="dimen" name="mtrl_slider_widget_height" id="0x7f070135" />
    <public type="dimen" name="mtrl_snackbar_action_text_color_alpha" id="0x7f070136" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f070137" />
    <public type="dimen" name="mtrl_snackbar_background_overlay_color_alpha" id="0x7f070138" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f070139" />
    <public type="dimen" name="mtrl_switch_thumb_elevation" id="0x7f07013a" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_medium" id="0x7f07013b" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f07013c" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f07013d" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f07013e" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f07013f" />
    <public type="dimen" name="mtrl_textinput_counter_margin_start" id="0x7f070140" />
    <public type="dimen" name="mtrl_textinput_end_icon_margin_start" id="0x7f070141" />
    <public type="dimen" name="mtrl_textinput_outline_box_expanded_padding" id="0x7f070142" />
    <public type="dimen" name="mtrl_textinput_start_icon_margin_end" id="0x7f070143" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f070144" />
    <public type="dimen" name="mtrl_tooltip_arrowSize" id="0x7f070145" />
    <public type="dimen" name="mtrl_tooltip_cornerSize" id="0x7f070146" />
    <public type="dimen" name="mtrl_tooltip_minHeight" id="0x7f070147" />
    <public type="dimen" name="mtrl_tooltip_minWidth" id="0x7f070148" />
    <public type="dimen" name="mtrl_tooltip_padding" id="0x7f070149" />
    <public type="dimen" name="mtrl_transition_shared_axis_slide_distance" id="0x7f07014a" />
    <public type="dimen" name="nav_header_height" id="0x7f07014b" />
    <public type="dimen" name="nav_header_vertical_spacing" id="0x7f07014c" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f07014d" />
    <public type="dimen" name="notification_action_text_size" id="0x7f07014e" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f07014f" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f070150" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f070151" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f070152" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f070153" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f070154" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f070155" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f070156" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f070157" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f070158" />
    <public type="dimen" name="notification_subtext_size" id="0x7f070159" />
    <public type="dimen" name="notification_top_pad" id="0x7f07015a" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f07015b" />
    <public type="dimen" name="preference_dropdown_padding_start" id="0x7f07015c" />
    <public type="dimen" name="preference_icon_minWidth" id="0x7f07015d" />
    <public type="dimen" name="preference_seekbar_padding_horizontal" id="0x7f07015e" />
    <public type="dimen" name="preference_seekbar_padding_vertical" id="0x7f07015f" />
    <public type="dimen" name="preference_seekbar_value_minWidth" id="0x7f070160" />
    <public type="dimen" name="preferences_detail_width" id="0x7f070161" />
    <public type="dimen" name="preferences_header_width" id="0x7f070162" />
    <public type="dimen" name="test_mtrl_calendar_day_cornerSize" id="0x7f070163" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f070164" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f070165" />
    <public type="dimen" name="tooltip_margin" id="0x7f070166" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f070167" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f070168" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f070169" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f07016a" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f07016b" />
    <public type="drawable" name="$avd_hide_password__0" id="0x7f080000" />
    <public type="drawable" name="$avd_hide_password__1" id="0x7f080001" />
    <public type="drawable" name="$avd_hide_password__2" id="0x7f080002" />
    <public type="drawable" name="$avd_show_password__0" id="0x7f080003" />
    <public type="drawable" name="$avd_show_password__1" id="0x7f080004" />
    <public type="drawable" name="$avd_show_password__2" id="0x7f080005" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f080006" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f080007" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f080008" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f080009" />
    <public type="drawable" name="abc_btn_check_material_anim" id="0x7f08000a" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f08000b" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f08000c" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f08000d" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f08000e" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f08000f" />
    <public type="drawable" name="abc_btn_radio_material_anim" id="0x7f080010" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f080011" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f080012" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f080013" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f080014" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f080015" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f080016" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f080017" />
    <public type="drawable" name="abc_control_background_material" id="0x7f080018" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f080019" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f08001a" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f08001b" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f08001c" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f08001d" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f08001e" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f08001f" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f080020" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f080021" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f080022" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f080023" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f080024" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f080025" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f080026" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f080027" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f080028" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f080029" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f08002a" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f08002b" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f08002c" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f08002d" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f08002e" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f08002f" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f080030" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f080031" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f080032" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f080033" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f080034" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f080035" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f080036" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f080037" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f080038" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f080039" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f08003a" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f08003b" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f08003c" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f08003d" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f08003e" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f08003f" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f080040" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f080041" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f080042" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f080043" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f080044" />
    <public type="drawable" name="abc_star_black_48dp" id="0x7f080045" />
    <public type="drawable" name="abc_star_half_black_48dp" id="0x7f080046" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f080047" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f080048" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f080049" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f08004a" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f08004b" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl" id="0x7f08004c" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl" id="0x7f08004d" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl" id="0x7f08004e" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f08004f" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f080050" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f080051" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f080052" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f080053" />
    <public type="drawable" name="abc_vector_test" id="0x7f080054" />
    <public type="drawable" name="avd_hide_password" id="0x7f080055" />
    <public type="drawable" name="avd_show_password" id="0x7f080056" />
    <public type="drawable" name="back" id="0x7f080057" />
    <public type="drawable" name="bg_gray" id="0x7f080058" />
    <public type="drawable" name="bg_white" id="0x7f080059" />
    <public type="drawable" name="bg_white2" id="0x7f08005a" />
    <public type="drawable" name="bg_white9" id="0x7f08005b" />
    <public type="drawable" name="border_gray" id="0x7f08005c" />
    <public type="drawable" name="border_window" id="0x7f08005d" />
    <public type="drawable" name="btn_checkbox_checked_mtrl" id="0x7f08005e" />
    <public type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation" id="0x7f08005f" />
    <public type="drawable" name="btn_checkbox_unchecked_mtrl" id="0x7f080060" />
    <public type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation" id="0x7f080061" />
    <public type="drawable" name="btn_radio_off_mtrl" id="0x7f080062" />
    <public type="drawable" name="btn_radio_off_to_on_mtrl_animation" id="0x7f080063" />
    <public type="drawable" name="btn_radio_on_mtrl" id="0x7f080064" />
    <public type="drawable" name="btn_radio_on_to_off_mtrl_animation" id="0x7f080065" />
    <public type="drawable" name="circle_shape_bg" id="0x7f080066" />
    <public type="drawable" name="circle_shape_normal" id="0x7f080067" />
    <public type="drawable" name="circle_shape_pressed" id="0x7f080068" />
    <public type="drawable" name="design_bottom_navigation_item_background" id="0x7f080069" />
    <public type="drawable" name="design_fab_background" id="0x7f08006a" />
    <public type="drawable" name="design_ic_visibility" id="0x7f08006b" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f08006c" />
    <public type="drawable" name="design_password_eye" id="0x7f08006d" />
    <public type="drawable" name="design_snackbar_background" id="0x7f08006e" />
    <public type="drawable" name="empty" id="0x7f08006f" />
    <public type="drawable" name="ic_arrow_down_24dp" id="0x7f080070" />
    <public type="drawable" name="ic_bike" id="0x7f080071" />
    <public type="drawable" name="ic_close" id="0x7f080072" />
    <public type="drawable" name="ic_contact" id="0x7f080073" />
    <public type="drawable" name="ic_copy" id="0x7f080074" />
    <public type="drawable" name="ic_date" id="0x7f080075" />
    <public type="drawable" name="ic_delete" id="0x7f080076" />
    <public type="drawable" name="ic_down" id="0x7f080077" />
    <public type="drawable" name="ic_fly" id="0x7f080078" />
    <public type="drawable" name="ic_history" id="0x7f080079" />
    <public type="drawable" name="ic_home_position" id="0x7f08007a" />
    <public type="drawable" name="ic_input" id="0x7f08007b" />
    <public type="drawable" name="ic_key" id="0x7f08007c" />
    <public type="drawable" name="ic_launcher_background" id="0x7f08007d" />
    <public type="drawable" name="ic_launcher_foreground" id="0x7f08007e" />
    <public type="drawable" name="ic_leading_in" id="0x7f08007f" />
    <public type="drawable" name="ic_leading_out" id="0x7f080080" />
    <public type="drawable" name="ic_left" id="0x7f080081" />
    <public type="drawable" name="ic_left_down" id="0x7f080082" />
    <public type="drawable" name="ic_left_up" id="0x7f080083" />
    <public type="drawable" name="ic_lock_close" id="0x7f080084" />
    <public type="drawable" name="ic_lock_open" id="0x7f080085" />
    <public type="drawable" name="ic_map" id="0x7f080086" />
    <public type="drawable" name="ic_menu_dev" id="0x7f080087" />
    <public type="drawable" name="ic_menu_feedback" id="0x7f080088" />
    <public type="drawable" name="ic_menu_history" id="0x7f080089" />
    <public type="drawable" name="ic_menu_settings" id="0x7f08008a" />
    <public type="drawable" name="ic_menu_upgrade" id="0x7f08008b" />
    <public type="drawable" name="ic_move" id="0x7f08008c" />
    <public type="drawable" name="ic_msg" id="0x7f08008d" />
    <public type="drawable" name="ic_mtrl_checked_circle" id="0x7f08008e" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f08008f" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f080090" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f080091" />
    <public type="drawable" name="ic_position" id="0x7f080092" />
    <public type="drawable" name="ic_pwd" id="0x7f080093" />
    <public type="drawable" name="ic_right" id="0x7f080094" />
    <public type="drawable" name="ic_right_down" id="0x7f080095" />
    <public type="drawable" name="ic_right_up" id="0x7f080096" />
    <public type="drawable" name="ic_run" id="0x7f080097" />
    <public type="drawable" name="ic_save" id="0x7f080098" />
    <public type="drawable" name="ic_search" id="0x7f080099" />
    <public type="drawable" name="ic_share" id="0x7f08009a" />
    <public type="drawable" name="ic_up" id="0x7f08009b" />
    <public type="drawable" name="ic_user" id="0x7f08009c" />
    <public type="drawable" name="ic_walk" id="0x7f08009d" />
    <public type="drawable" name="ic_zoom_in" id="0x7f08009e" />
    <public type="drawable" name="ic_zoom_out" id="0x7f08009f" />
    <public type="drawable" name="icon_gcoding" id="0x7f0800a0" />
    <public type="drawable" name="material_ic_calendar_black_24dp" id="0x7f0800a1" />
    <public type="drawable" name="material_ic_clear_black_24dp" id="0x7f0800a2" />
    <public type="drawable" name="material_ic_edit_black_24dp" id="0x7f0800a3" />
    <public type="drawable" name="material_ic_keyboard_arrow_left_black_24dp" id="0x7f0800a4" />
    <public type="drawable" name="material_ic_keyboard_arrow_right_black_24dp" id="0x7f0800a5" />
    <public type="drawable" name="material_ic_menu_arrow_down_black_24dp" id="0x7f0800a6" />
    <public type="drawable" name="material_ic_menu_arrow_up_black_24dp" id="0x7f0800a7" />
    <public type="drawable" name="mtrl_dialog_background" id="0x7f0800a8" />
    <public type="drawable" name="mtrl_dropdown_arrow" id="0x7f0800a9" />
    <public type="drawable" name="mtrl_ic_arrow_drop_down" id="0x7f0800aa" />
    <public type="drawable" name="mtrl_ic_arrow_drop_up" id="0x7f0800ab" />
    <public type="drawable" name="mtrl_ic_cancel" id="0x7f0800ac" />
    <public type="drawable" name="mtrl_ic_error" id="0x7f0800ad" />
    <public type="drawable" name="mtrl_popupmenu_background" id="0x7f0800ae" />
    <public type="drawable" name="mtrl_popupmenu_background_dark" id="0x7f0800af" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f0800b0" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f0800b1" />
    <public type="drawable" name="notification_action_background" id="0x7f0800b2" />
    <public type="drawable" name="notification_bg" id="0x7f0800b3" />
    <public type="drawable" name="notification_bg_low" id="0x7f0800b4" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f0800b5" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f0800b6" />
    <public type="drawable" name="notification_bg_normal" id="0x7f0800b7" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f0800b8" />
    <public type="drawable" name="notification_icon_background" id="0x7f0800b9" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f0800ba" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f0800bb" />
    <public type="drawable" name="notification_tile_bg" id="0x7f0800bc" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f0800bd" />
    <public type="drawable" name="position" id="0x7f0800be" />
    <public type="drawable" name="preference_list_divider_material" id="0x7f0800bf" />
    <public type="drawable" name="sel_guide_btn6" id="0x7f0800c0" />
    <public type="drawable" name="sel_guide_btn7" id="0x7f0800c1" />
    <public type="drawable" name="sel_orange_btn7" id="0x7f0800c2" />
    <public type="drawable" name="side_nav_bar" id="0x7f0800c3" />
    <public type="drawable" name="test_custom_background" id="0x7f0800c4" />
    <public type="drawable" name="test_level_drawable" id="0x7f0800c5" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f0800c6" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f0800c7" />
    <public type="drawable" name="welcome" id="0x7f0800c8" />
    <public type="drawable" name="white_tran" id="0x7f0800c9" />
    <public type="id" name="ALT" id="0x7f090000" />
    <public type="id" name="BDLatLngText" id="0x7f090001" />
    <public type="id" name="BOTTOM_END" id="0x7f090002" />
    <public type="id" name="BOTTOM_START" id="0x7f090003" />
    <public type="id" name="CTRL" id="0x7f090004" />
    <public type="id" name="FUNCTION" id="0x7f090005" />
    <public type="id" name="LocationID" id="0x7f090006" />
    <public type="id" name="LocationText" id="0x7f090007" />
    <public type="id" name="META" id="0x7f090008" />
    <public type="id" name="NO_DEBUG" id="0x7f090009" />
    <public type="id" name="RadioGroupMapType" id="0x7f09000a" />
    <public type="id" name="SHIFT" id="0x7f09000b" />
    <public type="id" name="SHOW_ALL" id="0x7f09000c" />
    <public type="id" name="SHOW_PATH" id="0x7f09000d" />
    <public type="id" name="SHOW_PROGRESS" id="0x7f09000e" />
    <public type="id" name="SYM" id="0x7f09000f" />
    <public type="id" name="TOP_END" id="0x7f090010" />
    <public type="id" name="TOP_START" id="0x7f090011" />
    <public type="id" name="TimeText" id="0x7f090012" />
    <public type="id" name="WGSLatLngText" id="0x7f090013" />
    <public type="id" name="accelerate" id="0x7f090014" />
    <public type="id" name="accessibility_action_clickable_span" id="0x7f090015" />
    <public type="id" name="accessibility_custom_action_0" id="0x7f090016" />
    <public type="id" name="accessibility_custom_action_1" id="0x7f090017" />
    <public type="id" name="accessibility_custom_action_10" id="0x7f090018" />
    <public type="id" name="accessibility_custom_action_11" id="0x7f090019" />
    <public type="id" name="accessibility_custom_action_12" id="0x7f09001a" />
    <public type="id" name="accessibility_custom_action_13" id="0x7f09001b" />
    <public type="id" name="accessibility_custom_action_14" id="0x7f09001c" />
    <public type="id" name="accessibility_custom_action_15" id="0x7f09001d" />
    <public type="id" name="accessibility_custom_action_16" id="0x7f09001e" />
    <public type="id" name="accessibility_custom_action_17" id="0x7f09001f" />
    <public type="id" name="accessibility_custom_action_18" id="0x7f090020" />
    <public type="id" name="accessibility_custom_action_19" id="0x7f090021" />
    <public type="id" name="accessibility_custom_action_2" id="0x7f090022" />
    <public type="id" name="accessibility_custom_action_20" id="0x7f090023" />
    <public type="id" name="accessibility_custom_action_21" id="0x7f090024" />
    <public type="id" name="accessibility_custom_action_22" id="0x7f090025" />
    <public type="id" name="accessibility_custom_action_23" id="0x7f090026" />
    <public type="id" name="accessibility_custom_action_24" id="0x7f090027" />
    <public type="id" name="accessibility_custom_action_25" id="0x7f090028" />
    <public type="id" name="accessibility_custom_action_26" id="0x7f090029" />
    <public type="id" name="accessibility_custom_action_27" id="0x7f09002a" />
    <public type="id" name="accessibility_custom_action_28" id="0x7f09002b" />
    <public type="id" name="accessibility_custom_action_29" id="0x7f09002c" />
    <public type="id" name="accessibility_custom_action_3" id="0x7f09002d" />
    <public type="id" name="accessibility_custom_action_30" id="0x7f09002e" />
    <public type="id" name="accessibility_custom_action_31" id="0x7f09002f" />
    <public type="id" name="accessibility_custom_action_4" id="0x7f090030" />
    <public type="id" name="accessibility_custom_action_5" id="0x7f090031" />
    <public type="id" name="accessibility_custom_action_6" id="0x7f090032" />
    <public type="id" name="accessibility_custom_action_7" id="0x7f090033" />
    <public type="id" name="accessibility_custom_action_8" id="0x7f090034" />
    <public type="id" name="accessibility_custom_action_9" id="0x7f090035" />
    <public type="id" name="actionDown" id="0x7f090036" />
    <public type="id" name="actionDownUp" id="0x7f090037" />
    <public type="id" name="actionUp" id="0x7f090038" />
    <public type="id" name="action_bar" id="0x7f090039" />
    <public type="id" name="action_bar_activity_content" id="0x7f09003a" />
    <public type="id" name="action_bar_container" id="0x7f09003b" />
    <public type="id" name="action_bar_root" id="0x7f09003c" />
    <public type="id" name="action_bar_spinner" id="0x7f09003d" />
    <public type="id" name="action_bar_subtitle" id="0x7f09003e" />
    <public type="id" name="action_bar_title" id="0x7f09003f" />
    <public type="id" name="action_container" id="0x7f090040" />
    <public type="id" name="action_context_bar" id="0x7f090041" />
    <public type="id" name="action_delete" id="0x7f090042" />
    <public type="id" name="action_divider" id="0x7f090043" />
    <public type="id" name="action_image" id="0x7f090044" />
    <public type="id" name="action_menu_divider" id="0x7f090045" />
    <public type="id" name="action_menu_presenter" id="0x7f090046" />
    <public type="id" name="action_mode_bar" id="0x7f090047" />
    <public type="id" name="action_mode_bar_stub" id="0x7f090048" />
    <public type="id" name="action_mode_close_button" id="0x7f090049" />
    <public type="id" name="action_search" id="0x7f09004a" />
    <public type="id" name="action_text" id="0x7f09004b" />
    <public type="id" name="actions" id="0x7f09004c" />
    <public type="id" name="activity_chooser_view_content" id="0x7f09004d" />
    <public type="id" name="add" id="0x7f09004e" />
    <public type="id" name="alertTitle" id="0x7f09004f" />
    <public type="id" name="aligned" id="0x7f090050" />
    <public type="id" name="all" id="0x7f090051" />
    <public type="id" name="allStates" id="0x7f090052" />
    <public type="id" name="always" id="0x7f090053" />
    <public type="id" name="androidx_window_activity_scope" id="0x7f090054" />
    <public type="id" name="animateToEnd" id="0x7f090055" />
    <public type="id" name="animateToStart" id="0x7f090056" />
    <public type="id" name="antiClockwise" id="0x7f090057" />
    <public type="id" name="anticipate" id="0x7f090058" />
    <public type="id" name="asConfigured" id="0x7f090059" />
    <public type="id" name="async" id="0x7f09005a" />
    <public type="id" name="auto" id="0x7f09005b" />
    <public type="id" name="autoComplete" id="0x7f09005c" />
    <public type="id" name="autoCompleteToEnd" id="0x7f09005d" />
    <public type="id" name="autoCompleteToStart" id="0x7f09005e" />
    <public type="id" name="backioc" id="0x7f09005f" />
    <public type="id" name="baohuimg" id="0x7f090060" />
    <public type="id" name="baohutv" id="0x7f090061" />
    <public type="id" name="barrier" id="0x7f090062" />
    <public type="id" name="baseline" id="0x7f090063" />
    <public type="id" name="bdMapView" id="0x7f090064" />
    <public type="id" name="beginOnFirstDraw" id="0x7f090065" />
    <public type="id" name="beginning" id="0x7f090066" />
    <public type="id" name="bestChoice" id="0x7f090067" />
    <public type="id" name="bianjiimg" id="0x7f090068" />
    <public type="id" name="blocking" id="0x7f090069" />
    <public type="id" name="bmapView" id="0x7f09006a" />
    <public type="id" name="bottom" id="0x7f09006b" />
    <public type="id" name="bounce" id="0x7f09006c" />
    <public type="id" name="bounceBoth" id="0x7f09006d" />
    <public type="id" name="bounceEnd" id="0x7f09006e" />
    <public type="id" name="bounceStart" id="0x7f09006f" />
    <public type="id" name="btnBack" id="0x7f090070" />
    <public type="id" name="btnGo" id="0x7f090071" />
    <public type="id" name="btn_center" id="0x7f090072" />
    <public type="id" name="btn_east" id="0x7f090073" />
    <public type="id" name="btn_north" id="0x7f090074" />
    <public type="id" name="btn_north_east" id="0x7f090075" />
    <public type="id" name="btn_north_west" id="0x7f090076" />
    <public type="id" name="btn_south" id="0x7f090077" />
    <public type="id" name="btn_south_east" id="0x7f090078" />
    <public type="id" name="btn_south_west" id="0x7f090079" />
    <public type="id" name="btn_west" id="0x7f09007a" />
    <public type="id" name="buttonPanel" id="0x7f09007b" />
    <public type="id" name="cache_measures" id="0x7f09007c" />
    <public type="id" name="callMeasure" id="0x7f09007d" />
    <public type="id" name="cancel_button" id="0x7f09007e" />
    <public type="id" name="carryVelocity" id="0x7f09007f" />
    <public type="id" name="center" id="0x7f090080" />
    <public type="id" name="center_horizontal" id="0x7f090081" />
    <public type="id" name="center_vertical" id="0x7f090082" />
    <public type="id" name="chain" id="0x7f090083" />
    <public type="id" name="chain2" id="0x7f090084" />
    <public type="id" name="chains" id="0x7f090085" />
    <public type="id" name="checkbox" id="0x7f090086" />
    <public type="id" name="checked" id="0x7f090087" />
    <public type="id" name="chip" id="0x7f090088" />
    <public type="id" name="chip1" id="0x7f090089" />
    <public type="id" name="chip2" id="0x7f09008a" />
    <public type="id" name="chip3" id="0x7f09008b" />
    <public type="id" name="chip_group" id="0x7f09008c" />
    <public type="id" name="chronometer" id="0x7f09008d" />
    <public type="id" name="city" id="0x7f09008e" />
    <public type="id" name="clPosition" id="0x7f09008f" />
    <public type="id" name="clear_text" id="0x7f090090" />
    <public type="id" name="clip_horizontal" id="0x7f090091" />
    <public type="id" name="clip_vertical" id="0x7f090092" />
    <public type="id" name="clockwise" id="0x7f090093" />
    <public type="id" name="closest" id="0x7f090094" />
    <public type="id" name="collapseActionView" id="0x7f090095" />
    <public type="id" name="confirm_button" id="0x7f090096" />
    <public type="id" name="constraint" id="0x7f090097" />
    <public type="id" name="container" id="0x7f090098" />
    <public type="id" name="content" id="0x7f090099" />
    <public type="id" name="contentPanel" id="0x7f09009a" />
    <public type="id" name="continuousVelocity" id="0x7f09009b" />
    <public type="id" name="coordinator" id="0x7f09009c" />
    <public type="id" name="cos" id="0x7f09009d" />
    <public type="id" name="cur_position" id="0x7f09009e" />
    <public type="id" name="currentState" id="0x7f09009f" />
    <public type="id" name="custom" id="0x7f0900a0" />
    <public type="id" name="customPanel" id="0x7f0900a1" />
    <public type="id" name="cut" id="0x7f0900a2" />
    <public type="id" name="date_picker" id="0x7f0900a3" />
    <public type="id" name="date_picker_actions" id="0x7f0900a4" />
    <public type="id" name="decelerate" id="0x7f0900a5" />
    <public type="id" name="decelerateAndComplete" id="0x7f0900a6" />
    <public type="id" name="decor_content_parent" id="0x7f0900a7" />
    <public type="id" name="default_activity_button" id="0x7f0900a8" />
    <public type="id" name="deltaRelative" id="0x7f0900a9" />
    <public type="id" name="dependency_ordering" id="0x7f0900aa" />
    <public type="id" name="design_bottom_sheet" id="0x7f0900ab" />
    <public type="id" name="design_menu_item_action_area" id="0x7f0900ac" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f0900ad" />
    <public type="id" name="design_menu_item_text" id="0x7f0900ae" />
    <public type="id" name="design_navigation_view" id="0x7f0900af" />
    <public type="id" name="dialog_button" id="0x7f0900b0" />
    <public type="id" name="dimensions" id="0x7f0900b1" />
    <public type="id" name="dingweiimg" id="0x7f0900b2" />
    <public type="id" name="direct" id="0x7f0900b3" />
    <public type="id" name="disableHome" id="0x7f0900b4" />
    <public type="id" name="disableIntraAutoTransition" id="0x7f0900b5" />
    <public type="id" name="disablePostScroll" id="0x7f0900b6" />
    <public type="id" name="disableScroll" id="0x7f0900b7" />
    <public type="id" name="dragAnticlockwise" id="0x7f0900b8" />
    <public type="id" name="dragClockwise" id="0x7f0900b9" />
    <public type="id" name="dragDown" id="0x7f0900ba" />
    <public type="id" name="dragEnd" id="0x7f0900bb" />
    <public type="id" name="dragLeft" id="0x7f0900bc" />
    <public type="id" name="dragRight" id="0x7f0900bd" />
    <public type="id" name="dragStart" id="0x7f0900be" />
    <public type="id" name="dragUp" id="0x7f0900bf" />
    <public type="id" name="drawer_layout" id="0x7f0900c0" />
    <public type="id" name="dropdown_menu" id="0x7f0900c1" />
    <public type="id" name="easeIn" id="0x7f0900c2" />
    <public type="id" name="easeInOut" id="0x7f0900c3" />
    <public type="id" name="easeOut" id="0x7f0900c4" />
    <public type="id" name="east" id="0x7f0900c5" />
    <public type="id" name="edit_query" id="0x7f0900c6" />
    <public type="id" name="end" id="0x7f0900c7" />
    <public type="id" name="enterAlways" id="0x7f0900c8" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f0900c9" />
    <public type="id" name="etCode" id="0x7f0900ca" />
    <public type="id" name="et_address" id="0x7f0900cb" />
    <public type="id" name="exitUntilCollapsed" id="0x7f0900cc" />
    <public type="id" name="expand_activities_button" id="0x7f0900cd" />
    <public type="id" name="expanded_menu" id="0x7f0900ce" />
    <public type="id" name="faBtnStart" id="0x7f0900cf" />
    <public type="id" name="fade" id="0x7f0900d0" />
    <public type="id" name="fill" id="0x7f0900d1" />
    <public type="id" name="fill_horizontal" id="0x7f0900d2" />
    <public type="id" name="fill_vertical" id="0x7f0900d3" />
    <public type="id" name="filled" id="0x7f0900d4" />
    <public type="id" name="fitToContents" id="0x7f0900d5" />
    <public type="id" name="fixed" id="0x7f0900d6" />
    <public type="id" name="flip" id="0x7f0900d7" />
    <public type="id" name="floating" id="0x7f0900d8" />
    <public type="id" name="forever" id="0x7f0900d9" />
    <public type="id" name="fragment_container_view_tag" id="0x7f0900da" />
    <public type="id" name="frost" id="0x7f0900db" />
    <public type="id" name="ghost_view" id="0x7f0900dc" />
    <public type="id" name="ghost_view_holder" id="0x7f0900dd" />
    <public type="id" name="gone" id="0x7f0900de" />
    <public type="id" name="graph" id="0x7f0900df" />
    <public type="id" name="graph_wrap" id="0x7f0900e0" />
    <public type="id" name="group_divider" id="0x7f0900e1" />
    <public type="id" name="grouping" id="0x7f0900e2" />
    <public type="id" name="groups" id="0x7f0900e3" />
    <public type="id" name="hideable" id="0x7f0900e4" />
    <public type="id" name="home" id="0x7f0900e5" />
    <public type="id" name="homeAsUp" id="0x7f0900e6" />
    <public type="id" name="honorRequest" id="0x7f0900e7" />
    <public type="id" name="horizontal_only" id="0x7f0900e8" />
    <public type="id" name="icon" id="0x7f0900e9" />
    <public type="id" name="icon_frame" id="0x7f0900ea" />
    <public type="id" name="icon_group" id="0x7f0900eb" />
    <public type="id" name="ifRoom" id="0x7f0900ec" />
    <public type="id" name="ignore" id="0x7f0900ed" />
    <public type="id" name="ignoreRequest" id="0x7f0900ee" />
    <public type="id" name="image" id="0x7f0900ef" />
    <public type="id" name="immediateStop" id="0x7f0900f0" />
    <public type="id" name="included" id="0x7f0900f1" />
    <public type="id" name="info" id="0x7f0900f2" />
    <public type="id" name="input_pos" id="0x7f0900f3" />
    <public type="id" name="input_position_cancel" id="0x7f0900f4" />
    <public type="id" name="input_position_ok" id="0x7f0900f5" />
    <public type="id" name="invisible" id="0x7f0900f6" />
    <public type="id" name="italic" id="0x7f0900f7" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f0900f8" />
    <public type="id" name="ivBack" id="0x7f0900f9" />
    <public type="id" name="ivLogo" id="0x7f0900fa" />
    <public type="id" name="joystick_his_close" id="0x7f0900fb" />
    <public type="id" name="joystick_his_record_list_view" id="0x7f0900fc" />
    <public type="id" name="joystick_his_record_no_textview" id="0x7f0900fd" />
    <public type="id" name="joystick_his_searchView" id="0x7f0900fe" />
    <public type="id" name="joystick_his_tips" id="0x7f0900ff" />
    <public type="id" name="joystick_latitude" id="0x7f090100" />
    <public type="id" name="joystick_longitude" id="0x7f090101" />
    <public type="id" name="joystick_map_searchView" id="0x7f090102" />
    <public type="id" name="joystick_map_tips" id="0x7f090103" />
    <public type="id" name="jumpToEnd" id="0x7f090104" />
    <public type="id" name="jumpToStart" id="0x7f090105" />
    <public type="id" name="labeled" id="0x7f090106" />
    <public type="id" name="largeLabel" id="0x7f090107" />
    <public type="id" name="layout" id="0x7f090108" />
    <public type="id" name="left" id="0x7f090109" />
    <public type="id" name="legacy" id="0x7f09010a" />
    <public type="id" name="line" id="0x7f09010b" />
    <public type="id" name="line1" id="0x7f09010c" />
    <public type="id" name="line3" id="0x7f09010d" />
    <public type="id" name="linear" id="0x7f09010e" />
    <public type="id" name="listMode" id="0x7f09010f" />
    <public type="id" name="list_item" id="0x7f090110" />
    <public type="id" name="llContent" id="0x7f090111" />
    <public type="id" name="llTop" id="0x7f090112" />
    <public type="id" name="ll_bottom" id="0x7f090113" />
    <public type="id" name="ll_et" id="0x7f090114" />
    <public type="id" name="ll_root" id="0x7f090115" />
    <public type="id" name="locale" id="0x7f090116" />
    <public type="id" name="ltr" id="0x7f090117" />
    <public type="id" name="mapNormal" id="0x7f090118" />
    <public type="id" name="mapSatellite" id="0x7f090119" />
    <public type="id" name="map_close" id="0x7f09011a" />
    <public type="id" name="map_joystick" id="0x7f09011b" />
    <public type="id" name="map_search_linear" id="0x7f09011c" />
    <public type="id" name="map_search_list_view" id="0x7f09011d" />
    <public type="id" name="markwon_drawables_scheduler" id="0x7f09011e" />
    <public type="id" name="markwon_drawables_scheduler_last_text_hashcode" id="0x7f09011f" />
    <public type="id" name="masked" id="0x7f090120" />
    <public type="id" name="match_constraint" id="0x7f090121" />
    <public type="id" name="match_parent" id="0x7f090122" />
    <public type="id" name="message" id="0x7f090123" />
    <public type="id" name="middle" id="0x7f090124" />
    <public type="id" name="mini" id="0x7f090125" />
    <public type="id" name="month_grid" id="0x7f090126" />
    <public type="id" name="month_navigation_bar" id="0x7f090127" />
    <public type="id" name="month_navigation_fragment_toggle" id="0x7f090128" />
    <public type="id" name="month_navigation_next" id="0x7f090129" />
    <public type="id" name="month_navigation_previous" id="0x7f09012a" />
    <public type="id" name="month_title" id="0x7f09012b" />
    <public type="id" name="motion_base" id="0x7f09012c" />
    <public type="id" name="mtrl_calendar_day_selector_frame" id="0x7f09012d" />
    <public type="id" name="mtrl_calendar_days_of_week" id="0x7f09012e" />
    <public type="id" name="mtrl_calendar_frame" id="0x7f09012f" />
    <public type="id" name="mtrl_calendar_main_pane" id="0x7f090130" />
    <public type="id" name="mtrl_calendar_months" id="0x7f090131" />
    <public type="id" name="mtrl_calendar_selection_frame" id="0x7f090132" />
    <public type="id" name="mtrl_calendar_text_input_frame" id="0x7f090133" />
    <public type="id" name="mtrl_calendar_year_selector_frame" id="0x7f090134" />
    <public type="id" name="mtrl_card_checked_layer_id" id="0x7f090135" />
    <public type="id" name="mtrl_child_content_container" id="0x7f090136" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f090137" />
    <public type="id" name="mtrl_motion_snapshot_view" id="0x7f090138" />
    <public type="id" name="mtrl_picker_fullscreen" id="0x7f090139" />
    <public type="id" name="mtrl_picker_header" id="0x7f09013a" />
    <public type="id" name="mtrl_picker_header_selection_text" id="0x7f09013b" />
    <public type="id" name="mtrl_picker_header_title_and_selection" id="0x7f09013c" />
    <public type="id" name="mtrl_picker_header_toggle" id="0x7f09013d" />
    <public type="id" name="mtrl_picker_text_input_date" id="0x7f09013e" />
    <public type="id" name="mtrl_picker_text_input_range_end" id="0x7f09013f" />
    <public type="id" name="mtrl_picker_text_input_range_start" id="0x7f090140" />
    <public type="id" name="mtrl_picker_title_text" id="0x7f090141" />
    <public type="id" name="multiply" id="0x7f090142" />
    <public type="id" name="nav_contact" id="0x7f090143" />
    <public type="id" name="nav_dev" id="0x7f090144" />
    <public type="id" name="nav_feedback" id="0x7f090145" />
    <public type="id" name="nav_history" id="0x7f090146" />
    <public type="id" name="nav_settings" id="0x7f090147" />
    <public type="id" name="nav_update" id="0x7f090148" />
    <public type="id" name="nav_view" id="0x7f090149" />
    <public type="id" name="navigation_header_container" id="0x7f09014a" />
    <public type="id" name="never" id="0x7f09014b" />
    <public type="id" name="neverCompleteToEnd" id="0x7f09014c" />
    <public type="id" name="neverCompleteToStart" id="0x7f09014d" />
    <public type="id" name="noScroll" id="0x7f09014e" />
    <public type="id" name="noState" id="0x7f09014f" />
    <public type="id" name="none" id="0x7f090150" />
    <public type="id" name="normal" id="0x7f090151" />
    <public type="id" name="north" id="0x7f090152" />
    <public type="id" name="notification_background" id="0x7f090153" />
    <public type="id" name="notification_main_column" id="0x7f090154" />
    <public type="id" name="notification_main_column_container" id="0x7f090155" />
    <public type="id" name="off" id="0x7f090156" />
    <public type="id" name="on" id="0x7f090157" />
    <public type="id" name="onInterceptTouchReturnSwipe" id="0x7f090158" />
    <public type="id" name="outline" id="0x7f090159" />
    <public type="id" name="overshoot" id="0x7f09015a" />
    <public type="id" name="packed" id="0x7f09015b" />
    <public type="id" name="parallax" id="0x7f09015c" />
    <public type="id" name="parent" id="0x7f09015d" />
    <public type="id" name="parentPanel" id="0x7f09015e" />
    <public type="id" name="parentRelative" id="0x7f09015f" />
    <public type="id" name="parent_matrix" id="0x7f090160" />
    <public type="id" name="password_toggle" id="0x7f090161" />
    <public type="id" name="path" id="0x7f090162" />
    <public type="id" name="pathRelative" id="0x7f090163" />
    <public type="id" name="peekHeight" id="0x7f090164" />
    <public type="id" name="percent" id="0x7f090165" />
    <public type="id" name="pin" id="0x7f090166" />
    <public type="id" name="poi_address" id="0x7f090167" />
    <public type="id" name="poi_copy" id="0x7f090168" />
    <public type="id" name="poi_fly" id="0x7f090169" />
    <public type="id" name="poi_latitude" id="0x7f09016a" />
    <public type="id" name="poi_longitude" id="0x7f09016b" />
    <public type="id" name="poi_name" id="0x7f09016c" />
    <public type="id" name="poi_save" id="0x7f09016d" />
    <public type="id" name="poi_share" id="0x7f09016e" />
    <public type="id" name="pos_type_bd" id="0x7f09016f" />
    <public type="id" name="position" id="0x7f090170" />
    <public type="id" name="postLayout" id="0x7f090171" />
    <public type="id" name="preferences_detail" id="0x7f090172" />
    <public type="id" name="preferences_header" id="0x7f090173" />
    <public type="id" name="preferences_sliding_pane_layout" id="0x7f090174" />
    <public type="id" name="progress_circular" id="0x7f090175" />
    <public type="id" name="progress_horizontal" id="0x7f090176" />
    <public type="id" name="radio" id="0x7f090177" />
    <public type="id" name="ratio" id="0x7f090178" />
    <public type="id" name="record_list_view" id="0x7f090179" />
    <public type="id" name="record_no_textview" id="0x7f09017a" />
    <public type="id" name="rectangles" id="0x7f09017b" />
    <public type="id" name="recycler_view" id="0x7f09017c" />
    <public type="id" name="reg_agree" id="0x7f09017d" />
    <public type="id" name="reg_cancel" id="0x7f09017e" />
    <public type="id" name="reg_check" id="0x7f09017f" />
    <public type="id" name="reg_request" id="0x7f090180" />
    <public type="id" name="reg_response" id="0x7f090181" />
    <public type="id" name="reg_user_name" id="0x7f090182" />
    <public type="id" name="reverseSawtooth" id="0x7f090183" />
    <public type="id" name="right" id="0x7f090184" />
    <public type="id" name="right_icon" id="0x7f090185" />
    <public type="id" name="right_side" id="0x7f090186" />
    <public type="id" name="rlTitles" id="0x7f090187" />
    <public type="id" name="rounded" id="0x7f090188" />
    <public type="id" name="row_index_key" id="0x7f090189" />
    <public type="id" name="rtl" id="0x7f09018a" />
    <public type="id" name="save_non_transition_alpha" id="0x7f09018b" />
    <public type="id" name="save_overlay_view" id="0x7f09018c" />
    <public type="id" name="sawtooth" id="0x7f09018d" />
    <public type="id" name="scale" id="0x7f09018e" />
    <public type="id" name="scb" id="0x7f09018f" />
    <public type="id" name="screen" id="0x7f090190" />
    <public type="id" name="scroll" id="0x7f090191" />
    <public type="id" name="scrollIndicatorDown" id="0x7f090192" />
    <public type="id" name="scrollIndicatorUp" id="0x7f090193" />
    <public type="id" name="scrollView" id="0x7f090194" />
    <public type="id" name="scroll_view" id="0x7f090195" />
    <public type="id" name="scrollable" id="0x7f090196" />
    <public type="id" name="searchView" id="0x7f090197" />
    <public type="id" name="search_badge" id="0x7f090198" />
    <public type="id" name="search_bar" id="0x7f090199" />
    <public type="id" name="search_button" id="0x7f09019a" />
    <public type="id" name="search_close_btn" id="0x7f09019b" />
    <public type="id" name="search_description" id="0x7f09019c" />
    <public type="id" name="search_edit_frame" id="0x7f09019d" />
    <public type="id" name="search_go_btn" id="0x7f09019e" />
    <public type="id" name="search_history_linear" id="0x7f09019f" />
    <public type="id" name="search_history_list_view" id="0x7f0901a0" />
    <public type="id" name="search_isLoc" id="0x7f0901a1" />
    <public type="id" name="search_key" id="0x7f0901a2" />
    <public type="id" name="search_latitude" id="0x7f0901a3" />
    <public type="id" name="search_linear" id="0x7f0901a4" />
    <public type="id" name="search_list_view" id="0x7f0901a5" />
    <public type="id" name="search_longitude" id="0x7f0901a6" />
    <public type="id" name="search_mag_icon" id="0x7f0901a7" />
    <public type="id" name="search_plate" id="0x7f0901a8" />
    <public type="id" name="search_src_text" id="0x7f0901a9" />
    <public type="id" name="search_timestamp" id="0x7f0901aa" />
    <public type="id" name="search_voice_btn" id="0x7f0901ab" />
    <public type="id" name="seekbar" id="0x7f0901ac" />
    <public type="id" name="seekbar_value" id="0x7f0901ad" />
    <public type="id" name="select_dialog_listview" id="0x7f0901ae" />
    <public type="id" name="selected" id="0x7f0901af" />
    <public type="id" name="selelayout" id="0x7f0901b0" />
    <public type="id" name="settings" id="0x7f0901b1" />
    <public type="id" name="sharedValueSet" id="0x7f0901b2" />
    <public type="id" name="sharedValueUnset" id="0x7f0901b3" />
    <public type="id" name="shortcut" id="0x7f0901b4" />
    <public type="id" name="showCustom" id="0x7f0901b5" />
    <public type="id" name="showHome" id="0x7f0901b6" />
    <public type="id" name="showTitle" id="0x7f0901b7" />
    <public type="id" name="show_address2" id="0x7f0901b8" />
    <public type="id" name="show_jingweidu2" id="0x7f0901b9" />
    <public type="id" name="sin" id="0x7f0901ba" />
    <public type="id" name="skipCollapsed" id="0x7f0901bb" />
    <public type="id" name="skipped" id="0x7f0901bc" />
    <public type="id" name="slide" id="0x7f0901bd" />
    <public type="id" name="smallLabel" id="0x7f0901be" />
    <public type="id" name="snackbar_action" id="0x7f0901bf" />
    <public type="id" name="snackbar_text" id="0x7f0901c0" />
    <public type="id" name="snap" id="0x7f0901c1" />
    <public type="id" name="snapMargins" id="0x7f0901c2" />
    <public type="id" name="south" id="0x7f0901c3" />
    <public type="id" name="spacer" id="0x7f0901c4" />
    <public type="id" name="special_effects_controller_view_tag" id="0x7f0901c5" />
    <public type="id" name="spinner" id="0x7f0901c6" />
    <public type="id" name="spline" id="0x7f0901c7" />
    <public type="id" name="split_action_bar" id="0x7f0901c8" />
    <public type="id" name="spread" id="0x7f0901c9" />
    <public type="id" name="spread_inside" id="0x7f0901ca" />
    <public type="id" name="spring" id="0x7f0901cb" />
    <public type="id" name="square" id="0x7f0901cc" />
    <public type="id" name="src_atop" id="0x7f0901cd" />
    <public type="id" name="src_in" id="0x7f0901ce" />
    <public type="id" name="src_over" id="0x7f0901cf" />
    <public type="id" name="standard" id="0x7f0901d0" />
    <public type="id" name="start" id="0x7f0901d1" />
    <public type="id" name="startHorizontal" id="0x7f0901d2" />
    <public type="id" name="startVertical" id="0x7f0901d3" />
    <public type="id" name="staticLayout" id="0x7f0901d4" />
    <public type="id" name="staticPostLayout" id="0x7f0901d5" />
    <public type="id" name="stop" id="0x7f0901d6" />
    <public type="id" name="stretch" id="0x7f0901d7" />
    <public type="id" name="submenuarrow" id="0x7f0901d8" />
    <public type="id" name="submit_area" id="0x7f0901d9" />
    <public type="id" name="sug_city" id="0x7f0901da" />
    <public type="id" name="sug_dis" id="0x7f0901db" />
    <public type="id" name="sug_key" id="0x7f0901dc" />
    <public type="id" name="sug_list" id="0x7f0901dd" />
    <public type="id" name="supportScrollUp" id="0x7f0901de" />
    <public type="id" name="switchWidget" id="0x7f0901df" />
    <public type="id" name="tabMode" id="0x7f0901e0" />
    <public type="id" name="tag_accessibility_actions" id="0x7f0901e1" />
    <public type="id" name="tag_accessibility_clickable_spans" id="0x7f0901e2" />
    <public type="id" name="tag_accessibility_heading" id="0x7f0901e3" />
    <public type="id" name="tag_accessibility_pane_title" id="0x7f0901e4" />
    <public type="id" name="tag_on_apply_window_listener" id="0x7f0901e5" />
    <public type="id" name="tag_on_receive_content_listener" id="0x7f0901e6" />
    <public type="id" name="tag_on_receive_content_mime_types" id="0x7f0901e7" />
    <public type="id" name="tag_screen_reader_focusable" id="0x7f0901e8" />
    <public type="id" name="tag_state_description" id="0x7f0901e9" />
    <public type="id" name="tag_transition_group" id="0x7f0901ea" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f0901eb" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f0901ec" />
    <public type="id" name="tag_window_insets_animation_callback" id="0x7f0901ed" />
    <public type="id" name="test_checkbox_android_button_tint" id="0x7f0901ee" />
    <public type="id" name="test_checkbox_app_button_tint" id="0x7f0901ef" />
    <public type="id" name="test_radiobutton_android_button_tint" id="0x7f0901f0" />
    <public type="id" name="test_radiobutton_app_button_tint" id="0x7f0901f1" />
    <public type="id" name="text" id="0x7f0901f2" />
    <public type="id" name="text2" id="0x7f0901f3" />
    <public type="id" name="textEnd" id="0x7f0901f4" />
    <public type="id" name="textSpacerNoButtons" id="0x7f0901f5" />
    <public type="id" name="textSpacerNoTitle" id="0x7f0901f6" />
    <public type="id" name="textStart" id="0x7f0901f7" />
    <public type="id" name="text_input_end_icon" id="0x7f0901f8" />
    <public type="id" name="text_input_start_icon" id="0x7f0901f9" />
    <public type="id" name="textinput_counter" id="0x7f0901fa" />
    <public type="id" name="textinput_error" id="0x7f0901fb" />
    <public type="id" name="textinput_helper_text" id="0x7f0901fc" />
    <public type="id" name="textinput_placeholder" id="0x7f0901fd" />
    <public type="id" name="textinput_prefix_text" id="0x7f0901fe" />
    <public type="id" name="textinput_suffix_text" id="0x7f0901ff" />
    <public type="id" name="tiaozhuan" id="0x7f090200" />
    <public type="id" name="time" id="0x7f090201" />
    <public type="id" name="title" id="0x7f090202" />
    <public type="id" name="titleDividerNoCustom" id="0x7f090203" />
    <public type="id" name="title_template" id="0x7f090204" />
    <public type="id" name="toGo" id="0x7f090205" />
    <public type="id" name="toggle" id="0x7f090206" />
    <public type="id" name="toolbar" id="0x7f090207" />
    <public type="id" name="top" id="0x7f090208" />
    <public type="id" name="topPanel" id="0x7f090209" />
    <public type="id" name="touch_outside" id="0x7f09020a" />
    <public type="id" name="transitionToEnd" id="0x7f09020b" />
    <public type="id" name="transitionToStart" id="0x7f09020c" />
    <public type="id" name="transition_current_scene" id="0x7f09020d" />
    <public type="id" name="transition_layout_save" id="0x7f09020e" />
    <public type="id" name="transition_position" id="0x7f09020f" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f090210" />
    <public type="id" name="transition_transform" id="0x7f090211" />
    <public type="id" name="triangle" id="0x7f090212" />
    <public type="id" name="tvHis" id="0x7f090213" />
    <public type="id" name="tvN" id="0x7f090214" />
    <public type="id" name="tvPosition" id="0x7f090215" />
    <public type="id" name="tvSelect" id="0x7f090216" />
    <public type="id" name="tv_agree" id="0x7f090217" />
    <public type="id" name="tv_cancel" id="0x7f090218" />
    <public type="id" name="tv_cancle" id="0x7f090219" />
    <public type="id" name="tv_confirm" id="0x7f09021a" />
    <public type="id" name="tv_content" id="0x7f09021b" />
    <public type="id" name="tv_splash_tag" id="0x7f09021c" />
    <public type="id" name="tv_top" id="0x7f09021d" />
    <public type="id" name="unchecked" id="0x7f09021e" />
    <public type="id" name="uniform" id="0x7f09021f" />
    <public type="id" name="unlabeled" id="0x7f090220" />
    <public type="id" name="up" id="0x7f090221" />
    <public type="id" name="update_agree" id="0x7f090222" />
    <public type="id" name="update_commit" id="0x7f090223" />
    <public type="id" name="update_content" id="0x7f090224" />
    <public type="id" name="update_ignore" id="0x7f090225" />
    <public type="id" name="update_time" id="0x7f090226" />
    <public type="id" name="update_title" id="0x7f090227" />
    <public type="id" name="useLogo" id="0x7f090228" />
    <public type="id" name="user_icon" id="0x7f090229" />
    <public type="id" name="user_name" id="0x7f09022a" />
    <public type="id" name="vertical_only" id="0x7f09022b" />
    <public type="id" name="view_offset_helper" id="0x7f09022c" />
    <public type="id" name="view_transition" id="0x7f09022d" />
    <public type="id" name="view_tree_lifecycle_owner" id="0x7f09022e" />
    <public type="id" name="view_tree_on_back_pressed_dispatcher_owner" id="0x7f09022f" />
    <public type="id" name="view_tree_saved_state_registry_owner" id="0x7f090230" />
    <public type="id" name="view_tree_view_model_store_owner" id="0x7f090231" />
    <public type="id" name="visible" id="0x7f090232" />
    <public type="id" name="visible_removing_fragment_view_tag" id="0x7f090233" />
    <public type="id" name="west" id="0x7f090234" />
    <public type="id" name="withText" id="0x7f090235" />
    <public type="id" name="withinBounds" id="0x7f090236" />
    <public type="id" name="wrap" id="0x7f090237" />
    <public type="id" name="wrap_content" id="0x7f090238" />
    <public type="id" name="wrap_content_constrained" id="0x7f090239" />
    <public type="id" name="x_left" id="0x7f09023a" />
    <public type="id" name="x_right" id="0x7f09023b" />
    <public type="id" name="zero_corner_chip" id="0x7f09023c" />
    <public type="id" name="zoom_in" id="0x7f09023d" />
    <public type="id" name="zoom_out" id="0x7f09023e" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f0a0000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f0a0001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f0a0002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f0a0003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f0a0004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f0a0005" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f0a0006" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f0a0007" />
    <public type="integer" name="hide_password_duration" id="0x7f0a0008" />
    <public type="integer" name="mtrl_badge_max_character_count" id="0x7f0a0009" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f0a000a" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f0a000b" />
    <public type="integer" name="mtrl_calendar_header_orientation" id="0x7f0a000c" />
    <public type="integer" name="mtrl_calendar_selection_text_lines" id="0x7f0a000d" />
    <public type="integer" name="mtrl_calendar_year_selector_span" id="0x7f0a000e" />
    <public type="integer" name="mtrl_card_anim_delay_ms" id="0x7f0a000f" />
    <public type="integer" name="mtrl_card_anim_duration_ms" id="0x7f0a0010" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f0a0011" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f0a0012" />
    <public type="integer" name="preferences_detail_pane_weight" id="0x7f0a0013" />
    <public type="integer" name="preferences_header_pane_weight" id="0x7f0a0014" />
    <public type="integer" name="show_password_duration" id="0x7f0a0015" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f0a0016" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_0" id="0x7f0b0000" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_1" id="0x7f0b0001" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_0" id="0x7f0b0002" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_1" id="0x7f0b0003" />
    <public type="interpolator" name="btn_radio_to_off_mtrl_animation_interpolator_0" id="0x7f0b0004" />
    <public type="interpolator" name="btn_radio_to_on_mtrl_animation_interpolator_0" id="0x7f0b0005" />
    <public type="interpolator" name="fast_out_slow_in" id="0x7f0b0006" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0b0007" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0b0008" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0b0009" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0b000a" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0c0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0c0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0c0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0c0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0c0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0c0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0c0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0c0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0c0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0c0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0c000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0c000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0c000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0c000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0c000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0c000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0c0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0c0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0c0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0c0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0c0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0c0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0c0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0c0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0c0018" />
    <public type="layout" name="abc_search_view" id="0x7f0c0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0c001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0c001b" />
    <public type="layout" name="activity_history" id="0x7f0c001c" />
    <public type="layout" name="activity_home" id="0x7f0c001d" />
    <public type="layout" name="activity_location_type2" id="0x7f0c001e" />
    <public type="layout" name="activity_login" id="0x7f0c001f" />
    <public type="layout" name="activity_main" id="0x7f0c0020" />
    <public type="layout" name="activity_select" id="0x7f0c0021" />
    <public type="layout" name="activity_settings" id="0x7f0c0022" />
    <public type="layout" name="activity_welcome" id="0x7f0c0023" />
    <public type="layout" name="custom_dialog" id="0x7f0c0024" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0c0025" />
    <public type="layout" name="design_bottom_sheet_dialog" id="0x7f0c0026" />
    <public type="layout" name="design_layout_snackbar" id="0x7f0c0027" />
    <public type="layout" name="design_layout_snackbar_include" id="0x7f0c0028" />
    <public type="layout" name="design_layout_tab_icon" id="0x7f0c0029" />
    <public type="layout" name="design_layout_tab_text" id="0x7f0c002a" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0c002b" />
    <public type="layout" name="design_navigation_item" id="0x7f0c002c" />
    <public type="layout" name="design_navigation_item_header" id="0x7f0c002d" />
    <public type="layout" name="design_navigation_item_separator" id="0x7f0c002e" />
    <public type="layout" name="design_navigation_item_subheader" id="0x7f0c002f" />
    <public type="layout" name="design_navigation_menu" id="0x7f0c0030" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0c0031" />
    <public type="layout" name="design_text_input_end_icon" id="0x7f0c0032" />
    <public type="layout" name="design_text_input_start_icon" id="0x7f0c0033" />
    <public type="layout" name="dialog_agreement" id="0x7f0c0034" />
    <public type="layout" name="expand_button" id="0x7f0c0035" />
    <public type="layout" name="history_item" id="0x7f0c0036" />
    <public type="layout" name="image_frame" id="0x7f0c0037" />
    <public type="layout" name="item_layout" id="0x7f0c0038" />
    <public type="layout" name="joystick" id="0x7f0c0039" />
    <public type="layout" name="joystick_button" id="0x7f0c003a" />
    <public type="layout" name="joystick_history" id="0x7f0c003b" />
    <public type="layout" name="joystick_map" id="0x7f0c003c" />
    <public type="layout" name="location_input" id="0x7f0c003d" />
    <public type="layout" name="location_poi_info" id="0x7f0c003e" />
    <public type="layout" name="main_content" id="0x7f0c003f" />
    <public type="layout" name="main_map" id="0x7f0c0040" />
    <public type="layout" name="mtrl_alert_dialog" id="0x7f0c0041" />
    <public type="layout" name="mtrl_alert_dialog_actions" id="0x7f0c0042" />
    <public type="layout" name="mtrl_alert_dialog_title" id="0x7f0c0043" />
    <public type="layout" name="mtrl_alert_select_dialog_item" id="0x7f0c0044" />
    <public type="layout" name="mtrl_alert_select_dialog_multichoice" id="0x7f0c0045" />
    <public type="layout" name="mtrl_alert_select_dialog_singlechoice" id="0x7f0c0046" />
    <public type="layout" name="mtrl_calendar_day" id="0x7f0c0047" />
    <public type="layout" name="mtrl_calendar_day_of_week" id="0x7f0c0048" />
    <public type="layout" name="mtrl_calendar_days_of_week" id="0x7f0c0049" />
    <public type="layout" name="mtrl_calendar_horizontal" id="0x7f0c004a" />
    <public type="layout" name="mtrl_calendar_month" id="0x7f0c004b" />
    <public type="layout" name="mtrl_calendar_month_labeled" id="0x7f0c004c" />
    <public type="layout" name="mtrl_calendar_month_navigation" id="0x7f0c004d" />
    <public type="layout" name="mtrl_calendar_months" id="0x7f0c004e" />
    <public type="layout" name="mtrl_calendar_vertical" id="0x7f0c004f" />
    <public type="layout" name="mtrl_calendar_year" id="0x7f0c0050" />
    <public type="layout" name="mtrl_layout_snackbar" id="0x7f0c0051" />
    <public type="layout" name="mtrl_layout_snackbar_include" id="0x7f0c0052" />
    <public type="layout" name="mtrl_picker_actions" id="0x7f0c0053" />
    <public type="layout" name="mtrl_picker_dialog" id="0x7f0c0054" />
    <public type="layout" name="mtrl_picker_fullscreen" id="0x7f0c0055" />
    <public type="layout" name="mtrl_picker_header_dialog" id="0x7f0c0056" />
    <public type="layout" name="mtrl_picker_header_fullscreen" id="0x7f0c0057" />
    <public type="layout" name="mtrl_picker_header_selection_text" id="0x7f0c0058" />
    <public type="layout" name="mtrl_picker_header_title_text" id="0x7f0c0059" />
    <public type="layout" name="mtrl_picker_header_toggle" id="0x7f0c005a" />
    <public type="layout" name="mtrl_picker_text_input_date" id="0x7f0c005b" />
    <public type="layout" name="mtrl_picker_text_input_date_range" id="0x7f0c005c" />
    <public type="layout" name="nav_header" id="0x7f0c005d" />
    <public type="layout" name="notification_action" id="0x7f0c005e" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0c005f" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0c0060" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0c0061" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0c0062" />
    <public type="layout" name="notification_template_part_time" id="0x7f0c0063" />
    <public type="layout" name="preference" id="0x7f0c0064" />
    <public type="layout" name="preference_category" id="0x7f0c0065" />
    <public type="layout" name="preference_category_material" id="0x7f0c0066" />
    <public type="layout" name="preference_dialog_edittext" id="0x7f0c0067" />
    <public type="layout" name="preference_dropdown" id="0x7f0c0068" />
    <public type="layout" name="preference_dropdown_material" id="0x7f0c0069" />
    <public type="layout" name="preference_information" id="0x7f0c006a" />
    <public type="layout" name="preference_information_material" id="0x7f0c006b" />
    <public type="layout" name="preference_list_fragment" id="0x7f0c006c" />
    <public type="layout" name="preference_material" id="0x7f0c006d" />
    <public type="layout" name="preference_recyclerview" id="0x7f0c006e" />
    <public type="layout" name="preference_widget_checkbox" id="0x7f0c006f" />
    <public type="layout" name="preference_widget_seekbar" id="0x7f0c0070" />
    <public type="layout" name="preference_widget_seekbar_material" id="0x7f0c0071" />
    <public type="layout" name="preference_widget_switch" id="0x7f0c0072" />
    <public type="layout" name="preference_widget_switch_compat" id="0x7f0c0073" />
    <public type="layout" name="register" id="0x7f0c0074" />
    <public type="layout" name="search_item" id="0x7f0c0075" />
    <public type="layout" name="search_poi_item" id="0x7f0c0076" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0c0077" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0c0078" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0c0079" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0c007a" />
    <public type="layout" name="test_action_chip" id="0x7f0c007b" />
    <public type="layout" name="test_chip_zero_corner_radius" id="0x7f0c007c" />
    <public type="layout" name="test_design_checkbox" id="0x7f0c007d" />
    <public type="layout" name="test_design_radiobutton" id="0x7f0c007e" />
    <public type="layout" name="test_reflow_chipgroup" id="0x7f0c007f" />
    <public type="layout" name="test_toolbar" id="0x7f0c0080" />
    <public type="layout" name="test_toolbar_custom_background" id="0x7f0c0081" />
    <public type="layout" name="test_toolbar_elevation" id="0x7f0c0082" />
    <public type="layout" name="test_toolbar_surface" id="0x7f0c0083" />
    <public type="layout" name="text_view_with_line_height_from_appearance" id="0x7f0c0084" />
    <public type="layout" name="text_view_with_line_height_from_layout" id="0x7f0c0085" />
    <public type="layout" name="text_view_with_line_height_from_style" id="0x7f0c0086" />
    <public type="layout" name="text_view_with_theme_line_height" id="0x7f0c0087" />
    <public type="layout" name="text_view_without_line_height" id="0x7f0c0088" />
    <public type="layout" name="update" id="0x7f0c0089" />
    <public type="layout" name="user_agreement" id="0x7f0c008a" />
    <public type="layout" name="user_privacy" id="0x7f0c008b" />
    <public type="menu" name="menu_history" id="0x7f0d0000" />
    <public type="menu" name="menu_main" id="0x7f0d0001" />
    <public type="menu" name="menu_nav" id="0x7f0d0002" />
    <public type="mipmap" name="ic_launcher" id="0x7f0e0000" />
    <public type="mipmap" name="zhixiang" id="0x7f0e0001" />
    <public type="plurals" name="mtrl_badge_content_description" id="0x7f0f0000" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f100000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f100001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f100002" />
    <public type="string" name="abc_action_mode_done" id="0x7f100003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f100004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f100005" />
    <public type="string" name="abc_capital_off" id="0x7f100006" />
    <public type="string" name="abc_capital_on" id="0x7f100007" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f100008" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f100009" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f10000a" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f10000b" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f10000c" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f10000d" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f10000e" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f10000f" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f100010" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f100011" />
    <public type="string" name="abc_search_hint" id="0x7f100012" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f100013" />
    <public type="string" name="abc_searchview_description_query" id="0x7f100014" />
    <public type="string" name="abc_searchview_description_search" id="0x7f100015" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f100016" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f100017" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f100018" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f100019" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f10001a" />
    <public type="string" name="ak" id="0x7f10001b" />
    <public type="string" name="androidx_startup" id="0x7f10001c" />
    <public type="string" name="app_agreement" id="0x7f10001d" />
    <public type="string" name="app_agreement_content" id="0x7f10001e" />
    <public type="string" name="app_agreement_privacy" id="0x7f10001f" />
    <public type="string" name="app_author" id="0x7f100020" />
    <public type="string" name="app_btn_agree" id="0x7f100021" />
    <public type="string" name="app_btn_disagree" id="0x7f100022" />
    <public type="string" name="app_email" id="0x7f100023" />
    <public type="string" name="app_error_agreement" id="0x7f100024" />
    <public type="string" name="app_error_code" id="0x7f100025" />
    <public type="string" name="app_error_dev" id="0x7f100026" />
    <public type="string" name="app_error_gps" id="0x7f100027" />
    <public type="string" name="app_error_input" id="0x7f100028" />
    <public type="string" name="app_error_input_null" id="0x7f100029" />
    <public type="string" name="app_error_latitude" id="0x7f10002a" />
    <public type="string" name="app_error_location" id="0x7f10002b" />
    <public type="string" name="app_error_longitude" id="0x7f10002c" />
    <public type="string" name="app_error_network" id="0x7f10002d" />
    <public type="string" name="app_error_param" id="0x7f10002e" />
    <public type="string" name="app_error_permission" id="0x7f10002f" />
    <public type="string" name="app_error_protocol" id="0x7f100030" />
    <public type="string" name="app_error_read" id="0x7f100031" />
    <public type="string" name="app_error_search" id="0x7f100032" />
    <public type="string" name="app_error_username" id="0x7f100033" />
    <public type="string" name="app_history" id="0x7f100034" />
    <public type="string" name="app_location_copy" id="0x7f100035" />
    <public type="string" name="app_location_ok" id="0x7f100036" />
    <public type="string" name="app_location_save" id="0x7f100037" />
    <public type="string" name="app_name" id="0x7f100038" />
    <public type="string" name="app_privacy" id="0x7f100039" />
    <public type="string" name="app_privacy_content" id="0x7f10003a" />
    <public type="string" name="app_search_null" id="0x7f10003b" />
    <public type="string" name="app_search_tips" id="0x7f10003c" />
    <public type="string" name="app_service_tips" id="0x7f10003d" />
    <public type="string" name="app_settings" id="0x7f10003e" />
    <public type="string" name="app_statement" id="0x7f10003f" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f100040" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f100041" />
    <public type="string" name="character_counter_content_description" id="0x7f100042" />
    <public type="string" name="character_counter_overflowed_content_description" id="0x7f100043" />
    <public type="string" name="character_counter_pattern" id="0x7f100044" />
    <public type="string" name="chip_text" id="0x7f100045" />
    <public type="string" name="clear_text_end_icon_content_description" id="0x7f100046" />
    <public type="string" name="copy" id="0x7f100047" />
    <public type="string" name="cur_position" id="0x7f100048" />
    <public type="string" name="error_icon_content_description" id="0x7f100049" />
    <public type="string" name="expand_button_title" id="0x7f10004a" />
    <public type="string" name="exposed_dropdown_menu_content_description" id="0x7f10004b" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f10004c" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f10004d" />
    <public type="string" name="hide_bottom_view_on_scroll_behavior" id="0x7f10004e" />
    <public type="string" name="history_delete_error" id="0x7f10004f" />
    <public type="string" name="history_delete_ok" id="0x7f100050" />
    <public type="string" name="history_error_location" id="0x7f100051" />
    <public type="string" name="history_error_search" id="0x7f100052" />
    <public type="string" name="history_expiration" id="0x7f100053" />
    <public type="string" name="history_idle" id="0x7f100054" />
    <public type="string" name="history_location_default_name" id="0x7f100055" />
    <public type="string" name="icon_content_description" id="0x7f100056" />
    <public type="string" name="input_button" id="0x7f100057" />
    <public type="string" name="input_position_baidu" id="0x7f100058" />
    <public type="string" name="input_position_cancel" id="0x7f100059" />
    <public type="string" name="input_position_gps" id="0x7f10005a" />
    <public type="string" name="input_position_ok" id="0x7f10005b" />
    <public type="string" name="item_view_role_description" id="0x7f10005c" />
    <public type="string" name="joystick_bike" id="0x7f10005d" />
    <public type="string" name="joystick_history" id="0x7f10005e" />
    <public type="string" name="joystick_history_tips" id="0x7f10005f" />
    <public type="string" name="joystick_map" id="0x7f100060" />
    <public type="string" name="joystick_map_tips" id="0x7f100061" />
    <public type="string" name="joystick_move" id="0x7f100062" />
    <public type="string" name="joystick_run" id="0x7f100063" />
    <public type="string" name="joystick_walk" id="0x7f100064" />
    <public type="string" name="label_latitude" id="0x7f100065" />
    <public type="string" name="label_longitude" id="0x7f100066" />
    <public type="string" name="map_pic_normal" id="0x7f100067" />
    <public type="string" name="map_pic_sate" id="0x7f100068" />
    <public type="string" name="material_slider_range_end" id="0x7f100069" />
    <public type="string" name="material_slider_range_start" id="0x7f10006a" />
    <public type="string" name="mtrl_badge_numberless_content_description" id="0x7f10006b" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f10006c" />
    <public type="string" name="mtrl_exceed_max_badge_number_content_description" id="0x7f10006d" />
    <public type="string" name="mtrl_exceed_max_badge_number_suffix" id="0x7f10006e" />
    <public type="string" name="mtrl_picker_a11y_next_month" id="0x7f10006f" />
    <public type="string" name="mtrl_picker_a11y_prev_month" id="0x7f100070" />
    <public type="string" name="mtrl_picker_announce_current_selection" id="0x7f100071" />
    <public type="string" name="mtrl_picker_cancel" id="0x7f100072" />
    <public type="string" name="mtrl_picker_confirm" id="0x7f100073" />
    <public type="string" name="mtrl_picker_date_header_selected" id="0x7f100074" />
    <public type="string" name="mtrl_picker_date_header_title" id="0x7f100075" />
    <public type="string" name="mtrl_picker_date_header_unselected" id="0x7f100076" />
    <public type="string" name="mtrl_picker_day_of_week_column_header" id="0x7f100077" />
    <public type="string" name="mtrl_picker_invalid_format" id="0x7f100078" />
    <public type="string" name="mtrl_picker_invalid_format_example" id="0x7f100079" />
    <public type="string" name="mtrl_picker_invalid_format_use" id="0x7f10007a" />
    <public type="string" name="mtrl_picker_invalid_range" id="0x7f10007b" />
    <public type="string" name="mtrl_picker_navigate_to_year_description" id="0x7f10007c" />
    <public type="string" name="mtrl_picker_out_of_range" id="0x7f10007d" />
    <public type="string" name="mtrl_picker_range_header_only_end_selected" id="0x7f10007e" />
    <public type="string" name="mtrl_picker_range_header_only_start_selected" id="0x7f10007f" />
    <public type="string" name="mtrl_picker_range_header_selected" id="0x7f100080" />
    <public type="string" name="mtrl_picker_range_header_title" id="0x7f100081" />
    <public type="string" name="mtrl_picker_range_header_unselected" id="0x7f100082" />
    <public type="string" name="mtrl_picker_save" id="0x7f100083" />
    <public type="string" name="mtrl_picker_text_input_date_hint" id="0x7f100084" />
    <public type="string" name="mtrl_picker_text_input_date_range_end_hint" id="0x7f100085" />
    <public type="string" name="mtrl_picker_text_input_date_range_start_hint" id="0x7f100086" />
    <public type="string" name="mtrl_picker_text_input_day_abbr" id="0x7f100087" />
    <public type="string" name="mtrl_picker_text_input_month_abbr" id="0x7f100088" />
    <public type="string" name="mtrl_picker_text_input_year_abbr" id="0x7f100089" />
    <public type="string" name="mtrl_picker_toggle_to_calendar_input_mode" id="0x7f10008a" />
    <public type="string" name="mtrl_picker_toggle_to_day_selection" id="0x7f10008b" />
    <public type="string" name="mtrl_picker_toggle_to_text_input_mode" id="0x7f10008c" />
    <public type="string" name="mtrl_picker_toggle_to_year_selection" id="0x7f10008d" />
    <public type="string" name="nav_drawer_close" id="0x7f10008e" />
    <public type="string" name="nav_drawer_open" id="0x7f10008f" />
    <public type="string" name="nav_menu_contact" id="0x7f100090" />
    <public type="string" name="nav_menu_dev" id="0x7f100091" />
    <public type="string" name="nav_menu_feedback" id="0x7f100092" />
    <public type="string" name="nav_menu_history" id="0x7f100093" />
    <public type="string" name="nav_menu_more" id="0x7f100094" />
    <public type="string" name="nav_menu_settings" id="0x7f100095" />
    <public type="string" name="nav_menu_upgrade" id="0x7f100096" />
    <public type="string" name="nav_user_limit_info" id="0x7f100097" />
    <public type="string" name="nav_user_name" id="0x7f100098" />
    <public type="string" name="not_set" id="0x7f100099" />
    <public type="string" name="note_hide" id="0x7f10009a" />
    <public type="string" name="note_show" id="0x7f10009b" />
    <public type="string" name="password_toggle_content_description" id="0x7f10009c" />
    <public type="string" name="path_password_eye" id="0x7f10009d" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f10009e" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f10009f" />
    <public type="string" name="path_password_strike_through" id="0x7f1000a0" />
    <public type="string" name="preference_copied" id="0x7f1000a1" />
    <public type="string" name="register_cancel" id="0x7f1000a2" />
    <public type="string" name="register_check" id="0x7f1000a3" />
    <public type="string" name="register_limit" id="0x7f1000a4" />
    <public type="string" name="register_ok" id="0x7f1000a5" />
    <public type="string" name="register_response" id="0x7f1000a6" />
    <public type="string" name="register_tips" id="0x7f1000a7" />
    <public type="string" name="register_title" id="0x7f1000a8" />
    <public type="string" name="register_user_name" id="0x7f1000a9" />
    <public type="string" name="safecode" id="0x7f1000aa" />
    <public type="string" name="search_menu_title" id="0x7f1000ab" />
    <public type="string" name="setting_author" id="0x7f1000ac" />
    <public type="string" name="setting_bike" id="0x7f1000ad" />
    <public type="string" name="setting_bike_default" id="0x7f1000ae" />
    <public type="string" name="setting_current_value" id="0x7f1000af" />
    <public type="string" name="setting_group_about" id="0x7f1000b0" />
    <public type="string" name="setting_group_log" id="0x7f1000b1" />
    <public type="string" name="setting_group_move" id="0x7f1000b2" />
    <public type="string" name="setting_group_sys" id="0x7f1000b3" />
    <public type="string" name="setting_joystick" id="0x7f1000b4" />
    <public type="string" name="setting_joystick_tips" id="0x7f1000b5" />
    <public type="string" name="setting_log_off" id="0x7f1000b6" />
    <public type="string" name="setting_pos_history" id="0x7f1000b7" />
    <public type="string" name="setting_run" id="0x7f1000b8" />
    <public type="string" name="setting_run_default" id="0x7f1000b9" />
    <public type="string" name="setting_version" id="0x7f1000ba" />
    <public type="string" name="setting_walk" id="0x7f1000bb" />
    <public type="string" name="setting_walk_default" id="0x7f1000bc" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f1000bd" />
    <public type="string" name="summary_collapsed_preference_list" id="0x7f1000be" />
    <public type="string" name="update_commit" id="0x7f1000bf" />
    <public type="string" name="update_download" id="0x7f1000c0" />
    <public type="string" name="update_downloading" id="0x7f1000c1" />
    <public type="string" name="update_ignore" id="0x7f1000c2" />
    <public type="string" name="update_last" id="0x7f1000c3" />
    <public type="string" name="update_time" id="0x7f1000c4" />
    <public type="string" name="update_title" id="0x7f1000c5" />
    <public type="string" name="v7_preference_off" id="0x7f1000c6" />
    <public type="string" name="v7_preference_on" id="0x7f1000c7" />
    <public type="string" name="welcome_btn_txt" id="0x7f1000c8" />
    <public type="string" name="zoom_in" id="0x7f1000c9" />
    <public type="string" name="zoom_out" id="0x7f1000ca" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f110000" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f110001" />
    <public type="style" name="AndroidThemeColorAccentYellow" id="0x7f110002" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f110003" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f110004" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f110005" />
    <public type="style" name="Animation.Design.BottomSheetDialog" id="0x7f110006" />
    <public type="style" name="Animation.MaterialComponents.BottomSheetDialog" id="0x7f110007" />
    <public type="style" name="AppTheme" id="0x7f110008" />
    <public type="style" name="AppTheme.AppBarOverlay" id="0x7f110009" />
    <public type="style" name="AppTheme.Button" id="0x7f11000a" />
    <public type="style" name="AppTheme.NoActionBar" id="0x7f11000b" />
    <public type="style" name="AppTheme.PopupOverlay" id="0x7f11000c" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f11000d" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f11000e" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f11000f" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f110010" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f110011" />
    <public type="style" name="Base.CardView" id="0x7f110012" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f110013" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f110014" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f110015" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f110016" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f110017" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f110018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f110019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f11001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f11001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f11001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f11001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f11001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f11001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f110020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f110021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f110022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f110023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f110024" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f110025" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f110026" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f110027" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f110028" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f110029" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f11002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f11002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f11002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f11002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f11002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f11002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f110030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f110031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f110032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f110033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f110034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f110035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f110036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f110037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f110038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f110039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f11003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f11003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f11003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f11003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f11003e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f11003f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f110040" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f110041" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f110042" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f110043" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f110044" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Badge" id="0x7f110045" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Button" id="0x7f110046" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Headline6" id="0x7f110047" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Subtitle2" id="0x7f110048" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f110049" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f11004a" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f11004b" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f11004c" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f11004d" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f11004e" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f11004f" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f110050" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f110051" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f110052" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f110053" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f110054" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f110055" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f110056" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f110057" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f110058" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f110059" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f11005a" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f11005b" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f11005c" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f11005d" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f11005e" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Bridge" id="0x7f11005f" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f110060" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f110061" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f110062" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f110063" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f110064" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f110065" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f110066" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f110067" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f110068" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f110069" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f11006a" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f11006b" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f11006c" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f11006d" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f11006e" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f11006f" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f110070" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f110071" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f110072" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f110073" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f110074" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f110075" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f110076" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f110077" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f110078" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f110079" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f11007a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f11007b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" id="0x7f11007c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f11007d" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f11007e" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f11007f" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f110080" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f110081" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f110082" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f110083" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f110084" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f110085" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f110086" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f110087" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f110088" />
    <public type="style" name="Base.V21.Theme.MaterialComponents" id="0x7f110089" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Dialog" id="0x7f11008a" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light" id="0x7f11008b" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light.Dialog" id="0x7f11008c" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f11008d" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f11008e" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f11008f" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f110090" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f110091" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f110092" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f110093" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f110094" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f110095" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f110096" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f110097" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f110098" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f110099" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f11009a" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f11009b" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f11009c" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f11009d" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f11009e" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f11009f" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f1100a0" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f1100a1" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f1100a2" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f1100a3" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f1100a4" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f1100a5" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f1100a6" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f1100a7" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f1100a8" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f1100a9" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f1100aa" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f1100ab" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f1100ac" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f1100ad" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f1100ae" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f1100af" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f1100b0" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f1100b1" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f1100b2" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f1100b3" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f1100b4" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f1100b5" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f1100b6" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f1100b7" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f1100b8" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f1100b9" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f1100ba" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f1100bb" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f1100bc" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f1100bd" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f1100be" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1100bf" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f1100c0" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1100c1" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f1100c2" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f1100c3" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f1100c4" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f1100c5" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f1100c6" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f1100c7" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f1100c8" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f1100c9" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f1100ca" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f1100cb" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f1100cc" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f1100cd" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f1100ce" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f1100cf" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f1100d0" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f1100d1" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f1100d2" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f1100d3" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f1100d4" />
    <public type="style" name="Base.Widget.AppCompat.TextView" id="0x7f1100d5" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f1100d6" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f1100d7" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1100d8" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f1100d9" />
    <public type="style" name="Base.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f1100da" />
    <public type="style" name="Base.Widget.MaterialComponents.CheckedTextView" id="0x7f1100db" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f1100dc" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu" id="0x7f1100dd" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f1100de" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f1100df" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f1100e0" />
    <public type="style" name="Base.Widget.MaterialComponents.Slider" id="0x7f1100e1" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f1100e2" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f1100e3" />
    <public type="style" name="Base.Widget.MaterialComponents.TextView" id="0x7f1100e4" />
    <public type="style" name="BasePreferenceThemeOverlay" id="0x7f1100e5" />
    <public type="style" name="CardView" id="0x7f1100e6" />
    <public type="style" name="CardView.Dark" id="0x7f1100e7" />
    <public type="style" name="CardView.Light" id="0x7f1100e8" />
    <public type="style" name="DialogAnimFadeInFadeOut" id="0x7f1100e9" />
    <public type="style" name="DialogBaseAnimation" id="0x7f1100ea" />
    <public type="style" name="EmptyTheme" id="0x7f1100eb" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents" id="0x7f1100ec" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Body.Text" id="0x7f1100ed" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" id="0x7f1100ee" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" id="0x7f1100ef" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f1100f0" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" id="0x7f1100f1" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f1100f2" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" id="0x7f1100f3" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f1100f4" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" id="0x7f1100f5" />
    <public type="style" name="Platform.AppCompat" id="0x7f1100f6" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f1100f7" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f1100f8" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f1100f9" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f1100fa" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f1100fb" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f1100fc" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f1100fd" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f1100fe" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f1100ff" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f110100" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f110101" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f110102" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f110103" />
    <public type="style" name="Preference" id="0x7f110104" />
    <public type="style" name="Preference.Category" id="0x7f110105" />
    <public type="style" name="Preference.Category.Material" id="0x7f110106" />
    <public type="style" name="Preference.CheckBoxPreference" id="0x7f110107" />
    <public type="style" name="Preference.CheckBoxPreference.Material" id="0x7f110108" />
    <public type="style" name="Preference.DialogPreference" id="0x7f110109" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference" id="0x7f11010a" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference.Material" id="0x7f11010b" />
    <public type="style" name="Preference.DialogPreference.Material" id="0x7f11010c" />
    <public type="style" name="Preference.DropDown" id="0x7f11010d" />
    <public type="style" name="Preference.DropDown.Material" id="0x7f11010e" />
    <public type="style" name="Preference.Information" id="0x7f11010f" />
    <public type="style" name="Preference.Information.Material" id="0x7f110110" />
    <public type="style" name="Preference.Material" id="0x7f110111" />
    <public type="style" name="Preference.PreferenceScreen" id="0x7f110112" />
    <public type="style" name="Preference.PreferenceScreen.Material" id="0x7f110113" />
    <public type="style" name="Preference.SeekBarPreference" id="0x7f110114" />
    <public type="style" name="Preference.SeekBarPreference.Material" id="0x7f110115" />
    <public type="style" name="Preference.SwitchPreference" id="0x7f110116" />
    <public type="style" name="Preference.SwitchPreference.Material" id="0x7f110117" />
    <public type="style" name="Preference.SwitchPreferenceCompat" id="0x7f110118" />
    <public type="style" name="Preference.SwitchPreferenceCompat.Material" id="0x7f110119" />
    <public type="style" name="PreferenceCategoryTitleTextStyle" id="0x7f11011a" />
    <public type="style" name="PreferenceFragment" id="0x7f11011b" />
    <public type="style" name="PreferenceFragment.Material" id="0x7f11011c" />
    <public type="style" name="PreferenceFragmentList" id="0x7f11011d" />
    <public type="style" name="PreferenceFragmentList.Material" id="0x7f11011e" />
    <public type="style" name="PreferenceSummaryTextStyle" id="0x7f11011f" />
    <public type="style" name="PreferenceThemeOverlay" id="0x7f110120" />
    <public type="style" name="PreferenceThemeOverlay.v14" id="0x7f110121" />
    <public type="style" name="PreferenceThemeOverlay.v14.Material" id="0x7f110122" />
    <public type="style" name="RippleWhite" id="0x7f110123" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f110124" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f110125" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f110126" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f110127" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f110128" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f110129" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f11012a" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f11012b" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f11012c" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f11012d" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f11012e" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f11012f" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f110130" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f110131" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f110132" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f110133" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f110134" />
    <public type="style" name="ShapeAppearance.MaterialComponents" id="0x7f110135" />
    <public type="style" name="ShapeAppearance.MaterialComponents.LargeComponent" id="0x7f110136" />
    <public type="style" name="ShapeAppearance.MaterialComponents.MediumComponent" id="0x7f110137" />
    <public type="style" name="ShapeAppearance.MaterialComponents.SmallComponent" id="0x7f110138" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Test" id="0x7f110139" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Tooltip" id="0x7f11013a" />
    <public type="style" name="ShapeAppearanceOverlay" id="0x7f11013b" />
    <public type="style" name="ShapeAppearanceOverlay.BottomLeftDifferentCornerSize" id="0x7f11013c" />
    <public type="style" name="ShapeAppearanceOverlay.BottomRightCut" id="0x7f11013d" />
    <public type="style" name="ShapeAppearanceOverlay.Cut" id="0x7f11013e" />
    <public type="style" name="ShapeAppearanceOverlay.DifferentCornerSize" id="0x7f11013f" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" id="0x7f110140" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.Chip" id="0x7f110141" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" id="0x7f110142" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" id="0x7f110143" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f110144" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" id="0x7f110145" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" id="0x7f110146" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" id="0x7f110147" />
    <public type="style" name="ShapeAppearanceOverlay.TopLeftCut" id="0x7f110148" />
    <public type="style" name="ShapeAppearanceOverlay.TopRightDifferentCornerSize" id="0x7f110149" />
    <public type="style" name="Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f11014a" />
    <public type="style" name="Test.Theme.MaterialComponents.MaterialCalendar" id="0x7f11014b" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar" id="0x7f11014c" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f11014d" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f11014e" />
    <public type="style" name="TestStyleWithLineHeight" id="0x7f11014f" />
    <public type="style" name="TestStyleWithLineHeightAppearance" id="0x7f110150" />
    <public type="style" name="TestStyleWithThemeLineHeightAttribute" id="0x7f110151" />
    <public type="style" name="TestStyleWithoutLineHeight" id="0x7f110152" />
    <public type="style" name="TestThemeWithLineHeight" id="0x7f110153" />
    <public type="style" name="TestThemeWithLineHeightDisabled" id="0x7f110154" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f110155" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f110156" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f110157" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f110158" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f110159" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f11015a" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f11015b" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f11015c" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f11015d" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f11015e" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f11015f" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f110160" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f110161" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f110162" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f110163" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f110164" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f110165" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f110166" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f110167" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f110168" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f110169" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f11016a" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f11016b" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f11016c" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f11016d" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f11016e" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f11016f" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f110170" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f110171" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f110172" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f110173" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f110174" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f110175" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f110176" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f110177" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f110178" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f110179" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f11017a" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f11017b" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f11017c" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f11017d" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f11017e" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f11017f" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f110180" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f110181" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f110182" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f110183" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f110184" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f110185" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f110186" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f110187" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f110188" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f110189" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f11018a" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f11018b" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f11018c" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f11018d" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f11018e" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f11018f" />
    <public type="style" name="TextAppearance.Design.Placeholder" id="0x7f110190" />
    <public type="style" name="TextAppearance.Design.Prefix" id="0x7f110191" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f110192" />
    <public type="style" name="TextAppearance.Design.Suffix" id="0x7f110193" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f110194" />
    <public type="style" name="TextAppearance.MaterialComponents.Badge" id="0x7f110195" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f110196" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f110197" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f110198" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f110199" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f11019a" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f11019b" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f11019c" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f11019d" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f11019e" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f11019f" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f1101a0" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f1101a1" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f1101a2" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f1101a3" />
    <public type="style" name="TextAppearance.MaterialComponents.Tooltip" id="0x7f1101a4" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f1101a5" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f1101a6" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f1101a7" />
    <public type="style" name="Theme.AppCompat" id="0x7f1101a8" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f1101a9" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f1101aa" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f1101ab" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f1101ac" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f1101ad" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f1101ae" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f1101af" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f1101b0" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f1101b1" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f1101b2" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f1101b3" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f1101b4" />
    <public type="style" name="Theme.AppCompat.Empty" id="0x7f1101b5" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f1101b6" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f1101b7" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f1101b8" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f1101b9" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f1101ba" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f1101bb" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f1101bc" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f1101bd" />
    <public type="style" name="Theme.Design" id="0x7f1101be" />
    <public type="style" name="Theme.Design.BottomSheetDialog" id="0x7f1101bf" />
    <public type="style" name="Theme.Design.Light" id="0x7f1101c0" />
    <public type="style" name="Theme.Design.Light.BottomSheetDialog" id="0x7f1101c1" />
    <public type="style" name="Theme.Design.Light.NoActionBar" id="0x7f1101c2" />
    <public type="style" name="Theme.Design.NoActionBar" id="0x7f1101c3" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f1101c4" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f1101c5" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f1101c6" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f1101c7" />
    <public type="style" name="Theme.MaterialComponents.DayNight" id="0x7f1101c8" />
    <public type="style" name="Theme.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f1101c9" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Bridge" id="0x7f1101ca" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar" id="0x7f1101cb" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" id="0x7f1101cc" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog" id="0x7f1101cd" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert" id="0x7f1101ce" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" id="0x7f1101cf" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Bridge" id="0x7f1101d0" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" id="0x7f1101d1" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" id="0x7f1101d2" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" id="0x7f1101d3" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" id="0x7f1101d4" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DialogWhenLarge" id="0x7f1101d5" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar" id="0x7f1101d6" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" id="0x7f1101d7" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f1101d8" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f1101d9" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert.Bridge" id="0x7f1101da" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Bridge" id="0x7f1101db" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize" id="0x7f1101dc" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" id="0x7f1101dd" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f1101de" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" id="0x7f1101df" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f1101e0" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f1101e1" />
    <public type="style" name="Theme.MaterialComponents.Light.BarSize" id="0x7f1101e2" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f1101e3" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f1101e4" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f1101e5" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f1101e6" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f1101e7" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f1101e8" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" id="0x7f1101e9" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f1101ea" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f1101eb" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" id="0x7f1101ec" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f1101ed" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" id="0x7f1101ee" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f1101ef" />
    <public type="style" name="Theme.MaterialComponents.Light.LargeTouch" id="0x7f1101f0" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f1101f1" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f1101f2" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f1101f3" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f1101f4" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f1101f5" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f1101f6" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f1101f7" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f1101f8" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight" id="0x7f1101f9" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight.ActionBar" id="0x7f1101fa" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f1101fb" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f1101fc" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f1101fd" />
    <public type="style" name="ThemeOverlay.Design.TextInputEditText" id="0x7f1101fe" />
    <public type="style" name="ThemeOverlay.MaterialComponents" id="0x7f1101ff" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar" id="0x7f110200" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Primary" id="0x7f110201" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Surface" id="0x7f110202" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" id="0x7f110203" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f110204" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f110205" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f110206" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f110207" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" id="0x7f110208" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" id="0x7f110209" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f11020a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f11020b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f11020c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f11020d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f11020e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f11020f" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f110210" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f110211" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.BottomSheetDialog" id="0x7f110212" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f110213" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f110214" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" id="0x7f110215" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" id="0x7f110216" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar" id="0x7f110217" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" id="0x7f110218" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" id="0x7f110219" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner" id="0x7f11021a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar" id="0x7f11021b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f11021c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f11021d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f11021e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f11021f" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f110220" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f110221" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Primary" id="0x7f110222" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Surface" id="0x7f110223" />
    <public type="style" name="ThemeOverlayColorAccentRed" id="0x7f110224" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f110225" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f110226" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f110227" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f110228" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f110229" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f11022a" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f11022b" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f11022c" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f11022d" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f11022e" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f11022f" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f110230" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f110231" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f110232" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f110233" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f110234" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f110235" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f110236" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f110237" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f110238" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f110239" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f11023a" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f11023b" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f11023c" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f11023d" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f11023e" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f11023f" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f110240" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f110241" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f110242" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f110243" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f110244" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f110245" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f110246" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f110247" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f110248" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f110249" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f11024a" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f11024b" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f11024c" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f11024d" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f11024e" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f11024f" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f110250" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f110251" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f110252" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f110253" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f110254" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f110255" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f110256" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f110257" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f110258" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f110259" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f11025a" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f11025b" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f11025c" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f11025d" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f11025e" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f11025f" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f110260" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f110261" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f110262" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f110263" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f110264" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f110265" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f110266" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f110267" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f110268" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f110269" />
    <public type="style" name="Widget.AppCompat.TextView" id="0x7f11026a" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f11026b" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f11026c" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f11026d" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f11026e" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f11026f" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f110270" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f110271" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f110272" />
    <public type="style" name="Widget.Design.CollapsingToolbar" id="0x7f110273" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f110274" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f110275" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f110276" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f110277" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f110278" />
    <public type="style" name="Widget.Design.TextInputEditText" id="0x7f110279" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f11027a" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Primary" id="0x7f11027b" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.PrimarySurface" id="0x7f11027c" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Solid" id="0x7f11027d" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Surface" id="0x7f11027e" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Primary" id="0x7f11027f" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" id="0x7f110280" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Surface" id="0x7f110281" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f110282" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f110283" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f110284" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f110285" />
    <public type="style" name="Widget.MaterialComponents.Badge" id="0x7f110286" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f110287" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.Colored" id="0x7f110288" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" id="0x7f110289" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f11028a" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.Colored" id="0x7f11028b" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" id="0x7f11028c" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet" id="0x7f11028d" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f11028e" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f11028f" />
    <public type="style" name="Widget.MaterialComponents.Button.Icon" id="0x7f110290" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f110291" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton.Icon" id="0x7f110292" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f110293" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f110294" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" id="0x7f110295" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" id="0x7f110296" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Icon" id="0x7f110297" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Snackbar" id="0x7f110298" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f110299" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" id="0x7f11029a" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f11029b" />
    <public type="style" name="Widget.MaterialComponents.CheckedTextView" id="0x7f11029c" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f11029d" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f11029e" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f11029f" />
    <public type="style" name="Widget.MaterialComponents.Chip.Filter" id="0x7f1102a0" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f1102a1" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.CheckBox" id="0x7f1102a2" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.RadioButton" id="0x7f1102a3" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.Switch" id="0x7f1102a4" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton" id="0x7f1102a5" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" id="0x7f1102a6" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f1102a7" />
    <public type="style" name="Widget.MaterialComponents.Light.ActionBar.Solid" id="0x7f1102a8" />
    <public type="style" name="Widget.MaterialComponents.MaterialButtonToggleGroup" id="0x7f1102a9" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar" id="0x7f1102aa" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f1102ab" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" id="0x7f1102ac" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f1102ad" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Today" id="0x7f1102ae" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayTextView" id="0x7f1102af" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f1102b0" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" id="0x7f1102b1" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" id="0x7f1102b2" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" id="0x7f1102b3" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" id="0x7f1102b4" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" id="0x7f1102b5" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" id="0x7f1102b6" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f1102b7" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Item" id="0x7f1102b8" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year" id="0x7f1102b9" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" id="0x7f1102ba" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Today" id="0x7f1102bb" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f1102bc" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu" id="0x7f1102bd" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f1102be" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f1102bf" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f1102c0" />
    <public type="style" name="Widget.MaterialComponents.ShapeableImageView" id="0x7f1102c1" />
    <public type="style" name="Widget.MaterialComponents.Slider" id="0x7f1102c2" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f1102c3" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.FullWidth" id="0x7f1102c4" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.TextView" id="0x7f1102c5" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f1102c6" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.Colored" id="0x7f1102c7" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.PrimarySurface" id="0x7f1102c8" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f1102c9" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f1102ca" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f1102cb" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f1102cc" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f1102cd" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f1102ce" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" id="0x7f1102cf" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" id="0x7f1102d0" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f1102d1" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f1102d2" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" id="0x7f1102d3" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" id="0x7f1102d4" />
    <public type="style" name="Widget.MaterialComponents.TextView" id="0x7f1102d5" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f1102d6" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Primary" id="0x7f1102d7" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.PrimarySurface" id="0x7f1102d8" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Surface" id="0x7f1102d9" />
    <public type="style" name="Widget.MaterialComponents.Tooltip" id="0x7f1102da" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f1102db" />
    <public type="xml" name="preferences_main" id="0x7f130000" />
    <public type="xml" name="provider_paths" id="0x7f130001" />
    <public type="xml" name="standalone_badge" id="0x7f130002" />
    <public type="xml" name="standalone_badge_gravity_bottom_end" id="0x7f130003" />
    <public type="xml" name="standalone_badge_gravity_bottom_start" id="0x7f130004" />
    <public type="xml" name="standalone_badge_gravity_top_start" id="0x7f130005" />
    <public type="xml" name="standalone_badge_offset" id="0x7f130006" />
    <public type="xml" name="epic_paths" id="0x7f130007" />
</resources>
