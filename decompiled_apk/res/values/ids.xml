<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="id" name="BDLatLngText" />
    <item type="id" name="LocationID" />
    <item type="id" name="LocationText" />
    <item type="id" name="RadioGroupMapType" />
    <item type="id" name="TimeText" />
    <item type="id" name="WGSLatLngText" />
    <item type="id" name="accessibility_action_clickable_span" />
    <item type="id" name="accessibility_custom_action_0" />
    <item type="id" name="accessibility_custom_action_1" />
    <item type="id" name="accessibility_custom_action_10" />
    <item type="id" name="accessibility_custom_action_11" />
    <item type="id" name="accessibility_custom_action_12" />
    <item type="id" name="accessibility_custom_action_13" />
    <item type="id" name="accessibility_custom_action_14" />
    <item type="id" name="accessibility_custom_action_15" />
    <item type="id" name="accessibility_custom_action_16" />
    <item type="id" name="accessibility_custom_action_17" />
    <item type="id" name="accessibility_custom_action_18" />
    <item type="id" name="accessibility_custom_action_19" />
    <item type="id" name="accessibility_custom_action_2" />
    <item type="id" name="accessibility_custom_action_20" />
    <item type="id" name="accessibility_custom_action_21" />
    <item type="id" name="accessibility_custom_action_22" />
    <item type="id" name="accessibility_custom_action_23" />
    <item type="id" name="accessibility_custom_action_24" />
    <item type="id" name="accessibility_custom_action_25" />
    <item type="id" name="accessibility_custom_action_26" />
    <item type="id" name="accessibility_custom_action_27" />
    <item type="id" name="accessibility_custom_action_28" />
    <item type="id" name="accessibility_custom_action_29" />
    <item type="id" name="accessibility_custom_action_3" />
    <item type="id" name="accessibility_custom_action_30" />
    <item type="id" name="accessibility_custom_action_31" />
    <item type="id" name="accessibility_custom_action_4" />
    <item type="id" name="accessibility_custom_action_5" />
    <item type="id" name="accessibility_custom_action_6" />
    <item type="id" name="accessibility_custom_action_7" />
    <item type="id" name="accessibility_custom_action_8" />
    <item type="id" name="accessibility_custom_action_9" />
    <item type="id" name="action_bar" />
    <item type="id" name="action_bar_activity_content" />
    <item type="id" name="action_bar_container" />
    <item type="id" name="action_bar_root" />
    <item type="id" name="action_bar_spinner" />
    <item type="id" name="action_bar_subtitle" />
    <item type="id" name="action_bar_title" />
    <item type="id" name="action_container" />
    <item type="id" name="action_context_bar" />
    <item type="id" name="action_delete" />
    <item type="id" name="action_divider" />
    <item type="id" name="action_image" />
    <item type="id" name="action_menu_divider" />
    <item type="id" name="action_menu_presenter" />
    <item type="id" name="action_mode_bar" />
    <item type="id" name="action_mode_bar_stub" />
    <item type="id" name="action_mode_close_button" />
    <item type="id" name="action_search" />
    <item type="id" name="action_text" />
    <item type="id" name="actions" />
    <item type="id" name="activity_chooser_view_content" />
    <item type="id" name="alertTitle" />
    <item type="id" name="androidx_window_activity_scope" />
    <item type="id" name="backioc" />
    <item type="id" name="baohuimg" />
    <item type="id" name="baohutv" />
    <item type="id" name="bdMapView" />
    <item type="id" name="bianjiimg" />
    <item type="id" name="bmapView" />
    <item type="id" name="btnBack" />
    <item type="id" name="btnGo" />
    <item type="id" name="btn_center" />
    <item type="id" name="btn_east" />
    <item type="id" name="btn_north" />
    <item type="id" name="btn_north_east" />
    <item type="id" name="btn_north_west" />
    <item type="id" name="btn_south" />
    <item type="id" name="btn_south_east" />
    <item type="id" name="btn_south_west" />
    <item type="id" name="btn_west" />
    <item type="id" name="buttonPanel" />
    <item type="id" name="cancel_button" />
    <item type="id" name="checkbox" />
    <item type="id" name="checked" />
    <item type="id" name="chip" />
    <item type="id" name="chip1" />
    <item type="id" name="chip2" />
    <item type="id" name="chip3" />
    <item type="id" name="chip_group" />
    <item type="id" name="chronometer" />
    <item type="id" name="city" />
    <item type="id" name="clPosition" />
    <item type="id" name="confirm_button" />
    <item type="id" name="container" />
    <item type="id" name="content" />
    <item type="id" name="contentPanel" />
    <item type="id" name="coordinator" />
    <item type="id" name="cur_position" />
    <item type="id" name="customPanel" />
    <item type="id" name="date_picker" />
    <item type="id" name="date_picker_actions" />
    <item type="id" name="decor_content_parent" />
    <item type="id" name="default_activity_button" />
    <item type="id" name="design_bottom_sheet" />
    <item type="id" name="design_menu_item_action_area" />
    <item type="id" name="design_menu_item_action_area_stub" />
    <item type="id" name="design_menu_item_text" />
    <item type="id" name="design_navigation_view" />
    <item type="id" name="dialog_button" />
    <item type="id" name="dingweiimg" />
    <item type="id" name="drawer_layout" />
    <item type="id" name="edit_query" />
    <item type="id" name="etCode" />
    <item type="id" name="et_address" />
    <item type="id" name="expand_activities_button" />
    <item type="id" name="expanded_menu" />
    <item type="id" name="faBtnStart" />
    <item type="id" name="fragment_container_view_tag" />
    <item type="id" name="ghost_view" />
    <item type="id" name="ghost_view_holder" />
    <item type="id" name="group_divider" />
    <item type="id" name="home" />
    <item type="id" name="icon" />
    <item type="id" name="icon_frame" />
    <item type="id" name="icon_group" />
    <item type="id" name="image" />
    <item type="id" name="info" />
    <item type="id" name="input_pos" />
    <item type="id" name="input_position_cancel" />
    <item type="id" name="input_position_ok" />
    <item type="id" name="item_touch_helper_previous_elevation" />
    <item type="id" name="ivBack" />
    <item type="id" name="ivLogo" />
    <item type="id" name="joystick_his_close" />
    <item type="id" name="joystick_his_record_list_view" />
    <item type="id" name="joystick_his_record_no_textview" />
    <item type="id" name="joystick_his_searchView" />
    <item type="id" name="joystick_his_tips" />
    <item type="id" name="joystick_latitude" />
    <item type="id" name="joystick_longitude" />
    <item type="id" name="joystick_map_searchView" />
    <item type="id" name="joystick_map_tips" />
    <item type="id" name="largeLabel" />
    <item type="id" name="line" />
    <item type="id" name="line1" />
    <item type="id" name="line3" />
    <item type="id" name="list_item" />
    <item type="id" name="llContent" />
    <item type="id" name="llTop" />
    <item type="id" name="ll_bottom" />
    <item type="id" name="ll_et" />
    <item type="id" name="ll_root" />
    <item type="id" name="mapNormal" />
    <item type="id" name="mapSatellite" />
    <item type="id" name="map_close" />
    <item type="id" name="map_joystick" />
    <item type="id" name="map_search_linear" />
    <item type="id" name="map_search_list_view" />
    <item type="id" name="markwon_drawables_scheduler" />
    <item type="id" name="markwon_drawables_scheduler_last_text_hashcode" />
    <item type="id" name="masked" />
    <item type="id" name="message" />
    <item type="id" name="month_grid" />
    <item type="id" name="month_navigation_bar" />
    <item type="id" name="month_navigation_fragment_toggle" />
    <item type="id" name="month_navigation_next" />
    <item type="id" name="month_navigation_previous" />
    <item type="id" name="month_title" />
    <item type="id" name="motion_base" />
    <item type="id" name="mtrl_calendar_day_selector_frame" />
    <item type="id" name="mtrl_calendar_days_of_week" />
    <item type="id" name="mtrl_calendar_frame" />
    <item type="id" name="mtrl_calendar_main_pane" />
    <item type="id" name="mtrl_calendar_months" />
    <item type="id" name="mtrl_calendar_selection_frame" />
    <item type="id" name="mtrl_calendar_text_input_frame" />
    <item type="id" name="mtrl_calendar_year_selector_frame" />
    <item type="id" name="mtrl_card_checked_layer_id" />
    <item type="id" name="mtrl_child_content_container" />
    <item type="id" name="mtrl_internal_children_alpha_tag" />
    <item type="id" name="mtrl_motion_snapshot_view" />
    <item type="id" name="mtrl_picker_fullscreen" />
    <item type="id" name="mtrl_picker_header" />
    <item type="id" name="mtrl_picker_header_selection_text" />
    <item type="id" name="mtrl_picker_header_title_and_selection" />
    <item type="id" name="mtrl_picker_header_toggle" />
    <item type="id" name="mtrl_picker_text_input_date" />
    <item type="id" name="mtrl_picker_text_input_range_end" />
    <item type="id" name="mtrl_picker_text_input_range_start" />
    <item type="id" name="mtrl_picker_title_text" />
    <item type="id" name="nav_contact" />
    <item type="id" name="nav_dev" />
    <item type="id" name="nav_feedback" />
    <item type="id" name="nav_history" />
    <item type="id" name="nav_settings" />
    <item type="id" name="nav_update" />
    <item type="id" name="nav_view" />
    <item type="id" name="navigation_header_container" />
    <item type="id" name="notification_background" />
    <item type="id" name="notification_main_column" />
    <item type="id" name="notification_main_column_container" />
    <item type="id" name="off" />
    <item type="id" name="on" />
    <item type="id" name="parentPanel" />
    <item type="id" name="parent_matrix" />
    <item type="id" name="poi_address" />
    <item type="id" name="poi_copy" />
    <item type="id" name="poi_fly" />
    <item type="id" name="poi_latitude" />
    <item type="id" name="poi_longitude" />
    <item type="id" name="poi_name" />
    <item type="id" name="poi_save" />
    <item type="id" name="poi_share" />
    <item type="id" name="pos_type_bd" />
    <item type="id" name="preferences_detail" />
    <item type="id" name="preferences_header" />
    <item type="id" name="preferences_sliding_pane_layout" />
    <item type="id" name="progress_circular" />
    <item type="id" name="progress_horizontal" />
    <item type="id" name="radio" />
    <item type="id" name="record_list_view" />
    <item type="id" name="record_no_textview" />
    <item type="id" name="recycler_view" />
    <item type="id" name="reg_agree" />
    <item type="id" name="reg_cancel" />
    <item type="id" name="reg_check" />
    <item type="id" name="reg_request" />
    <item type="id" name="reg_response" />
    <item type="id" name="reg_user_name" />
    <item type="id" name="right_icon" />
    <item type="id" name="right_side" />
    <item type="id" name="rlTitles" />
    <item type="id" name="row_index_key" />
    <item type="id" name="save_non_transition_alpha" />
    <item type="id" name="save_overlay_view" />
    <item type="id" name="scb" />
    <item type="id" name="scrollIndicatorDown" />
    <item type="id" name="scrollIndicatorUp" />
    <item type="id" name="scrollView" />
    <item type="id" name="scroll_view" />
    <item type="id" name="searchView" />
    <item type="id" name="search_badge" />
    <item type="id" name="search_bar" />
    <item type="id" name="search_button" />
    <item type="id" name="search_close_btn" />
    <item type="id" name="search_description" />
    <item type="id" name="search_edit_frame" />
    <item type="id" name="search_go_btn" />
    <item type="id" name="search_history_linear" />
    <item type="id" name="search_history_list_view" />
    <item type="id" name="search_isLoc" />
    <item type="id" name="search_key" />
    <item type="id" name="search_latitude" />
    <item type="id" name="search_linear" />
    <item type="id" name="search_list_view" />
    <item type="id" name="search_longitude" />
    <item type="id" name="search_mag_icon" />
    <item type="id" name="search_plate" />
    <item type="id" name="search_src_text" />
    <item type="id" name="search_timestamp" />
    <item type="id" name="search_voice_btn" />
    <item type="id" name="seekbar" />
    <item type="id" name="seekbar_value" />
    <item type="id" name="select_dialog_listview" />
    <item type="id" name="selelayout" />
    <item type="id" name="settings" />
    <item type="id" name="shortcut" />
    <item type="id" name="show_address2" />
    <item type="id" name="show_jingweidu2" />
    <item type="id" name="smallLabel" />
    <item type="id" name="snackbar_action" />
    <item type="id" name="snackbar_text" />
    <item type="id" name="spacer" />
    <item type="id" name="special_effects_controller_view_tag" />
    <item type="id" name="spinner" />
    <item type="id" name="split_action_bar" />
    <item type="id" name="submenuarrow" />
    <item type="id" name="submit_area" />
    <item type="id" name="sug_city" />
    <item type="id" name="sug_dis" />
    <item type="id" name="sug_key" />
    <item type="id" name="sug_list" />
    <item type="id" name="switchWidget" />
    <item type="id" name="tag_accessibility_actions" />
    <item type="id" name="tag_accessibility_clickable_spans" />
    <item type="id" name="tag_accessibility_heading" />
    <item type="id" name="tag_accessibility_pane_title" />
    <item type="id" name="tag_on_apply_window_listener" />
    <item type="id" name="tag_on_receive_content_listener" />
    <item type="id" name="tag_on_receive_content_mime_types" />
    <item type="id" name="tag_screen_reader_focusable" />
    <item type="id" name="tag_state_description" />
    <item type="id" name="tag_transition_group" />
    <item type="id" name="tag_unhandled_key_event_manager" />
    <item type="id" name="tag_unhandled_key_listeners" />
    <item type="id" name="tag_window_insets_animation_callback" />
    <item type="id" name="test_checkbox_android_button_tint" />
    <item type="id" name="test_checkbox_app_button_tint" />
    <item type="id" name="test_radiobutton_android_button_tint" />
    <item type="id" name="test_radiobutton_app_button_tint" />
    <item type="id" name="text" />
    <item type="id" name="text2" />
    <item type="id" name="textSpacerNoButtons" />
    <item type="id" name="textSpacerNoTitle" />
    <item type="id" name="text_input_end_icon" />
    <item type="id" name="text_input_start_icon" />
    <item type="id" name="textinput_counter" />
    <item type="id" name="textinput_error" />
    <item type="id" name="textinput_helper_text" />
    <item type="id" name="textinput_placeholder" />
    <item type="id" name="textinput_prefix_text" />
    <item type="id" name="textinput_suffix_text" />
    <item type="id" name="tiaozhuan" />
    <item type="id" name="time" />
    <item type="id" name="title" />
    <item type="id" name="titleDividerNoCustom" />
    <item type="id" name="title_template" />
    <item type="id" name="toGo" />
    <item type="id" name="toolbar" />
    <item type="id" name="topPanel" />
    <item type="id" name="touch_outside" />
    <item type="id" name="transition_current_scene" />
    <item type="id" name="transition_layout_save" />
    <item type="id" name="transition_position" />
    <item type="id" name="transition_scene_layoutid_cache" />
    <item type="id" name="transition_transform" />
    <item type="id" name="tvHis" />
    <item type="id" name="tvN" />
    <item type="id" name="tvPosition" />
    <item type="id" name="tvSelect" />
    <item type="id" name="tv_agree" />
    <item type="id" name="tv_cancel" />
    <item type="id" name="tv_cancle" />
    <item type="id" name="tv_confirm" />
    <item type="id" name="tv_content" />
    <item type="id" name="tv_splash_tag" />
    <item type="id" name="tv_top" />
    <item type="id" name="unchecked" />
    <item type="id" name="up" />
    <item type="id" name="update_agree" />
    <item type="id" name="update_commit" />
    <item type="id" name="update_content" />
    <item type="id" name="update_ignore" />
    <item type="id" name="update_time" />
    <item type="id" name="update_title" />
    <item type="id" name="user_icon" />
    <item type="id" name="user_name" />
    <item type="id" name="view_offset_helper" />
    <item type="id" name="view_transition" />
    <item type="id" name="view_tree_lifecycle_owner" />
    <item type="id" name="view_tree_on_back_pressed_dispatcher_owner" />
    <item type="id" name="view_tree_saved_state_registry_owner" />
    <item type="id" name="view_tree_view_model_store_owner" />
    <item type="id" name="visible_removing_fragment_view_tag" />
    <item type="id" name="zero_corner_chip" />
    <item type="id" name="zoom_in" />
    <item type="id" name="zoom_out" />
</resources>
