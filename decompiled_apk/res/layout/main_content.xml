<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.google.android.material.appbar.AppBarLayout android:theme="@style/AppTheme.AppBarOverlay" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <androidx.appcompat.widget.Toolbar android:id="@id/toolbar" android:background="@color/colorPrimary" android:layout_width="fill_parent" android:layout_height="?actionBarSize" app:popupTheme="@style/AppTheme.PopupOverlay" />
        </com.google.android.material.appbar.AppBarLayout>
        <include android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="?actionBarSize" layout="@layout/main_map" />
        <com.google.android.material.floatingactionbutton.FloatingActionButton android:layout_gravity="start|bottom" android:id="@id/faBtnStart" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="80.0dip" android:contentDescription="@null" android:layout_marginStart="30.0dip" app:elevation="6.0dip" app:pressedTranslationZ="12.0dip" app:srcCompat="@drawable/ic_position" />
        <LinearLayout android:id="@id/search_linear" android:background="#ddffffff" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="?actionBarSize">
            <ListView android:id="@id/search_list_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="10.0dip" />
        </LinearLayout>
        <LinearLayout android:id="@id/search_history_linear" android:background="#ddffffff" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="?actionBarSize">
            <ListView android:id="@id/search_history_list_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="10.0dip" />
        </LinearLayout>
    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
