<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:id="@id/rlTitles" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="48.0dip">
        <ImageView android:id="@id/ivBack" android:padding="15.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@drawable/back" android:layout_centerVertical="true" />
        <LinearLayout android:gravity="center" android:layout_gravity="center_vertical" android:background="@drawable/bg_white2" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_weight="1.0" android:paddingHorizontal="10.0dip">
            <ImageView android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@drawable/ic_search" />
            <EditText android:textSize="14.0sp" android:textColor="@color/black" android:textColorHint="#ff919fac" android:gravity="center_vertical" android:id="@id/city" android:background="@null" android:paddingLeft="10.0dip" android:paddingTop="7.0dip" android:paddingRight="10.0dip" android:paddingBottom="7.0dip" android:layout_width="fill_parent" android:layout_height="50.0dip" android:hint="搜索地点" android:layout_weight="1.0" android:inputType="text" android:imeOptions="actionSearch" />
        </LinearLayout>
        <TextView android:textColor="@color/white" android:layout_gravity="center_vertical" android:id="@id/clear_text" android:padding="5.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="10.0dip" android:text="清除" />
    </LinearLayout>
    <ListView android:id="@id/sug_list" android:layout_width="fill_parent" android:layout_height="wrap_content" android:divider="@null" android:dividerHeight="0.0dip" />
</LinearLayout>
