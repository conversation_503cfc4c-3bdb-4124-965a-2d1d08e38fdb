<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="fill_parent" android:layout_height="fill_parent" android:baselineAligned="false"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_vertical" android:layout_gravity="center_vertical" android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_margin="7.0dip" android:layout_weight="1.0">
        <TextView android:textSize="20.0sp" android:id="@id/poi_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
        <TextView android:id="@id/poi_address" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
        <TextView android:id="@id/poi_longitude" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
        <TextView android:id="@id/poi_latitude" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
    </LinearLayout>
</LinearLayout>
