<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="fill_parent" android:layout_height="fill_parent" android:baselineAligned="false"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_vertical" android:layout_gravity="center_vertical" android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_margin="7.0dip" android:layout_weight="1.0">
        <TextView android:textSize="15.0sp" android:textColor="#ff333333" android:id="@id/search_key" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        <TextView android:textSize="13.0sp" android:textColor="@color/gray" android:id="@id/search_description" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
        <TextView android:id="@id/search_timestamp" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        <TextView android:id="@id/search_isLoc" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
        <TextView android:id="@id/search_longitude" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
        <TextView android:id="@id/search_latitude" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
    </LinearLayout>
</LinearLayout>
