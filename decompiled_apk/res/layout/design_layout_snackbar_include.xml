<?xml version="1.0" encoding="utf-8"?>
<view android:theme="@style/ThemeOverlay.AppCompat.Dark" android:layout_gravity="bottom" android:layout_width="fill_parent" android:layout_height="wrap_content" class="com.google.android.material.snackbar.SnackbarContentLayout"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textAppearance="@style/TextAppearance.Design.Snackbar.Message" android:ellipsize="end" android:layout_gravity="start|center" android:id="@id/snackbar_text" android:paddingLeft="@dimen/design_snackbar_padding_horizontal" android:paddingTop="@dimen/design_snackbar_padding_vertical" android:paddingRight="@dimen/design_snackbar_padding_horizontal" android:paddingBottom="@dimen/design_snackbar_padding_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="@integer/design_snackbar_text_max_lines" android:layout_weight="1.0" android:textAlignment="viewStart" />
    <Button android:textColor="?colorAccent" android:layout_gravity="end|center" android:id="@id/snackbar_action" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/design_snackbar_extra_spacing_horizontal" android:minWidth="48.0dip" android:layout_marginStart="@dimen/design_snackbar_extra_spacing_horizontal" style="?borderlessButtonStyle" />
</view>
