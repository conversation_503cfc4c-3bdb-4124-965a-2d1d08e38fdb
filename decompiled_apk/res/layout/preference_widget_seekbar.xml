<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:paddingRight="?android:scrollbarSize" android:clipChildren="false" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="?android:listPreferredItemHeight" android:paddingEnd="?android:scrollbarSize"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:layout_gravity="center" android:id="@android:id/icon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="@dimen/preference_icon_minWidth" />
    <RelativeLayout android:clipChildren="false" android:clipToPadding="false" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="8.0dip" android:layout_marginBottom="6.0dip" android:layout_weight="1.0" android:layout_marginStart="16.0dip" android:layout_marginEnd="8.0dip">
        <TextView android:textAppearance="?android:textAppearanceMedium" android:ellipsize="marquee" android:id="@android:id/title" android:fadingEdge="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" />
        <TextView android:textAppearance="?android:textAppearanceSmall" android:textColor="?android:textColorSecondary" android:id="@android:id/summary" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="4" android:layout_below="@android:id/title" android:layout_alignLeft="@android:id/title" android:layout_alignStart="@android:id/title" />
        <androidx.preference.UnPressableLinearLayout android:clipChildren="false" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_below="@android:id/summary" android:layout_alignLeft="@android:id/title" android:layout_alignStart="@android:id/title">
            <SeekBar android:id="@id/seekbar" android:background="@null" android:paddingLeft="@dimen/preference_seekbar_padding_horizontal" android:paddingRight="22.0dip" android:focusable="false" android:clickable="false" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" android:paddingStart="@dimen/preference_seekbar_padding_horizontal" android:paddingEnd="22.0dip" />
            <TextView android:textAppearance="?android:textAppearanceMedium" android:ellipsize="marquee" android:gravity="right|center" android:id="@id/seekbar_value" android:fadingEdge="horizontal" android:layout_width="@dimen/preference_seekbar_value_minWidth" android:layout_height="fill_parent" android:singleLine="true" android:fontFamily="sans-serif-condensed" />
        </androidx.preference.UnPressableLinearLayout>
    </RelativeLayout>
</LinearLayout>
