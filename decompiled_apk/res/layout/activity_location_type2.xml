<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.baidu.mapapi.map.MapView android:id="@id/bmapView" android:clickable="true" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </LinearLayout>
    <LinearLayout android:gravity="start" android:orientation="horizontal" android:id="@id/llContent" android:background="@drawable/white_tran" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginTop="10.0dip" android:layout_marginHorizontal="10.0dip">
        <LinearLayout android:orientation="horizontal" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <com.zcshou.widgets.MaterialRippleLayout android:layout_width="50.0dip" android:layout_height="50.0dip">
                <ImageView android:id="@id/backioc" android:padding="15.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" android:src="@drawable/back" />
            </com.zcshou.widgets.MaterialRippleLayout>
            <RelativeLayout android:id="@id/selelayout" android:clickable="true" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0">
                <LinearLayout android:gravity="center" android:layout_width="fill_parent" android:layout_height="fill_parent">
                    <ImageView android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@drawable/ic_search" />
                    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:textColorHint="#ff919fac" android:gravity="center_vertical" android:id="@id/et_address" android:paddingLeft="10.0dip" android:paddingTop="7.0dip" android:paddingRight="10.0dip" android:paddingBottom="7.0dip" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="搜索地点" android:layout_weight="1.0" />
                    <TextView android:textColor="#ff919fac" android:id="@id/clear_text" android:padding="5.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="10.0dip" android:text=" ╳" />
                </LinearLayout>
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
    <RelativeLayout android:background="@drawable/sel_guide_btn6" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true">
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <ImageView android:id="@id/dingweiimg" android:background="#ffffffff" android:padding="10.0dip" android:visibility="gone" android:layout_width="50.0dip" android:layout_height="50.0dip" android:layout_marginLeft="15.0dip" />
            <LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                    <LinearLayout android:gravity="center_vertical" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="13.0dip" android:layout_marginTop="10.0dip">
                        <ImageView android:id="@id/baohuimg" android:layout_width="20.0dip" android:layout_height="20.0dip" />
                        <TextView android:id="@id/baohutv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="未保护" />
                    </LinearLayout>
                    <LinearLayout android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="10.0dip">
                        <ImageView android:id="@id/bianjiimg" android:background="#ffffffff" android:padding="13.0dip" android:visibility="gone" android:layout_width="50.0dip" android:layout_height="50.0dip" android:layout_alignParentBottom="true" />
                        <LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                            <TextView android:textSize="13.0sp" android:textColor="#ffffffff" android:id="@id/show_address2" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="当前位置：" android:drawableLeft="@drawable/ic_position" android:drawablePadding="5.0dip" android:layout_marginVertical="15.0dip" />
                            <TextView android:textSize="14.0sp" android:id="@id/show_jingweidu2" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
                <TextView android:textSize="14.0sp" android:textColor="@color/black" android:gravity="center" android:id="@id/tiaozhuan" android:background="@drawable/sel_guide_btn7" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="  保存位置  " android:layout_marginHorizontal="30.0dip" android:layout_marginVertical="10.0dip" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
