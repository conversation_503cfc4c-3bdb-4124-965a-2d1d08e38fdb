<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:theme="@style/ThemeOverlay.AppCompat.Dark" android:orientation="vertical" android:background="@drawable/side_nav_bar" android:paddingLeft="@dimen/activity_horizontal_margin" android:paddingTop="@dimen/activity_vertical_margin" android:paddingRight="@dimen/activity_horizontal_margin" android:layout_width="fill_parent" android:layout_height="@dimen/nav_header_height"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/user_icon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="32.0dip" android:contentDescription="@null" />
    <TextView android:textAppearance="@style/TextAppearance.AppCompat.Body1" android:textSize="18.0sp" android:id="@id/user_name" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/nav_header_vertical_spacing" android:text="@string/nav_user_name" />
    <TextView android:gravity="center_vertical" android:layout_gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:text="@string/nav_user_limit_info" android:drawablePadding="5.0dip" app:drawableStartCompat="@drawable/ic_msg" />
</LinearLayout>
