<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="SharedValue" format="integer">
    </attr>
    <attr name="SharedValueId" format="reference">
    </attr>
    <attr name="actionBarDivider" format="reference">
    </attr>
    <attr name="actionBarItemBackground" format="reference">
    </attr>
    <attr name="actionBarPopupTheme" format="reference">
    </attr>
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference">
    </attr>
    <attr name="actionBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabStyle" format="reference">
    </attr>
    <attr name="actionBarTabTextStyle" format="reference">
    </attr>
    <attr name="actionBarTheme" format="reference">
    </attr>
    <attr name="actionBarWidgetTheme" format="reference">
    </attr>
    <attr name="actionButtonStyle" format="reference">
    </attr>
    <attr name="actionDropDownStyle" format="reference">
    </attr>
    <attr name="actionLayout" format="reference">
    </attr>
    <attr name="actionMenuTextAppearance" format="reference">
    </attr>
    <attr name="actionMenuTextColor" format="reference|color">
    </attr>
    <attr name="actionModeBackground" format="reference">
    </attr>
    <attr name="actionModeCloseButtonStyle" format="reference">
    </attr>
    <attr name="actionModeCloseContentDescription" format="string">
    </attr>
    <attr name="actionModeCloseDrawable" format="reference">
    </attr>
    <attr name="actionModeCopyDrawable" format="reference">
    </attr>
    <attr name="actionModeCutDrawable" format="reference">
    </attr>
    <attr name="actionModeFindDrawable" format="reference">
    </attr>
    <attr name="actionModePasteDrawable" format="reference">
    </attr>
    <attr name="actionModePopupWindowStyle" format="reference">
    </attr>
    <attr name="actionModeSelectAllDrawable" format="reference">
    </attr>
    <attr name="actionModeShareDrawable" format="reference">
    </attr>
    <attr name="actionModeSplitBackground" format="reference">
    </attr>
    <attr name="actionModeStyle" format="reference">
    </attr>
    <attr name="actionModeTheme" format="reference">
    </attr>
    <attr name="actionModeWebSearchDrawable" format="reference">
    </attr>
    <attr name="actionOverflowButtonStyle" format="reference">
    </attr>
    <attr name="actionOverflowMenuStyle" format="reference">
    </attr>
    <attr name="actionProviderClass" format="string">
    </attr>
    <attr name="actionTextColorAlpha" format="float">
    </attr>
    <attr name="actionViewClass" format="string">
    </attr>
    <attr name="activityAction" format="string">
    </attr>
    <attr name="activityChooserViewStyle" format="reference">
    </attr>
    <attr name="activityName" format="string">
    </attr>
    <attr name="adjustable" format="boolean">
    </attr>
    <attr name="alertDialogButtonGroupStyle" format="reference">
    </attr>
    <attr name="alertDialogCenterButtons" format="boolean">
    </attr>
    <attr name="alertDialogStyle" format="reference">
    </attr>
    <attr name="alertDialogTheme" format="reference">
    </attr>
    <attr name="allowDividerAbove" format="boolean">
    </attr>
    <attr name="allowDividerAfterLastItem" format="boolean">
    </attr>
    <attr name="allowDividerBelow" format="boolean">
    </attr>
    <attr name="allowStacking" format="boolean">
    </attr>
    <attr name="alpha" format="float">
    </attr>
    <attr name="alphabeticModifiers">
        <flag name="META" value="0x10000" />
        <flag name="CTRL" value="0x1000" />
        <flag name="ALT" value="0x2" />
        <flag name="SHIFT" value="0x1" />
        <flag name="SYM" value="0x4" />
        <flag name="FUNCTION" value="0x8" />
    </attr>
    <attr name="altSrc" format="reference">
    </attr>
    <attr name="alwaysExpand" format="boolean">
    </attr>
    <attr name="animateCircleAngleTo">
        <enum name="bestChoice" value="0" />
        <enum name="closest" value="1" />
        <enum name="clockwise" value="2" />
        <enum name="antiClockwise" value="3" />
        <enum name="constraint" value="4" />
    </attr>
    <attr name="animateRelativeTo" format="reference">
    </attr>
    <attr name="animationMode">
        <enum name="slide" value="0" />
        <enum name="fade" value="1" />
    </attr>
    <attr name="appBarLayoutStyle" format="reference">
    </attr>
    <attr name="applyMotionScene" format="boolean">
    </attr>
    <attr name="arcMode">
        <enum name="startVertical" value="0" />
        <enum name="startHorizontal" value="1" />
        <enum name="flip" value="2" />
    </attr>
    <attr name="arrowHeadLength" format="dimension">
    </attr>
    <attr name="arrowShaftLength" format="dimension">
    </attr>
    <attr name="attributeName" format="string">
    </attr>
    <attr name="autoCompleteMode">
        <enum name="continuousVelocity" value="0" />
        <enum name="spring" value="1" />
    </attr>
    <attr name="autoCompleteTextViewStyle" format="reference">
    </attr>
    <attr name="autoSizeMaxTextSize" format="dimension">
    </attr>
    <attr name="autoSizeMinTextSize" format="dimension">
    </attr>
    <attr name="autoSizePresetSizes" format="reference">
    </attr>
    <attr name="autoSizeStepGranularity" format="dimension">
    </attr>
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="autoTransition">
        <enum name="none" value="0" />
        <enum name="jumpToStart" value="1" />
        <enum name="jumpToEnd" value="2" />
        <enum name="animateToStart" value="3" />
        <enum name="animateToEnd" value="4" />
    </attr>
    <attr name="background" format="reference">
    </attr>
    <attr name="backgroundColor" format="color">
    </attr>
    <attr name="backgroundInsetBottom" format="dimension">
    </attr>
    <attr name="backgroundInsetEnd" format="dimension">
    </attr>
    <attr name="backgroundInsetStart" format="dimension">
    </attr>
    <attr name="backgroundInsetTop" format="dimension">
    </attr>
    <attr name="backgroundOverlayColorAlpha" format="float">
    </attr>
    <attr name="backgroundSplit" format="reference|color">
    </attr>
    <attr name="backgroundStacked" format="reference|color">
    </attr>
    <attr name="backgroundTint" format="color">
    </attr>
    <attr name="backgroundTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="badgeGravity">
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
    </attr>
    <attr name="badgeStyle" format="reference">
    </attr>
    <attr name="badgeTextColor" format="color">
    </attr>
    <attr name="barLength" format="dimension">
    </attr>
    <attr name="barrierAllowsGoneWidgets" format="boolean">
    </attr>
    <attr name="barrierDirection">
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="top" value="2" />
        <enum name="bottom" value="3" />
        <enum name="start" value="5" />
        <enum name="end" value="6" />
    </attr>
    <attr name="barrierMargin" format="dimension">
    </attr>
    <attr name="behavior_autoHide" format="boolean">
    </attr>
    <attr name="behavior_autoShrink" format="boolean">
    </attr>
    <attr name="behavior_draggable" format="boolean">
    </attr>
    <attr name="behavior_expandedOffset" format="reference|dimension">
    </attr>
    <attr name="behavior_fitToContents" format="boolean">
    </attr>
    <attr name="behavior_halfExpandedRatio" format="reference|float">
    </attr>
    <attr name="behavior_hideable" format="boolean">
    </attr>
    <attr name="behavior_overlapTop" format="dimension">
    </attr>
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_saveFlags">
        <flag name="peekHeight" value="0x1" />
        <flag name="fitToContents" value="0x2" />
        <flag name="hideable" value="0x4" />
        <flag name="skipCollapsed" value="0x8" />
        <flag name="all" value="-1" />
        <flag name="none" value="0" />
    </attr>
    <attr name="behavior_skipCollapsed" format="boolean">
    </attr>
    <attr name="blendSrc" format="reference">
    </attr>
    <attr name="borderRound" format="dimension">
    </attr>
    <attr name="borderRoundPercent" format="float">
    </attr>
    <attr name="borderWidth" format="dimension">
    </attr>
    <attr name="borderlessButtonStyle" format="reference">
    </attr>
    <attr name="bottomAppBarStyle" format="reference">
    </attr>
    <attr name="bottomNavigationStyle" format="reference">
    </attr>
    <attr name="bottomSheetDialogTheme" format="reference">
    </attr>
    <attr name="bottomSheetStyle" format="reference">
    </attr>
    <attr name="boxBackgroundColor" format="color">
    </attr>
    <attr name="boxBackgroundMode">
        <enum name="none" value="0" />
        <enum name="filled" value="1" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomStart" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopStart" format="dimension">
    </attr>
    <attr name="boxStrokeColor" format="color">
    </attr>
    <attr name="boxStrokeErrorColor" format="color">
    </attr>
    <attr name="boxStrokeWidth" format="dimension">
    </attr>
    <attr name="boxStrokeWidthFocused" format="dimension">
    </attr>
    <attr name="brightness" format="float">
    </attr>
    <attr name="buttonBarButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNegativeButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNeutralButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarPositiveButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarStyle" format="reference">
    </attr>
    <attr name="buttonCompat" format="reference">
    </attr>
    <attr name="buttonGravity">
        <flag name="center_vertical" value="0x10" />
        <flag name="top" value="0x30" />
        <flag name="bottom" value="0x50" />
    </attr>
    <attr name="buttonIconDimen" format="dimension">
    </attr>
    <attr name="buttonPanelSideLayout" format="reference">
    </attr>
    <attr name="buttonStyle" format="reference">
    </attr>
    <attr name="buttonStyleSmall" format="reference">
    </attr>
    <attr name="buttonTint" format="color">
    </attr>
    <attr name="buttonTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="cardBackgroundColor" format="color">
    </attr>
    <attr name="cardCornerRadius" format="dimension">
    </attr>
    <attr name="cardElevation" format="dimension">
    </attr>
    <attr name="cardForegroundColor" format="color">
    </attr>
    <attr name="cardMaxElevation" format="dimension">
    </attr>
    <attr name="cardPreventCornerOverlap" format="boolean">
    </attr>
    <attr name="cardUseCompatPadding" format="boolean">
    </attr>
    <attr name="cardViewStyle" format="reference">
    </attr>
    <attr name="carousel_backwardTransition" format="reference">
    </attr>
    <attr name="carousel_emptyViewsBehavior">
        <enum name="invisible" value="4" />
        <enum name="gone" value="8" />
    </attr>
    <attr name="carousel_firstView" format="reference">
    </attr>
    <attr name="carousel_forwardTransition" format="reference">
    </attr>
    <attr name="carousel_infinite" format="boolean">
    </attr>
    <attr name="carousel_nextState" format="reference">
    </attr>
    <attr name="carousel_previousState" format="reference">
    </attr>
    <attr name="carousel_touchUpMode">
        <enum name="immediateStop" value="1" />
        <enum name="carryVelocity" value="2" />
    </attr>
    <attr name="carousel_touchUp_dampeningFactor" format="float">
    </attr>
    <attr name="carousel_touchUp_velocityThreshold" format="float">
    </attr>
    <attr name="chainUseRtl" format="boolean">
    </attr>
    <attr name="checkBoxPreferenceStyle" format="reference">
    </attr>
    <attr name="checkMarkCompat" format="reference">
    </attr>
    <attr name="checkMarkTint" format="color">
    </attr>
    <attr name="checkMarkTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="checkboxStyle" format="reference">
    </attr>
    <attr name="checkedButton" format="reference">
    </attr>
    <attr name="checkedChip" format="reference">
    </attr>
    <attr name="checkedIcon" format="reference">
    </attr>
    <attr name="checkedIconEnabled" format="boolean">
    </attr>
    <attr name="checkedIconTint" format="color">
    </attr>
    <attr name="checkedIconVisible" format="boolean">
    </attr>
    <attr name="checkedTextViewStyle" format="reference">
    </attr>
    <attr name="chipBackgroundColor" format="color">
    </attr>
    <attr name="chipCornerRadius" format="dimension">
    </attr>
    <attr name="chipEndPadding" format="dimension">
    </attr>
    <attr name="chipGroupStyle" format="reference">
    </attr>
    <attr name="chipIcon" format="reference">
    </attr>
    <attr name="chipIconEnabled" format="boolean">
    </attr>
    <attr name="chipIconSize" format="dimension">
    </attr>
    <attr name="chipIconTint" format="color">
    </attr>
    <attr name="chipIconVisible" format="boolean">
    </attr>
    <attr name="chipMinHeight" format="dimension">
    </attr>
    <attr name="chipMinTouchTargetSize" format="dimension">
    </attr>
    <attr name="chipSpacing" format="dimension">
    </attr>
    <attr name="chipSpacingHorizontal" format="dimension">
    </attr>
    <attr name="chipSpacingVertical" format="dimension">
    </attr>
    <attr name="chipStandaloneStyle" format="reference">
    </attr>
    <attr name="chipStartPadding" format="dimension">
    </attr>
    <attr name="chipStrokeColor" format="color">
    </attr>
    <attr name="chipStrokeWidth" format="dimension">
    </attr>
    <attr name="chipStyle" format="reference">
    </attr>
    <attr name="chipSurfaceColor" format="color">
    </attr>
    <attr name="circleRadius" format="dimension">
    </attr>
    <attr name="circularflow_angles" format="string">
    </attr>
    <attr name="circularflow_defaultAngle" format="float">
    </attr>
    <attr name="circularflow_defaultRadius" format="dimension">
    </attr>
    <attr name="circularflow_radiusInDP" format="string">
    </attr>
    <attr name="circularflow_viewCenter" format="reference">
    </attr>
    <attr name="clearTop" format="boolean">
    </attr>
    <attr name="clearsTag" format="reference">
    </attr>
    <attr name="clickAction">
        <flag name="toggle" value="0x11" />
        <flag name="transitionToEnd" value="0x1" />
        <flag name="transitionToStart" value="0x10" />
        <flag name="jumpToEnd" value="0x100" />
        <flag name="jumpToStart" value="0x1000" />
    </attr>
    <attr name="closeIcon" format="reference">
    </attr>
    <attr name="closeIconEnabled" format="boolean">
    </attr>
    <attr name="closeIconEndPadding" format="dimension">
    </attr>
    <attr name="closeIconSize" format="dimension">
    </attr>
    <attr name="closeIconStartPadding" format="dimension">
    </attr>
    <attr name="closeIconTint" format="color">
    </attr>
    <attr name="closeIconVisible" format="boolean">
    </attr>
    <attr name="closeItemLayout" format="reference">
    </attr>
    <attr name="collapseContentDescription" format="string">
    </attr>
    <attr name="collapseIcon" format="reference">
    </attr>
    <attr name="collapsedTitleGravity">
        <flag name="top" value="0x30" />
        <flag name="bottom" value="0x50" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="center_vertical" value="0x10" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center" value="0x11" />
        <flag name="start" value="0x800003" />
        <flag name="end" value="0x800005" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference">
    </attr>
    <attr name="color" format="color">
    </attr>
    <attr name="colorAccent" format="color">
    </attr>
    <attr name="colorBackgroundFloating" format="color">
    </attr>
    <attr name="colorButtonNormal" format="color">
    </attr>
    <attr name="colorControlActivated" format="color">
    </attr>
    <attr name="colorControlHighlight" format="color">
    </attr>
    <attr name="colorControlNormal" format="color">
    </attr>
    <attr name="colorError" format="reference|color">
    </attr>
    <attr name="colorOnBackground" format="reference|string|integer|boolean|color|float|dimension|fraction">
    </attr>
    <attr name="colorOnError" format="color">
    </attr>
    <attr name="colorOnPrimary" format="color">
    </attr>
    <attr name="colorOnPrimarySurface" format="color">
    </attr>
    <attr name="colorOnSecondary" format="color">
    </attr>
    <attr name="colorOnSurface" format="color">
    </attr>
    <attr name="colorPrimary" format="color">
    </attr>
    <attr name="colorPrimaryDark" format="color">
    </attr>
    <attr name="colorPrimarySurface" format="color">
    </attr>
    <attr name="colorPrimaryVariant" format="color">
    </attr>
    <attr name="colorSecondary" format="color">
    </attr>
    <attr name="colorSecondaryVariant" format="color">
    </attr>
    <attr name="colorSurface" format="color">
    </attr>
    <attr name="colorSwitchThumbNormal" format="color">
    </attr>
    <attr name="commitIcon" format="reference">
    </attr>
    <attr name="constraintRotate">
        <enum name="none" value="0" />
        <enum name="right" value="1" />
        <enum name="left" value="2" />
        <enum name="x_right" value="3" />
        <enum name="x_left" value="4" />
    </attr>
    <attr name="constraintSet" format="reference">
    </attr>
    <attr name="constraintSetEnd" format="reference">
    </attr>
    <attr name="constraintSetStart" format="reference">
    </attr>
    <attr name="constraint_referenced_ids" format="string">
    </attr>
    <attr name="constraint_referenced_tags" format="string">
    </attr>
    <attr name="constraints" format="reference">
    </attr>
    <attr name="content" format="reference">
    </attr>
    <attr name="contentDescription" format="string">
    </attr>
    <attr name="contentInsetEnd" format="dimension">
    </attr>
    <attr name="contentInsetEndWithActions" format="dimension">
    </attr>
    <attr name="contentInsetLeft" format="dimension">
    </attr>
    <attr name="contentInsetRight" format="dimension">
    </attr>
    <attr name="contentInsetStart" format="dimension">
    </attr>
    <attr name="contentInsetStartWithNavigation" format="dimension">
    </attr>
    <attr name="contentPadding" format="dimension">
    </attr>
    <attr name="contentPaddingBottom" format="dimension">
    </attr>
    <attr name="contentPaddingLeft" format="dimension">
    </attr>
    <attr name="contentPaddingRight" format="dimension">
    </attr>
    <attr name="contentPaddingTop" format="dimension">
    </attr>
    <attr name="contentScrim" format="color">
    </attr>
    <attr name="contrast" format="float">
    </attr>
    <attr name="controlBackground" format="reference">
    </attr>
    <attr name="coordinatorLayoutStyle" format="reference">
    </attr>
    <attr name="cornerFamily">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyBottomLeft">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyBottomRight">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyTopLeft">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyTopRight">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerRadius" format="dimension">
    </attr>
    <attr name="cornerSize" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomRight" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopRight" format="dimension|fraction">
    </attr>
    <attr name="counterEnabled" format="boolean">
    </attr>
    <attr name="counterMaxLength" format="integer">
    </attr>
    <attr name="counterOverflowTextAppearance" format="reference">
    </attr>
    <attr name="counterOverflowTextColor" format="reference">
    </attr>
    <attr name="counterTextAppearance" format="reference">
    </attr>
    <attr name="counterTextColor" format="reference">
    </attr>
    <attr name="crossfade" format="float">
    </attr>
    <attr name="currentState" format="reference">
    </attr>
    <attr name="curveFit">
        <enum name="spline" value="0" />
        <enum name="linear" value="1" />
    </attr>
    <attr name="customBoolean" format="boolean">
    </attr>
    <attr name="customColorDrawableValue" format="color">
    </attr>
    <attr name="customColorValue" format="color">
    </attr>
    <attr name="customDimension" format="dimension">
    </attr>
    <attr name="customFloatValue" format="float">
    </attr>
    <attr name="customIntegerValue" format="integer">
    </attr>
    <attr name="customNavigationLayout" format="reference">
    </attr>
    <attr name="customPixelDimension" format="dimension">
    </attr>
    <attr name="customReference" format="reference">
    </attr>
    <attr name="customStringValue" format="string">
    </attr>
    <attr name="dayInvalidStyle" format="reference">
    </attr>
    <attr name="daySelectedStyle" format="reference">
    </attr>
    <attr name="dayStyle" format="reference">
    </attr>
    <attr name="dayTodayStyle" format="reference">
    </attr>
    <attr name="defaultDuration" format="integer">
    </attr>
    <attr name="defaultQueryHint" format="string">
    </attr>
    <attr name="defaultState" format="reference">
    </attr>
    <attr name="defaultValue" format="reference|string|integer|boolean|float">
    </attr>
    <attr name="deltaPolarAngle" format="float">
    </attr>
    <attr name="deltaPolarRadius" format="float">
    </attr>
    <attr name="dependency" format="string">
    </attr>
    <attr name="deriveConstraintsFrom" format="reference">
    </attr>
    <attr name="dialogCornerRadius" format="dimension">
    </attr>
    <attr name="dialogIcon" format="reference">
    </attr>
    <attr name="dialogLayout" format="reference">
    </attr>
    <attr name="dialogMessage" format="string">
    </attr>
    <attr name="dialogPreferenceStyle" format="reference">
    </attr>
    <attr name="dialogPreferredPadding" format="dimension">
    </attr>
    <attr name="dialogTheme" format="reference">
    </attr>
    <attr name="dialogTitle" format="string">
    </attr>
    <attr name="disableDependentsState" format="boolean">
    </attr>
    <attr name="displayOptions">
        <flag name="none" value="0" />
        <flag name="useLogo" value="0x1" />
        <flag name="showHome" value="0x2" />
        <flag name="homeAsUp" value="0x4" />
        <flag name="showTitle" value="0x8" />
        <flag name="showCustom" value="0x10" />
        <flag name="disableHome" value="0x20" />
    </attr>
    <attr name="divider" format="reference">
    </attr>
    <attr name="dividerHorizontal" format="reference">
    </attr>
    <attr name="dividerPadding" format="dimension">
    </attr>
    <attr name="dividerVertical" format="reference">
    </attr>
    <attr name="dragDirection">
        <enum name="dragUp" value="0" />
        <enum name="dragDown" value="1" />
        <enum name="dragLeft" value="2" />
        <enum name="dragRight" value="3" />
        <enum name="dragStart" value="4" />
        <enum name="dragEnd" value="5" />
        <enum name="dragClockwise" value="6" />
        <enum name="dragAnticlockwise" value="7" />
    </attr>
    <attr name="dragScale" format="float">
    </attr>
    <attr name="dragThreshold" format="float">
    </attr>
    <attr name="drawPath">
        <enum name="none" value="0" />
        <enum name="path" value="1" />
        <enum name="pathRelative" value="2" />
        <enum name="deltaRelative" value="3" />
        <enum name="asConfigured" value="4" />
        <enum name="rectangles" value="5" />
    </attr>
    <attr name="drawableBottomCompat" format="reference">
    </attr>
    <attr name="drawableEndCompat" format="reference">
    </attr>
    <attr name="drawableLeftCompat" format="reference">
    </attr>
    <attr name="drawableRightCompat" format="reference">
    </attr>
    <attr name="drawableSize" format="dimension">
    </attr>
    <attr name="drawableStartCompat" format="reference">
    </attr>
    <attr name="drawableTint" format="color">
    </attr>
    <attr name="drawableTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="drawableTopCompat" format="reference">
    </attr>
    <attr name="drawerArrowStyle" format="reference">
    </attr>
    <attr name="dropDownListViewStyle" format="reference">
    </attr>
    <attr name="dropdownListPreferredItemHeight" format="dimension">
    </attr>
    <attr name="dropdownPreferenceStyle" format="reference">
    </attr>
    <attr name="duration" format="integer">
    </attr>
    <attr name="editTextBackground" format="reference">
    </attr>
    <attr name="editTextColor" format="reference|color">
    </attr>
    <attr name="editTextPreferenceStyle" format="reference">
    </attr>
    <attr name="editTextStyle" format="reference">
    </attr>
    <attr name="elevation" format="dimension">
    </attr>
    <attr name="elevationOverlayColor" format="color">
    </attr>
    <attr name="elevationOverlayEnabled" format="boolean">
    </attr>
    <attr name="emojiCompatEnabled" format="boolean">
    </attr>
    <attr name="enableCopying" format="boolean">
    </attr>
    <attr name="enabled" format="boolean">
    </attr>
    <attr name="endIconCheckable" format="boolean">
    </attr>
    <attr name="endIconContentDescription" format="string">
    </attr>
    <attr name="endIconDrawable" format="reference">
    </attr>
    <attr name="endIconMode">
        <enum name="custom" value="-1" />
        <enum name="none" value="0" />
        <enum name="password_toggle" value="1" />
        <enum name="clear_text" value="2" />
        <enum name="dropdown_menu" value="3" />
    </attr>
    <attr name="endIconTint" format="color">
    </attr>
    <attr name="endIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="enforceMaterialTheme" format="boolean">
    </attr>
    <attr name="enforceTextAppearance" format="boolean">
    </attr>
    <attr name="ensureMinTouchTargetSize" format="boolean">
    </attr>
    <attr name="entries" format="reference">
    </attr>
    <attr name="entryValues" format="reference">
    </attr>
    <attr name="errorContentDescription" format="string">
    </attr>
    <attr name="errorEnabled" format="boolean">
    </attr>
    <attr name="errorIconDrawable" format="reference">
    </attr>
    <attr name="errorIconTint" format="reference">
    </attr>
    <attr name="errorIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="errorTextAppearance" format="reference">
    </attr>
    <attr name="errorTextColor" format="color">
    </attr>
    <attr name="expandActivityOverflowButtonDrawable" format="reference">
    </attr>
    <attr name="expanded" format="boolean">
    </attr>
    <attr name="expandedTitleGravity">
        <flag name="top" value="0x30" />
        <flag name="bottom" value="0x50" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="center_vertical" value="0x10" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="center" value="0x11" />
        <flag name="start" value="0x800003" />
        <flag name="end" value="0x800005" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension">
    </attr>
    <attr name="expandedTitleMarginBottom" format="dimension">
    </attr>
    <attr name="expandedTitleMarginEnd" format="dimension">
    </attr>
    <attr name="expandedTitleMarginStart" format="dimension">
    </attr>
    <attr name="expandedTitleMarginTop" format="dimension">
    </attr>
    <attr name="expandedTitleTextAppearance" format="reference">
    </attr>
    <attr name="extendMotionSpec" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonStyle" format="reference">
    </attr>
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabAnimationMode">
        <enum name="scale" value="0" />
        <enum name="slide" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension">
    </attr>
    <attr name="fabCradleRoundedCornerRadius" format="dimension">
    </attr>
    <attr name="fabCradleVerticalOffset" format="dimension">
    </attr>
    <attr name="fabCustomSize" format="dimension">
    </attr>
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="normal" value="0" />
        <enum name="mini" value="1" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean">
    </attr>
    <attr name="fastScrollHorizontalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollHorizontalTrackDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalTrackDrawable" format="reference">
    </attr>
    <attr name="finishPrimaryWithSecondary" format="boolean">
    </attr>
    <attr name="finishSecondaryWithPrimary" format="boolean">
    </attr>
    <attr name="firstBaselineToTopHeight" format="dimension">
    </attr>
    <attr name="floatingActionButtonStyle" format="reference">
    </attr>
    <attr name="flow_firstHorizontalBias" format="float">
    </attr>
    <attr name="flow_firstHorizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_firstVerticalBias" format="float">
    </attr>
    <attr name="flow_firstVerticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_horizontalAlign">
        <enum name="start" value="0" />
        <enum name="end" value="1" />
        <enum name="center" value="2" />
    </attr>
    <attr name="flow_horizontalBias" format="float">
    </attr>
    <attr name="flow_horizontalGap" format="dimension">
    </attr>
    <attr name="flow_horizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_lastHorizontalBias" format="float">
    </attr>
    <attr name="flow_lastHorizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_lastVerticalBias" format="float">
    </attr>
    <attr name="flow_lastVerticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_maxElementsWrap" format="integer">
    </attr>
    <attr name="flow_padding" format="dimension">
    </attr>
    <attr name="flow_verticalAlign">
        <enum name="top" value="0" />
        <enum name="bottom" value="1" />
        <enum name="center" value="2" />
        <enum name="baseline" value="3" />
    </attr>
    <attr name="flow_verticalBias" format="float">
    </attr>
    <attr name="flow_verticalGap" format="dimension">
    </attr>
    <attr name="flow_verticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_wrapMode">
        <enum name="none" value="0" />
        <enum name="chain" value="1" />
        <enum name="aligned" value="2" />
        <enum name="chain2" value="3" />
    </attr>
    <attr name="font" format="reference">
    </attr>
    <attr name="fontFamily" format="string">
    </attr>
    <attr name="fontProviderAuthority" format="string">
    </attr>
    <attr name="fontProviderCerts" format="reference">
    </attr>
    <attr name="fontProviderFetchStrategy">
        <enum name="blocking" value="0" />
        <enum name="async" value="1" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string">
    </attr>
    <attr name="fontProviderQuery" format="string">
    </attr>
    <attr name="fontProviderSystemFontFamily" format="string">
    </attr>
    <attr name="fontStyle">
        <enum name="normal" value="0" />
        <enum name="italic" value="1" />
    </attr>
    <attr name="fontVariationSettings" format="string">
    </attr>
    <attr name="fontWeight" format="integer">
    </attr>
    <attr name="foregroundInsidePadding" format="boolean">
    </attr>
    <attr name="fragment" format="string">
    </attr>
    <attr name="framePosition" format="integer">
    </attr>
    <attr name="gapBetweenBars" format="dimension">
    </attr>
    <attr name="gestureInsetBottomIgnored" format="boolean">
    </attr>
    <attr name="goIcon" format="reference">
    </attr>
    <attr name="guidelineUseRtl" format="boolean">
    </attr>
    <attr name="haloColor" format="color">
    </attr>
    <attr name="haloRadius" format="dimension">
    </attr>
    <attr name="headerLayout" format="reference">
    </attr>
    <attr name="height" format="dimension">
    </attr>
    <attr name="helperText" format="string">
    </attr>
    <attr name="helperTextEnabled" format="boolean">
    </attr>
    <attr name="helperTextTextAppearance" format="reference">
    </attr>
    <attr name="helperTextTextColor" format="color">
    </attr>
    <attr name="hideMotionSpec" format="reference">
    </attr>
    <attr name="hideOnContentScroll" format="boolean">
    </attr>
    <attr name="hideOnScroll" format="boolean">
    </attr>
    <attr name="hintAnimationEnabled" format="boolean">
    </attr>
    <attr name="hintEnabled" format="boolean">
    </attr>
    <attr name="hintTextAppearance" format="reference">
    </attr>
    <attr name="hintTextColor" format="color">
    </attr>
    <attr name="homeAsUpIndicator" format="reference">
    </attr>
    <attr name="homeLayout" format="reference">
    </attr>
    <attr name="horizontalOffset" format="dimension">
    </attr>
    <attr name="hoveredFocusedTranslationZ" format="dimension">
    </attr>
    <attr name="icon" format="reference">
    </attr>
    <attr name="iconEndPadding" format="dimension">
    </attr>
    <attr name="iconGravity">
        <flag name="start" value="0x1" />
        <flag name="textStart" value="0x2" />
        <flag name="end" value="0x3" />
        <flag name="textEnd" value="0x4" />
    </attr>
    <attr name="iconPadding" format="dimension">
    </attr>
    <attr name="iconSize" format="dimension">
    </attr>
    <attr name="iconSpaceReserved" format="boolean">
    </attr>
    <attr name="iconStartPadding" format="dimension">
    </attr>
    <attr name="iconTint" format="color">
    </attr>
    <attr name="iconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="iconifiedByDefault" format="boolean">
    </attr>
    <attr name="ifTagNotSet" format="reference">
    </attr>
    <attr name="ifTagSet" format="reference">
    </attr>
    <attr name="imageButtonStyle" format="reference">
    </attr>
    <attr name="imagePanX" format="float">
    </attr>
    <attr name="imagePanY" format="float">
    </attr>
    <attr name="imageRotate" format="float">
    </attr>
    <attr name="imageZoom" format="float">
    </attr>
    <attr name="indeterminateProgressStyle" format="reference">
    </attr>
    <attr name="initialActivityCount" format="string">
    </attr>
    <attr name="initialExpandedChildrenCount" format="integer">
    </attr>
    <attr name="insetForeground" format="reference|color">
    </attr>
    <attr name="isLightTheme" format="boolean">
    </attr>
    <attr name="isMaterialTheme" format="boolean">
    </attr>
    <attr name="isPreferenceVisible" format="boolean">
    </attr>
    <attr name="itemBackground" format="reference">
    </attr>
    <attr name="itemFillColor" format="color">
    </attr>
    <attr name="itemHorizontalPadding" format="dimension">
    </attr>
    <attr name="itemHorizontalTranslationEnabled" format="boolean">
    </attr>
    <attr name="itemIconPadding" format="dimension">
    </attr>
    <attr name="itemIconSize" format="dimension">
    </attr>
    <attr name="itemIconTint" format="color">
    </attr>
    <attr name="itemMaxLines" format="integer" min="1">
    </attr>
    <attr name="itemPadding" format="dimension">
    </attr>
    <attr name="itemRippleColor" format="color">
    </attr>
    <attr name="itemShapeAppearance" format="reference">
    </attr>
    <attr name="itemShapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="itemShapeFillColor" format="color">
    </attr>
    <attr name="itemShapeInsetBottom" format="dimension">
    </attr>
    <attr name="itemShapeInsetEnd" format="dimension">
    </attr>
    <attr name="itemShapeInsetStart" format="dimension">
    </attr>
    <attr name="itemShapeInsetTop" format="dimension">
    </attr>
    <attr name="itemSpacing" format="dimension">
    </attr>
    <attr name="itemStrokeColor" format="color">
    </attr>
    <attr name="itemStrokeWidth" format="dimension">
    </attr>
    <attr name="itemTextAppearance" format="reference">
    </attr>
    <attr name="itemTextAppearanceActive" format="reference">
    </attr>
    <attr name="itemTextAppearanceInactive" format="reference">
    </attr>
    <attr name="itemTextColor" format="color">
    </attr>
    <attr name="key" format="string">
    </attr>
    <attr name="keyPositionType">
        <enum name="deltaRelative" value="0" />
        <enum name="pathRelative" value="1" />
        <enum name="parentRelative" value="2" />
    </attr>
    <attr name="keylines" format="reference">
    </attr>
    <attr name="lStar" format="float">
    </attr>
    <attr name="labelBehavior">
        <enum name="floating" value="0" />
        <enum name="withinBounds" value="1" />
        <enum name="gone" value="2" />
    </attr>
    <attr name="labelStyle" format="reference">
    </attr>
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="selected" value="0" />
        <enum name="labeled" value="1" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="lastBaselineToBottomHeight" format="dimension">
    </attr>
    <attr name="layout" format="reference">
    </attr>
    <attr name="layoutDescription" format="reference">
    </attr>
    <attr name="layoutDuringTransition">
        <enum name="ignoreRequest" value="0" />
        <enum name="honorRequest" value="1" />
        <enum name="callMeasure" value="2" />
    </attr>
    <attr name="layoutManager" format="string">
    </attr>
    <attr name="layout_anchor" format="reference">
    </attr>
    <attr name="layout_anchorGravity">
        <flag name="top" value="0x30" />
        <flag name="bottom" value="0x50" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="center_vertical" value="0x10" />
        <flag name="fill_vertical" value="0x70" />
        <flag name="center_horizontal" value="0x1" />
        <flag name="fill_horizontal" value="0x7" />
        <flag name="center" value="0x11" />
        <flag name="fill" value="0x77" />
        <flag name="clip_vertical" value="0x80" />
        <flag name="clip_horizontal" value="0x8" />
        <flag name="start" value="0x800003" />
        <flag name="end" value="0x800005" />
    </attr>
    <attr name="layout_behavior" format="string">
    </attr>
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="pin" value="1" />
        <enum name="parallax" value="2" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float">
    </attr>
    <attr name="layout_constrainedHeight" format="boolean">
    </attr>
    <attr name="layout_constrainedWidth" format="boolean">
    </attr>
    <attr name="layout_constraintBaseline_creator" format="integer">
    </attr>
    <attr name="layout_constraintBaseline_toBaselineOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_creator" format="integer">
    </attr>
    <attr name="layout_constraintBottom_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintCircle" format="reference">
    </attr>
    <attr name="layout_constraintCircleAngle" format="float">
    </attr>
    <attr name="layout_constraintCircleRadius" format="dimension">
    </attr>
    <attr name="layout_constraintDimensionRatio" format="string">
    </attr>
    <attr name="layout_constraintEnd_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintEnd_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintGuide_begin" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_end" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_percent" format="float">
    </attr>
    <attr name="layout_constraintHeight" format="string|dimension">
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="match_constraint" value="-3" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintHeight_default">
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
        <enum name="percent" value="2" />
    </attr>
    <attr name="layout_constraintHeight_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_percent" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_bias" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_chainStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="layout_constraintHorizontal_weight" format="float">
    </attr>
    <attr name="layout_constraintLeft_creator" format="integer">
    </attr>
    <attr name="layout_constraintLeft_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintLeft_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_creator" format="integer">
    </attr>
    <attr name="layout_constraintRight_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTag" format="string">
    </attr>
    <attr name="layout_constraintTop_creator" format="integer">
    </attr>
    <attr name="layout_constraintTop_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintVertical_bias" format="float">
    </attr>
    <attr name="layout_constraintVertical_chainStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="layout_constraintVertical_weight" format="float">
    </attr>
    <attr name="layout_constraintWidth" format="string|dimension">
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="match_constraint" value="-3" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintWidth_default">
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
        <enum name="percent" value="2" />
    </attr>
    <attr name="layout_constraintWidth_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_percent" format="float">
    </attr>
    <attr name="layout_dodgeInsetEdges">
        <flag name="none" value="0x0" />
        <flag name="top" value="0x30" />
        <flag name="bottom" value="0x50" />
        <flag name="left" value="0x3" />
        <flag name="right" value="0x5" />
        <flag name="start" value="0x800003" />
        <flag name="end" value="0x800005" />
        <flag name="all" value="0x77" />
    </attr>
    <attr name="layout_editor_absoluteX" format="dimension">
    </attr>
    <attr name="layout_editor_absoluteY" format="dimension">
    </attr>
    <attr name="layout_goneMarginBaseline" format="dimension">
    </attr>
    <attr name="layout_goneMarginBottom" format="dimension">
    </attr>
    <attr name="layout_goneMarginEnd" format="dimension">
    </attr>
    <attr name="layout_goneMarginLeft" format="dimension">
    </attr>
    <attr name="layout_goneMarginRight" format="dimension">
    </attr>
    <attr name="layout_goneMarginStart" format="dimension">
    </attr>
    <attr name="layout_goneMarginTop" format="dimension">
    </attr>
    <attr name="layout_insetEdge">
        <enum name="none" value="0x0" />
        <enum name="top" value="0x30" />
        <enum name="bottom" value="0x50" />
        <enum name="left" value="0x3" />
        <enum name="right" value="0x5" />
        <enum name="start" value="0x800003" />
        <enum name="end" value="0x800005" />
    </attr>
    <attr name="layout_keyline" format="integer">
    </attr>
    <attr name="layout_marginBaseline" format="dimension">
    </attr>
    <attr name="layout_optimizationLevel">
        <flag name="none" value="0" />
        <flag name="legacy" value="0" />
        <flag name="standard" value="257" />
        <flag name="direct" value="1" />
        <flag name="barrier" value="2" />
        <flag name="chains" value="4" />
        <flag name="dimensions" value="8" />
        <flag name="ratio" value="16" />
        <flag name="groups" value="32" />
        <flag name="graph" value="64" />
        <flag name="graph_wrap" value="128" />
        <flag name="cache_measures" value="256" />
        <flag name="dependency_ordering" value="512" />
        <flag name="grouping" value="1024" />
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="noScroll" value="0x0" />
        <flag name="scroll" value="0x1" />
        <flag name="exitUntilCollapsed" value="0x2" />
        <flag name="enterAlways" value="0x4" />
        <flag name="enterAlwaysCollapsed" value="0x8" />
        <flag name="snap" value="0x10" />
        <flag name="snapMargins" value="0x20" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference">
    </attr>
    <attr name="layout_wrapBehaviorInParent">
        <enum name="included" value="0" />
        <enum name="horizontal_only" value="1" />
        <enum name="vertical_only" value="2" />
        <enum name="skipped" value="3" />
    </attr>
    <attr name="liftOnScroll" format="boolean">
    </attr>
    <attr name="liftOnScrollTargetViewId" format="reference">
    </attr>
    <attr name="limitBoundsTo" format="reference">
    </attr>
    <attr name="lineHeight" format="dimension">
    </attr>
    <attr name="lineSpacing" format="dimension">
    </attr>
    <attr name="listChoiceBackgroundIndicator" format="reference">
    </attr>
    <attr name="listChoiceIndicatorMultipleAnimated" format="reference">
    </attr>
    <attr name="listChoiceIndicatorSingleAnimated" format="reference">
    </attr>
    <attr name="listDividerAlertDialog" format="reference">
    </attr>
    <attr name="listItemLayout" format="reference">
    </attr>
    <attr name="listLayout" format="reference">
    </attr>
    <attr name="listMenuViewStyle" format="reference">
    </attr>
    <attr name="listPopupWindowStyle" format="reference">
    </attr>
    <attr name="listPreferredItemHeight" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightLarge" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightSmall" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingEnd" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingLeft" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingRight" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingStart" format="dimension">
    </attr>
    <attr name="logo" format="reference">
    </attr>
    <attr name="logoDescription" format="string">
    </attr>
    <attr name="materialAlertDialogBodyTextStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTheme" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleIconStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitlePanelStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleTextStyle" format="reference">
    </attr>
    <attr name="materialButtonOutlinedStyle" format="reference">
    </attr>
    <attr name="materialButtonStyle" format="reference">
    </attr>
    <attr name="materialButtonToggleGroupStyle" format="reference">
    </attr>
    <attr name="materialCalendarDay" format="reference">
    </attr>
    <attr name="materialCalendarFullscreenTheme" format="reference">
    </attr>
    <attr name="materialCalendarHeaderConfirmButton" format="reference">
    </attr>
    <attr name="materialCalendarHeaderDivider" format="reference">
    </attr>
    <attr name="materialCalendarHeaderLayout" format="reference">
    </attr>
    <attr name="materialCalendarHeaderSelection" format="reference">
    </attr>
    <attr name="materialCalendarHeaderTitle" format="reference">
    </attr>
    <attr name="materialCalendarHeaderToggleButton" format="reference">
    </attr>
    <attr name="materialCalendarStyle" format="reference">
    </attr>
    <attr name="materialCalendarTheme" format="reference">
    </attr>
    <attr name="materialCardViewStyle" format="reference">
    </attr>
    <attr name="materialThemeOverlay" format="reference">
    </attr>
    <attr name="maxAcceleration" format="float">
    </attr>
    <attr name="maxActionInlineWidth" format="dimension">
    </attr>
    <attr name="maxButtonHeight" format="dimension">
    </attr>
    <attr name="maxCharacterCount" format="integer">
    </attr>
    <attr name="maxHeight" format="dimension">
    </attr>
    <attr name="maxImageSize" format="dimension">
    </attr>
    <attr name="maxLines" format="integer">
    </attr>
    <attr name="maxVelocity" format="float">
    </attr>
    <attr name="maxWidth" format="dimension">
    </attr>
    <attr name="measureWithLargestChild" format="boolean">
    </attr>
    <attr name="menu" format="reference">
    </attr>
    <attr name="methodName" format="string">
    </attr>
    <attr name="min" format="integer">
    </attr>
    <attr name="minHeight" format="dimension">
    </attr>
    <attr name="minTouchTargetSize" format="dimension">
    </attr>
    <attr name="minWidth" format="dimension">
    </attr>
    <attr name="mock_diagonalsColor" format="color">
    </attr>
    <attr name="mock_label" format="string">
    </attr>
    <attr name="mock_labelBackgroundColor" format="color">
    </attr>
    <attr name="mock_labelColor" format="color">
    </attr>
    <attr name="mock_showDiagonals" format="boolean">
    </attr>
    <attr name="mock_showLabel" format="boolean">
    </attr>
    <attr name="motionDebug">
        <enum name="NO_DEBUG" value="0" />
        <enum name="SHOW_PROGRESS" value="1" />
        <enum name="SHOW_PATH" value="2" />
        <enum name="SHOW_ALL" value="3" />
    </attr>
    <attr name="motionEffect_alpha" format="float">
    </attr>
    <attr name="motionEffect_end" format="integer">
    </attr>
    <attr name="motionEffect_move">
        <enum name="auto" value="-1" />
        <enum name="north" value="0" />
        <enum name="south" value="1" />
        <enum name="east" value="2" />
        <enum name="west" value="3" />
    </attr>
    <attr name="motionEffect_start" format="integer">
    </attr>
    <attr name="motionEffect_strict" format="boolean">
    </attr>
    <attr name="motionEffect_translationX" format="dimension">
    </attr>
    <attr name="motionEffect_translationY" format="dimension">
    </attr>
    <attr name="motionEffect_viewTransition" format="reference">
    </attr>
    <attr name="motionInterpolator" format="reference|string">
        <enum name="easeInOut" value="0" />
        <enum name="easeIn" value="1" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="bounce" value="4" />
        <enum name="overshoot" value="5" />
        <enum name="anticipate" value="6" />
    </attr>
    <attr name="motionPathRotate" format="float">
    </attr>
    <attr name="motionProgress" format="float">
    </attr>
    <attr name="motionStagger" format="float">
    </attr>
    <attr name="motionTarget" format="reference|string">
    </attr>
    <attr name="motion_postLayoutCollision" format="boolean">
    </attr>
    <attr name="motion_triggerOnCollision" format="reference">
    </attr>
    <attr name="moveWhenScrollAtTop" format="boolean">
    </attr>
    <attr name="mrl_rippleAlpha" format="float">
    </attr>
    <attr name="mrl_rippleBackground" format="color">
    </attr>
    <attr name="mrl_rippleColor" format="color">
    </attr>
    <attr name="mrl_rippleDelayClick" format="boolean">
    </attr>
    <attr name="mrl_rippleDimension" format="dimension">
    </attr>
    <attr name="mrl_rippleDuration" format="integer">
    </attr>
    <attr name="mrl_rippleFadeDuration" format="integer">
    </attr>
    <attr name="mrl_rippleHover" format="boolean">
    </attr>
    <attr name="mrl_rippleInAdapter" format="boolean">
    </attr>
    <attr name="mrl_rippleOverlay" format="boolean">
    </attr>
    <attr name="mrl_ripplePersistent" format="boolean">
    </attr>
    <attr name="mrl_rippleRoundedCorners" format="dimension">
    </attr>
    <attr name="multiChoiceItemLayout" format="reference">
    </attr>
    <attr name="navigationContentDescription" format="string">
    </attr>
    <attr name="navigationIcon" format="reference">
    </attr>
    <attr name="navigationMode">
        <enum name="normal" value="0" />
        <enum name="listMode" value="1" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationViewStyle" format="reference">
    </attr>
    <attr name="negativeButtonText" format="string">
    </attr>
    <attr name="nestedScrollFlags">
        <flag name="none" value="0" />
        <flag name="disablePostScroll" value="1" />
        <flag name="disableScroll" value="2" />
        <flag name="supportScrollUp" value="4" />
    </attr>
    <attr name="nestedScrollViewStyle" format="reference">
    </attr>
    <attr name="number" format="integer">
    </attr>
    <attr name="numericModifiers">
        <flag name="META" value="0x10000" />
        <flag name="CTRL" value="0x1000" />
        <flag name="ALT" value="0x2" />
        <flag name="SHIFT" value="0x1" />
        <flag name="SYM" value="0x4" />
        <flag name="FUNCTION" value="0x8" />
    </attr>
    <attr name="onCross" format="string">
    </attr>
    <attr name="onHide" format="boolean">
    </attr>
    <attr name="onNegativeCross" format="string">
    </attr>
    <attr name="onPositiveCross" format="string">
    </attr>
    <attr name="onShow" format="boolean">
    </attr>
    <attr name="onStateTransition">
        <enum name="actionDown" value="1" />
        <enum name="actionUp" value="2" />
        <enum name="actionDownUp" value="3" />
        <enum name="sharedValueSet" value="4" />
        <enum name="sharedValueUnset" value="5" />
    </attr>
    <attr name="onTouchUp">
        <enum name="autoComplete" value="0" />
        <enum name="autoCompleteToStart" value="1" />
        <enum name="autoCompleteToEnd" value="2" />
        <enum name="stop" value="3" />
        <enum name="decelerate" value="4" />
        <enum name="decelerateAndComplete" value="5" />
        <enum name="neverCompleteToStart" value="6" />
        <enum name="neverCompleteToEnd" value="7" />
    </attr>
    <attr name="order" format="integer">
    </attr>
    <attr name="orderingFromXml" format="boolean">
    </attr>
    <attr name="overlapAnchor" format="boolean">
    </attr>
    <attr name="overlay" format="boolean">
    </attr>
    <attr name="paddingBottomNoButtons" format="dimension">
    </attr>
    <attr name="paddingBottomSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingEnd" format="dimension">
    </attr>
    <attr name="paddingLeftSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingRightSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingStart" format="dimension">
    </attr>
    <attr name="paddingTopNoTitle" format="dimension">
    </attr>
    <attr name="panelBackground" format="reference">
    </attr>
    <attr name="panelMenuListTheme" format="reference">
    </attr>
    <attr name="panelMenuListWidth" format="dimension">
    </attr>
    <attr name="passwordToggleContentDescription" format="string">
    </attr>
    <attr name="passwordToggleDrawable" format="reference">
    </attr>
    <attr name="passwordToggleEnabled" format="boolean">
    </attr>
    <attr name="passwordToggleTint" format="color">
    </attr>
    <attr name="passwordToggleTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="pathMotionArc">
        <enum name="none" value="0" />
        <enum name="startVertical" value="1" />
        <enum name="startHorizontal" value="2" />
        <enum name="flip" value="3" />
    </attr>
    <attr name="path_percent" format="float">
    </attr>
    <attr name="percentHeight" format="float">
    </attr>
    <attr name="percentWidth" format="float">
    </attr>
    <attr name="percentX" format="float">
    </attr>
    <attr name="percentY" format="float">
    </attr>
    <attr name="perpendicularPath_percent" format="float">
    </attr>
    <attr name="persistent" format="boolean">
    </attr>
    <attr name="pivotAnchor" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="placeholderActivityName" format="string">
    </attr>
    <attr name="placeholderText" format="string">
    </attr>
    <attr name="placeholderTextAppearance" format="reference">
    </attr>
    <attr name="placeholderTextColor" format="color">
    </attr>
    <attr name="placeholder_emptyVisibility">
        <enum name="visible" value="0" />
        <enum name="invisible" value="4" />
        <enum name="gone" value="8" />
    </attr>
    <attr name="polarRelativeTo" format="reference">
    </attr>
    <attr name="popupMenuBackground" format="reference">
    </attr>
    <attr name="popupMenuStyle" format="reference">
    </attr>
    <attr name="popupTheme" format="reference">
    </attr>
    <attr name="popupWindowStyle" format="reference">
    </attr>
    <attr name="positiveButtonText" format="string">
    </attr>
    <attr name="preferenceCategoryStyle" format="reference">
    </attr>
    <attr name="preferenceCategoryTitleTextAppearance" format="reference">
    </attr>
    <attr name="preferenceCategoryTitleTextColor" format="reference|color">
    </attr>
    <attr name="preferenceFragmentCompatStyle" format="reference">
    </attr>
    <attr name="preferenceFragmentListStyle" format="reference">
    </attr>
    <attr name="preferenceFragmentStyle" format="reference">
    </attr>
    <attr name="preferenceInformationStyle" format="reference">
    </attr>
    <attr name="preferenceScreenStyle" format="reference">
    </attr>
    <attr name="preferenceStyle" format="reference">
    </attr>
    <attr name="preferenceTheme" format="reference">
    </attr>
    <attr name="prefixText" format="string">
    </attr>
    <attr name="prefixTextAppearance" format="reference">
    </attr>
    <attr name="prefixTextColor" format="color">
    </attr>
    <attr name="preserveIconSpacing" format="boolean">
    </attr>
    <attr name="pressedTranslationZ" format="dimension">
    </attr>
    <attr name="primaryActivityName" format="string">
    </attr>
    <attr name="progressBarPadding" format="dimension">
    </attr>
    <attr name="progressBarStyle" format="reference">
    </attr>
    <attr name="quantizeMotionInterpolator" format="reference|string">
        <enum name="easeInOut" value="0" />
        <enum name="easeIn" value="1" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="bounce" value="4" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="quantizeMotionPhase" format="float">
    </attr>
    <attr name="quantizeMotionSteps" format="integer">
    </attr>
    <attr name="queryBackground" format="reference">
    </attr>
    <attr name="queryHint" format="string">
    </attr>
    <attr name="queryPatterns" format="reference">
    </attr>
    <attr name="radioButtonStyle" format="reference">
    </attr>
    <attr name="rangeFillColor" format="color">
    </attr>
    <attr name="ratingBarStyle" format="reference">
    </attr>
    <attr name="ratingBarStyleIndicator" format="reference">
    </attr>
    <attr name="ratingBarStyleSmall" format="reference">
    </attr>
    <attr name="reactiveGuide_animateChange" format="boolean">
    </attr>
    <attr name="reactiveGuide_applyToAllConstraintSets" format="boolean">
    </attr>
    <attr name="reactiveGuide_applyToConstraintSet" format="reference">
    </attr>
    <attr name="reactiveGuide_valueId" format="reference">
    </attr>
    <attr name="recyclerViewStyle" format="reference">
    </attr>
    <attr name="region_heightLessThan" format="dimension">
    </attr>
    <attr name="region_heightMoreThan" format="dimension">
    </attr>
    <attr name="region_widthLessThan" format="dimension">
    </attr>
    <attr name="region_widthMoreThan" format="dimension">
    </attr>
    <attr name="reverseLayout" format="boolean">
    </attr>
    <attr name="rippleColor" format="color">
    </attr>
    <attr name="rotationCenterId" format="reference">
    </attr>
    <attr name="round" format="dimension">
    </attr>
    <attr name="roundPercent" format="float">
    </attr>
    <attr name="saturation" format="float">
    </attr>
    <attr name="sb_background" format="reference|color">
    </attr>
    <attr name="sb_border_width" format="reference|dimension">
    </attr>
    <attr name="sb_button_color" format="reference|color">
    </attr>
    <attr name="sb_checked" format="reference|boolean">
    </attr>
    <attr name="sb_checked_color" format="reference|color">
    </attr>
    <attr name="sb_checkline_color" format="reference|color">
    </attr>
    <attr name="sb_checkline_width" format="reference|dimension">
    </attr>
    <attr name="sb_effect_duration" format="reference|integer">
    </attr>
    <attr name="sb_enable_effect" format="reference|boolean">
    </attr>
    <attr name="sb_shadow_color" format="reference|color">
    </attr>
    <attr name="sb_shadow_effect" format="reference|boolean">
    </attr>
    <attr name="sb_shadow_offset" format="reference|dimension">
    </attr>
    <attr name="sb_shadow_radius" format="reference|dimension">
    </attr>
    <attr name="sb_show_indicator" format="reference|boolean">
    </attr>
    <attr name="sb_uncheck_color" format="reference|color">
    </attr>
    <attr name="sb_uncheckcircle_color" format="reference|color">
    </attr>
    <attr name="sb_uncheckcircle_radius" format="reference|dimension">
    </attr>
    <attr name="sb_uncheckcircle_width" format="reference|dimension">
    </attr>
    <attr name="scaleFromTextSize" format="dimension">
    </attr>
    <attr name="scrimAnimationDuration" format="integer">
    </attr>
    <attr name="scrimBackground" format="reference|color">
    </attr>
    <attr name="scrimVisibleHeightTrigger" format="dimension">
    </attr>
    <attr name="searchHintIcon" format="reference">
    </attr>
    <attr name="searchIcon" format="reference">
    </attr>
    <attr name="searchViewStyle" format="reference">
    </attr>
    <attr name="secondaryActivityAction" format="string">
    </attr>
    <attr name="secondaryActivityName" format="string">
    </attr>
    <attr name="seekBarIncrement" format="integer">
    </attr>
    <attr name="seekBarPreferenceStyle" format="reference">
    </attr>
    <attr name="seekBarStyle" format="reference">
    </attr>
    <attr name="selectable" format="boolean">
    </attr>
    <attr name="selectableItemBackground" format="reference">
    </attr>
    <attr name="selectableItemBackgroundBorderless" format="reference">
    </attr>
    <attr name="selectionRequired" format="boolean">
    </attr>
    <attr name="setsTag" format="reference">
    </attr>
    <attr name="shapeAppearance" format="reference">
    </attr>
    <attr name="shapeAppearanceLargeComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceMediumComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="shapeAppearanceSmallComponent" format="reference">
    </attr>
    <attr name="shortcutMatchRequired" format="boolean">
    </attr>
    <attr name="shouldDisableView" format="boolean">
    </attr>
    <attr name="showAsAction">
        <flag name="never" value="0" />
        <flag name="ifRoom" value="1" />
        <flag name="always" value="2" />
        <flag name="withText" value="4" />
        <flag name="collapseActionView" value="8" />
    </attr>
    <attr name="showDividers">
        <flag name="none" value="0" />
        <flag name="beginning" value="1" />
        <flag name="middle" value="2" />
        <flag name="end" value="4" />
    </attr>
    <attr name="showMotionSpec" format="reference">
    </attr>
    <attr name="showPaths" format="boolean">
    </attr>
    <attr name="showSeekBarValue" format="boolean">
    </attr>
    <attr name="showText" format="boolean">
    </attr>
    <attr name="showTitle" format="boolean">
    </attr>
    <attr name="shrinkMotionSpec" format="reference">
    </attr>
    <attr name="singleChoiceItemLayout" format="reference">
    </attr>
    <attr name="singleLine" format="boolean">
    </attr>
    <attr name="singleLineTitle" format="boolean">
    </attr>
    <attr name="singleSelection" format="boolean">
    </attr>
    <attr name="sizePercent" format="float">
    </attr>
    <attr name="sliderStyle" format="reference">
    </attr>
    <attr name="snackbarButtonStyle" format="reference">
    </attr>
    <attr name="snackbarStyle" format="reference">
    </attr>
    <attr name="snackbarTextViewStyle" format="reference">
    </attr>
    <attr name="spanCount" format="integer">
    </attr>
    <attr name="spinBars" format="boolean">
    </attr>
    <attr name="spinnerDropDownItemStyle" format="reference">
    </attr>
    <attr name="spinnerStyle" format="reference">
    </attr>
    <attr name="splitLayoutDirection">
        <enum name="locale" value="0" />
        <enum name="ltr" value="1" />
        <enum name="rtl" value="2" />
    </attr>
    <attr name="splitMinSmallestWidth" format="dimension">
    </attr>
    <attr name="splitMinWidth" format="dimension">
    </attr>
    <attr name="splitRatio" format="float">
    </attr>
    <attr name="splitTrack" format="boolean">
    </attr>
    <attr name="springBoundary">
        <flag name="overshoot" value="0" />
        <flag name="bounceStart" value="1" />
        <flag name="bounceEnd" value="2" />
        <flag name="bounceBoth" value="3" />
    </attr>
    <attr name="springDamping" format="float">
    </attr>
    <attr name="springMass" format="float">
    </attr>
    <attr name="springStiffness" format="float">
    </attr>
    <attr name="springStopThreshold" format="float">
    </attr>
    <attr name="srcCompat" format="reference">
    </attr>
    <attr name="stackFromEnd" format="boolean">
    </attr>
    <attr name="staggered" format="float">
    </attr>
    <attr name="startIconCheckable" format="boolean">
    </attr>
    <attr name="startIconContentDescription" format="string">
    </attr>
    <attr name="startIconDrawable" format="reference">
    </attr>
    <attr name="startIconTint" format="color">
    </attr>
    <attr name="startIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="state_above_anchor" format="boolean">
    </attr>
    <attr name="state_collapsed" format="boolean">
    </attr>
    <attr name="state_collapsible" format="boolean">
    </attr>
    <attr name="state_dragged" format="boolean">
    </attr>
    <attr name="state_liftable" format="boolean">
    </attr>
    <attr name="state_lifted" format="boolean">
    </attr>
    <attr name="statusBarBackground" format="reference|color">
    </attr>
    <attr name="statusBarForeground" format="color">
    </attr>
    <attr name="statusBarScrim" format="color">
    </attr>
    <attr name="strokeColor" format="color">
    </attr>
    <attr name="strokeWidth" format="dimension">
    </attr>
    <attr name="subMenuArrow" format="reference">
    </attr>
    <attr name="submitBackground" format="reference">
    </attr>
    <attr name="subtitle" format="string">
    </attr>
    <attr name="subtitleTextAppearance" format="reference">
    </attr>
    <attr name="subtitleTextColor" format="color">
    </attr>
    <attr name="subtitleTextStyle" format="reference">
    </attr>
    <attr name="suffixText" format="string">
    </attr>
    <attr name="suffixTextAppearance" format="reference">
    </attr>
    <attr name="suffixTextColor" format="color">
    </attr>
    <attr name="suggestionRowLayout" format="reference">
    </attr>
    <attr name="summary" format="string">
    </attr>
    <attr name="summaryOff" format="string">
    </attr>
    <attr name="summaryOn" format="string">
    </attr>
    <attr name="switchMinWidth" format="dimension">
    </attr>
    <attr name="switchPadding" format="dimension">
    </attr>
    <attr name="switchPreferenceCompatStyle" format="reference">
    </attr>
    <attr name="switchPreferenceStyle" format="reference">
    </attr>
    <attr name="switchStyle" format="reference">
    </attr>
    <attr name="switchTextAppearance" format="reference">
    </attr>
    <attr name="switchTextOff" format="string">
    </attr>
    <attr name="switchTextOn" format="string">
    </attr>
    <attr name="tabBackground" format="reference">
    </attr>
    <attr name="tabContentStart" format="dimension">
    </attr>
    <attr name="tabGravity">
        <enum name="fill" value="0" />
        <enum name="center" value="1" />
        <enum name="start" value="2" />
    </attr>
    <attr name="tabIconTint" format="color">
    </attr>
    <attr name="tabIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tabIndicator" format="reference">
    </attr>
    <attr name="tabIndicatorAnimationDuration" format="integer">
    </attr>
    <attr name="tabIndicatorColor" format="color">
    </attr>
    <attr name="tabIndicatorFullWidth" format="boolean">
    </attr>
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="top" value="2" />
        <enum name="stretch" value="3" />
    </attr>
    <attr name="tabIndicatorHeight" format="dimension">
    </attr>
    <attr name="tabInlineLabel" format="boolean">
    </attr>
    <attr name="tabMaxWidth" format="dimension">
    </attr>
    <attr name="tabMinWidth" format="dimension">
    </attr>
    <attr name="tabMode">
        <enum name="scrollable" value="0" />
        <enum name="fixed" value="1" />
        <enum name="auto" value="2" />
    </attr>
    <attr name="tabPadding" format="dimension">
    </attr>
    <attr name="tabPaddingBottom" format="dimension">
    </attr>
    <attr name="tabPaddingEnd" format="dimension">
    </attr>
    <attr name="tabPaddingStart" format="dimension">
    </attr>
    <attr name="tabPaddingTop" format="dimension">
    </attr>
    <attr name="tabRippleColor" format="color">
    </attr>
    <attr name="tabSelectedTextColor" format="color">
    </attr>
    <attr name="tabStyle" format="reference">
    </attr>
    <attr name="tabTextAppearance" format="reference">
    </attr>
    <attr name="tabTextColor" format="color">
    </attr>
    <attr name="tabUnboundedRipple" format="boolean">
    </attr>
    <attr name="targetId" format="reference">
    </attr>
    <attr name="telltales_tailColor" format="color">
    </attr>
    <attr name="telltales_tailScale" format="float">
    </attr>
    <attr name="telltales_velocityMode">
        <enum name="layout" value="0" />
        <enum name="postLayout" value="1" />
        <enum name="staticPostLayout" value="2" />
        <enum name="staticLayout" value="3" />
    </attr>
    <attr name="textAllCaps" format="reference|boolean">
    </attr>
    <attr name="textAppearanceBody1" format="reference">
    </attr>
    <attr name="textAppearanceBody2" format="reference">
    </attr>
    <attr name="textAppearanceButton" format="reference">
    </attr>
    <attr name="textAppearanceCaption" format="reference">
    </attr>
    <attr name="textAppearanceHeadline1" format="reference">
    </attr>
    <attr name="textAppearanceHeadline2" format="reference">
    </attr>
    <attr name="textAppearanceHeadline3" format="reference">
    </attr>
    <attr name="textAppearanceHeadline4" format="reference">
    </attr>
    <attr name="textAppearanceHeadline5" format="reference">
    </attr>
    <attr name="textAppearanceHeadline6" format="reference">
    </attr>
    <attr name="textAppearanceLargePopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceLineHeightEnabled" format="boolean">
    </attr>
    <attr name="textAppearanceListItem" format="reference">
    </attr>
    <attr name="textAppearanceListItemSecondary" format="reference">
    </attr>
    <attr name="textAppearanceListItemSmall" format="reference">
    </attr>
    <attr name="textAppearanceOverline" format="reference">
    </attr>
    <attr name="textAppearancePopupMenuHeader" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultSubtitle" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultTitle" format="reference">
    </attr>
    <attr name="textAppearanceSmallPopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle1" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle2" format="reference">
    </attr>
    <attr name="textBackground" format="reference">
    </attr>
    <attr name="textBackgroundPanX" format="float">
    </attr>
    <attr name="textBackgroundPanY" format="float">
    </attr>
    <attr name="textBackgroundRotate" format="float">
    </attr>
    <attr name="textBackgroundZoom" format="float">
    </attr>
    <attr name="textColorAlertDialogListItem" format="reference|color">
    </attr>
    <attr name="textColorSearchUrl" format="reference|color">
    </attr>
    <attr name="textEndPadding" format="dimension">
    </attr>
    <attr name="textFillColor" format="color">
    </attr>
    <attr name="textInputLayoutFocusedRectEnabled" format="boolean">
    </attr>
    <attr name="textInputStyle" format="reference">
    </attr>
    <attr name="textLocale" format="string">
    </attr>
    <attr name="textOutlineColor" format="color">
    </attr>
    <attr name="textOutlineThickness" format="dimension">
    </attr>
    <attr name="textPanX" format="float">
    </attr>
    <attr name="textPanY" format="float">
    </attr>
    <attr name="textStartPadding" format="dimension">
    </attr>
    <attr name="textureBlurFactor" format="integer">
    </attr>
    <attr name="textureEffect">
        <enum name="none" value="0" />
        <enum name="frost" value="1" />
    </attr>
    <attr name="textureHeight" format="dimension">
    </attr>
    <attr name="textureWidth" format="dimension">
    </attr>
    <attr name="theme" format="reference">
    </attr>
    <attr name="themeLineHeight" format="dimension">
    </attr>
    <attr name="thickness" format="dimension">
    </attr>
    <attr name="thumbColor" format="color">
    </attr>
    <attr name="thumbElevation" format="dimension">
    </attr>
    <attr name="thumbRadius" format="dimension">
    </attr>
    <attr name="thumbTextPadding" format="dimension">
    </attr>
    <attr name="thumbTint" format="color">
    </attr>
    <attr name="thumbTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tickColor" format="color">
    </attr>
    <attr name="tickColorActive" format="color">
    </attr>
    <attr name="tickColorInactive" format="color">
    </attr>
    <attr name="tickMark" format="reference">
    </attr>
    <attr name="tickMarkTint" format="color">
    </attr>
    <attr name="tickMarkTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tint" format="color">
    </attr>
    <attr name="tintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="title" format="string">
    </attr>
    <attr name="titleEnabled" format="boolean">
    </attr>
    <attr name="titleMargin" format="dimension">
    </attr>
    <attr name="titleMarginBottom" format="dimension">
    </attr>
    <attr name="titleMarginEnd" format="dimension">
    </attr>
    <attr name="titleMarginStart" format="dimension">
    </attr>
    <attr name="titleMarginTop" format="dimension">
    </attr>
    <attr name="titleMargins" format="dimension">
    </attr>
    <attr name="titleTextAppearance" format="reference">
    </attr>
    <attr name="titleTextColor" format="color">
    </attr>
    <attr name="titleTextStyle" format="reference">
    </attr>
    <attr name="toolbarId" format="reference">
    </attr>
    <attr name="toolbarNavigationButtonStyle" format="reference">
    </attr>
    <attr name="toolbarStyle" format="reference">
    </attr>
    <attr name="tooltipForegroundColor" format="reference|color">
    </attr>
    <attr name="tooltipFrameBackground" format="reference">
    </attr>
    <attr name="tooltipStyle" format="reference">
    </attr>
    <attr name="tooltipText" format="string">
    </attr>
    <attr name="touchAnchorId" format="reference">
    </attr>
    <attr name="touchAnchorSide">
        <enum name="top" value="0" />
        <enum name="left" value="1" />
        <enum name="right" value="2" />
        <enum name="bottom" value="3" />
        <enum name="middle" value="4" />
        <enum name="start" value="5" />
        <enum name="end" value="6" />
    </attr>
    <attr name="touchRegionId" format="reference">
    </attr>
    <attr name="track" format="reference">
    </attr>
    <attr name="trackColor" format="color">
    </attr>
    <attr name="trackColorActive" format="color">
    </attr>
    <attr name="trackColorInactive" format="color">
    </attr>
    <attr name="trackHeight" format="dimension">
    </attr>
    <attr name="trackTint" format="color">
    </attr>
    <attr name="trackTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="transformPivotTarget" format="reference">
    </attr>
    <attr name="transitionDisable" format="boolean">
    </attr>
    <attr name="transitionEasing" format="string">
        <enum name="standard" value="0" />
        <enum name="accelerate" value="1" />
        <enum name="decelerate" value="2" />
        <enum name="linear" value="3" />
    </attr>
    <attr name="transitionFlags">
        <flag name="none" value="0" />
        <flag name="beginOnFirstDraw" value="1" />
        <flag name="disableIntraAutoTransition" value="2" />
        <flag name="onInterceptTouchReturnSwipe" value="4" />
    </attr>
    <attr name="transitionPathRotate" format="float">
    </attr>
    <attr name="transitionShapeAppearance" format="reference">
    </attr>
    <attr name="triggerId" format="reference">
    </attr>
    <attr name="triggerReceiver" format="reference">
    </attr>
    <attr name="triggerSlack" format="float">
    </attr>
    <attr name="ttcIndex" format="integer">
    </attr>
    <attr name="upDuration" format="integer">
    </attr>
    <attr name="updatesContinuously" format="boolean">
    </attr>
    <attr name="useCompatPadding" format="boolean">
    </attr>
    <attr name="useMaterialThemeColors" format="boolean">
    </attr>
    <attr name="useSimpleSummaryProvider" format="boolean">
    </attr>
    <attr name="values" format="reference">
    </attr>
    <attr name="verticalOffset" format="dimension">
    </attr>
    <attr name="viewInflaterClass" format="string">
    </attr>
    <attr name="viewTransitionMode">
        <enum name="currentState" value="0" />
        <enum name="allStates" value="1" />
        <enum name="noState" value="2" />
    </attr>
    <attr name="viewTransitionOnCross" format="reference">
    </attr>
    <attr name="viewTransitionOnNegativeCross" format="reference">
    </attr>
    <attr name="viewTransitionOnPositiveCross" format="reference">
    </attr>
    <attr name="visibilityMode">
        <enum name="normal" value="0" />
        <enum name="ignore" value="1" />
    </attr>
    <attr name="voiceIcon" format="reference">
    </attr>
    <attr name="warmth" format="float">
    </attr>
    <attr name="waveDecay" format="integer">
    </attr>
    <attr name="waveOffset" format="float|dimension">
    </attr>
    <attr name="wavePeriod" format="float">
    </attr>
    <attr name="wavePhase" format="float">
    </attr>
    <attr name="waveShape" format="string">
        <enum name="sin" value="0" />
        <enum name="square" value="1" />
        <enum name="triangle" value="2" />
        <enum name="sawtooth" value="3" />
        <enum name="reverseSawtooth" value="4" />
        <enum name="cos" value="5" />
        <enum name="bounce" value="6" />
    </attr>
    <attr name="waveVariesBy">
        <enum name="position" value="0" />
        <enum name="path" value="1" />
    </attr>
    <attr name="widgetLayout" format="reference">
    </attr>
    <attr name="windowActionBar" format="boolean">
    </attr>
    <attr name="windowActionBarOverlay" format="boolean">
    </attr>
    <attr name="windowActionModeOverlay" format="boolean">
    </attr>
    <attr name="windowFixedHeightMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedHeightMinor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowNoTitle" format="boolean">
    </attr>
    <attr name="yearSelectedStyle" format="reference">
    </attr>
    <attr name="yearStyle" format="reference">
    </attr>
    <attr name="yearTodayStyle" format="reference">
    </attr>
</resources>
