package com.alibaba.android.rimet.beesforce;

public final class R {

    public static final class anim {
        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
        public static final int abc_tooltip_enter = 0x7f01000a;
        public static final int abc_tooltip_exit = 0x7f01000b;
        public static final int anim_fade_in = 0x7f01000c;
        public static final int anim_fade_out = 0x7f01000d;
        public static final int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000e;
        public static final int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000f;
        public static final int btn_checkbox_to_checked_icon_null_animation = 0x7f010010;
        public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f010011;
        public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010012;
        public static final int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010013;
        public static final int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010014;
        public static final int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010015;
        public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010016;
        public static final int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010017;
        public static final int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010018;
        public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010019;
        public static final int design_bottom_sheet_slide_in = 0x7f01001a;
        public static final int design_bottom_sheet_slide_out = 0x7f01001b;
        public static final int design_snackbar_in = 0x7f01001c;
        public static final int design_snackbar_out = 0x7f01001d;
        public static final int dialog_enter = 0x7f01001e;
        public static final int dialog_out = 0x7f01001f;
        public static final int fragment_fast_out_extra_slow_in = 0x7f010020;
        public static final int mtrl_bottom_sheet_slide_in = 0x7f010021;
        public static final int mtrl_bottom_sheet_slide_out = 0x7f010022;
        public static final int mtrl_card_lowers_interpolator = 0x7f010023;
    }

    public static final class animator {
        public static final int design_appbar_state_list_animator = 0x7f020000;
        public static final int design_fab_hide_motion_spec = 0x7f020001;
        public static final int design_fab_show_motion_spec = 0x7f020002;
        public static final int fragment_close_enter = 0x7f020003;
        public static final int fragment_close_exit = 0x7f020004;
        public static final int fragment_fade_enter = 0x7f020005;
        public static final int fragment_fade_exit = 0x7f020006;
        public static final int fragment_open_enter = 0x7f020007;
        public static final int fragment_open_exit = 0x7f020008;
        public static final int mtrl_btn_state_list_anim = 0x7f020009;
        public static final int mtrl_btn_unelevated_state_list_anim = 0x7f02000a;
        public static final int mtrl_card_state_list_anim = 0x7f02000b;
        public static final int mtrl_chip_state_list_anim = 0x7f02000c;
        public static final int mtrl_extended_fab_change_size_motion_spec = 0x7f02000d;
        public static final int mtrl_extended_fab_hide_motion_spec = 0x7f02000e;
        public static final int mtrl_extended_fab_show_motion_spec = 0x7f02000f;
        public static final int mtrl_extended_fab_state_list_animator = 0x7f020010;
        public static final int mtrl_fab_hide_motion_spec = 0x7f020011;
        public static final int mtrl_fab_show_motion_spec = 0x7f020012;
        public static final int mtrl_fab_transformation_sheet_collapse_spec = 0x7f020013;
        public static final int mtrl_fab_transformation_sheet_expand_spec = 0x7f020014;
    }

    public static final class array {
        public static final int array_joystick_type = 0x7f030000;
        public static final int array_joystick_type_values = 0x7f030001;
        public static final int weeks = 0x7f030002;
    }

    public static final class attr {
        public static final int SharedValue = 0x7f040000;
        public static final int SharedValueId = 0x7f040001;
        public static final int actionBarDivider = 0x7f040002;
        public static final int actionBarItemBackground = 0x7f040003;
        public static final int actionBarPopupTheme = 0x7f040004;
        public static final int actionBarSize = 0x7f040005;
        public static final int actionBarSplitStyle = 0x7f040006;
        public static final int actionBarStyle = 0x7f040007;
        public static final int actionBarTabBarStyle = 0x7f040008;
        public static final int actionBarTabStyle = 0x7f040009;
        public static final int actionBarTabTextStyle = 0x7f04000a;
        public static final int actionBarTheme = 0x7f04000b;
        public static final int actionBarWidgetTheme = 0x7f04000c;
        public static final int actionButtonStyle = 0x7f04000d;
        public static final int actionDropDownStyle = 0x7f04000e;
        public static final int actionLayout = 0x7f04000f;
        public static final int actionMenuTextAppearance = 0x7f040010;
        public static final int actionMenuTextColor = 0x7f040011;
        public static final int actionModeBackground = 0x7f040012;
        public static final int actionModeCloseButtonStyle = 0x7f040013;
        public static final int actionModeCloseContentDescription = 0x7f040014;
        public static final int actionModeCloseDrawable = 0x7f040015;
        public static final int actionModeCopyDrawable = 0x7f040016;
        public static final int actionModeCutDrawable = 0x7f040017;
        public static final int actionModeFindDrawable = 0x7f040018;
        public static final int actionModePasteDrawable = 0x7f040019;
        public static final int actionModePopupWindowStyle = 0x7f04001a;
        public static final int actionModeSelectAllDrawable = 0x7f04001b;
        public static final int actionModeShareDrawable = 0x7f04001c;
        public static final int actionModeSplitBackground = 0x7f04001d;
        public static final int actionModeStyle = 0x7f04001e;
        public static final int actionModeTheme = 0x7f04001f;
        public static final int actionModeWebSearchDrawable = 0x7f040020;
        public static final int actionOverflowButtonStyle = 0x7f040021;
        public static final int actionOverflowMenuStyle = 0x7f040022;
        public static final int actionProviderClass = 0x7f040023;
        public static final int actionTextColorAlpha = 0x7f040024;
        public static final int actionViewClass = 0x7f040025;
        public static final int activityAction = 0x7f040026;
        public static final int activityChooserViewStyle = 0x7f040027;
        public static final int activityName = 0x7f040028;
        public static final int adjustable = 0x7f040029;
        public static final int alertDialogButtonGroupStyle = 0x7f04002a;
        public static final int alertDialogCenterButtons = 0x7f04002b;
        public static final int alertDialogStyle = 0x7f04002c;
        public static final int alertDialogTheme = 0x7f04002d;
        public static final int allowDividerAbove = 0x7f04002e;
        public static final int allowDividerAfterLastItem = 0x7f04002f;
        public static final int allowDividerBelow = 0x7f040030;
        public static final int allowStacking = 0x7f040031;
        public static final int alpha = 0x7f040032;
        public static final int alphabeticModifiers = 0x7f040033;
        public static final int altSrc = 0x7f040034;
        public static final int alwaysExpand = 0x7f040035;
        public static final int animateCircleAngleTo = 0x7f040036;
        public static final int animateRelativeTo = 0x7f040037;
        public static final int animationMode = 0x7f040038;
        public static final int appBarLayoutStyle = 0x7f040039;
        public static final int applyMotionScene = 0x7f04003a;
        public static final int arcMode = 0x7f04003b;
        public static final int arrowHeadLength = 0x7f04003c;
        public static final int arrowShaftLength = 0x7f04003d;
        public static final int attributeName = 0x7f04003e;
        public static final int autoCompleteMode = 0x7f04003f;
        public static final int autoCompleteTextViewStyle = 0x7f040040;
        public static final int autoSizeMaxTextSize = 0x7f040041;
        public static final int autoSizeMinTextSize = 0x7f040042;
        public static final int autoSizePresetSizes = 0x7f040043;
        public static final int autoSizeStepGranularity = 0x7f040044;
        public static final int autoSizeTextType = 0x7f040045;
        public static final int autoTransition = 0x7f040046;
        public static final int background = 0x7f040047;
        public static final int backgroundColor = 0x7f040048;
        public static final int backgroundInsetBottom = 0x7f040049;
        public static final int backgroundInsetEnd = 0x7f04004a;
        public static final int backgroundInsetStart = 0x7f04004b;
        public static final int backgroundInsetTop = 0x7f04004c;
        public static final int backgroundOverlayColorAlpha = 0x7f04004d;
        public static final int backgroundSplit = 0x7f04004e;
        public static final int backgroundStacked = 0x7f04004f;
        public static final int backgroundTint = 0x7f040050;
        public static final int backgroundTintMode = 0x7f040051;
        public static final int badgeGravity = 0x7f040052;
        public static final int badgeStyle = 0x7f040053;
        public static final int badgeTextColor = 0x7f040054;
        public static final int barLength = 0x7f040055;
        public static final int barrierAllowsGoneWidgets = 0x7f040056;
        public static final int barrierDirection = 0x7f040057;
        public static final int barrierMargin = 0x7f040058;
        public static final int behavior_autoHide = 0x7f040059;
        public static final int behavior_autoShrink = 0x7f04005a;
        public static final int behavior_draggable = 0x7f04005b;
        public static final int behavior_expandedOffset = 0x7f04005c;
        public static final int behavior_fitToContents = 0x7f04005d;
        public static final int behavior_halfExpandedRatio = 0x7f04005e;
        public static final int behavior_hideable = 0x7f04005f;
        public static final int behavior_overlapTop = 0x7f040060;
        public static final int behavior_peekHeight = 0x7f040061;
        public static final int behavior_saveFlags = 0x7f040062;
        public static final int behavior_skipCollapsed = 0x7f040063;
        public static final int blendSrc = 0x7f040064;
        public static final int borderRound = 0x7f040065;
        public static final int borderRoundPercent = 0x7f040066;
        public static final int borderWidth = 0x7f040067;
        public static final int borderlessButtonStyle = 0x7f040068;
        public static final int bottomAppBarStyle = 0x7f040069;
        public static final int bottomNavigationStyle = 0x7f04006a;
        public static final int bottomSheetDialogTheme = 0x7f04006b;
        public static final int bottomSheetStyle = 0x7f04006c;
        public static final int boxBackgroundColor = 0x7f04006d;
        public static final int boxBackgroundMode = 0x7f04006e;
        public static final int boxCollapsedPaddingTop = 0x7f04006f;
        public static final int boxCornerRadiusBottomEnd = 0x7f040070;
        public static final int boxCornerRadiusBottomStart = 0x7f040071;
        public static final int boxCornerRadiusTopEnd = 0x7f040072;
        public static final int boxCornerRadiusTopStart = 0x7f040073;
        public static final int boxStrokeColor = 0x7f040074;
        public static final int boxStrokeErrorColor = 0x7f040075;
        public static final int boxStrokeWidth = 0x7f040076;
        public static final int boxStrokeWidthFocused = 0x7f040077;
        public static final int brightness = 0x7f040078;
        public static final int buttonBarButtonStyle = 0x7f040079;
        public static final int buttonBarNegativeButtonStyle = 0x7f04007a;
        public static final int buttonBarNeutralButtonStyle = 0x7f04007b;
        public static final int buttonBarPositiveButtonStyle = 0x7f04007c;
        public static final int buttonBarStyle = 0x7f04007d;
        public static final int buttonCompat = 0x7f04007e;
        public static final int buttonGravity = 0x7f04007f;
        public static final int buttonIconDimen = 0x7f040080;
        public static final int buttonPanelSideLayout = 0x7f040081;
        public static final int buttonStyle = 0x7f040082;
        public static final int buttonStyleSmall = 0x7f040083;
        public static final int buttonTint = 0x7f040084;
        public static final int buttonTintMode = 0x7f040085;
        public static final int cardBackgroundColor = 0x7f040086;
        public static final int cardCornerRadius = 0x7f040087;
        public static final int cardElevation = 0x7f040088;
        public static final int cardForegroundColor = 0x7f040089;
        public static final int cardMaxElevation = 0x7f04008a;
        public static final int cardPreventCornerOverlap = 0x7f04008b;
        public static final int cardUseCompatPadding = 0x7f04008c;
        public static final int cardViewStyle = 0x7f04008d;
        public static final int carousel_backwardTransition = 0x7f04008e;
        public static final int carousel_emptyViewsBehavior = 0x7f04008f;
        public static final int carousel_firstView = 0x7f040090;
        public static final int carousel_forwardTransition = 0x7f040091;
        public static final int carousel_infinite = 0x7f040092;
        public static final int carousel_nextState = 0x7f040093;
        public static final int carousel_previousState = 0x7f040094;
        public static final int carousel_touchUpMode = 0x7f040095;
        public static final int carousel_touchUp_dampeningFactor = 0x7f040096;
        public static final int carousel_touchUp_velocityThreshold = 0x7f040097;
        public static final int chainUseRtl = 0x7f040098;
        public static final int checkBoxPreferenceStyle = 0x7f040099;
        public static final int checkMarkCompat = 0x7f04009a;
        public static final int checkMarkTint = 0x7f04009b;
        public static final int checkMarkTintMode = 0x7f04009c;
        public static final int checkboxStyle = 0x7f04009d;
        public static final int checkedButton = 0x7f04009e;
        public static final int checkedChip = 0x7f04009f;
        public static final int checkedIcon = 0x7f0400a0;
        public static final int checkedIconEnabled = 0x7f0400a1;
        public static final int checkedIconTint = 0x7f0400a2;
        public static final int checkedIconVisible = 0x7f0400a3;
        public static final int checkedTextViewStyle = 0x7f0400a4;
        public static final int chipBackgroundColor = 0x7f0400a5;
        public static final int chipCornerRadius = 0x7f0400a6;
        public static final int chipEndPadding = 0x7f0400a7;
        public static final int chipGroupStyle = 0x7f0400a8;
        public static final int chipIcon = 0x7f0400a9;
        public static final int chipIconEnabled = 0x7f0400aa;
        public static final int chipIconSize = 0x7f0400ab;
        public static final int chipIconTint = 0x7f0400ac;
        public static final int chipIconVisible = 0x7f0400ad;
        public static final int chipMinHeight = 0x7f0400ae;
        public static final int chipMinTouchTargetSize = 0x7f0400af;
        public static final int chipSpacing = 0x7f0400b0;
        public static final int chipSpacingHorizontal = 0x7f0400b1;
        public static final int chipSpacingVertical = 0x7f0400b2;
        public static final int chipStandaloneStyle = 0x7f0400b3;
        public static final int chipStartPadding = 0x7f0400b4;
        public static final int chipStrokeColor = 0x7f0400b5;
        public static final int chipStrokeWidth = 0x7f0400b6;
        public static final int chipStyle = 0x7f0400b7;
        public static final int chipSurfaceColor = 0x7f0400b8;
        public static final int circleRadius = 0x7f0400b9;
        public static final int circularflow_angles = 0x7f0400ba;
        public static final int circularflow_defaultAngle = 0x7f0400bb;
        public static final int circularflow_defaultRadius = 0x7f0400bc;
        public static final int circularflow_radiusInDP = 0x7f0400bd;
        public static final int circularflow_viewCenter = 0x7f0400be;
        public static final int clearTop = 0x7f0400bf;
        public static final int clearsTag = 0x7f0400c0;
        public static final int clickAction = 0x7f0400c1;
        public static final int closeIcon = 0x7f0400c2;
        public static final int closeIconEnabled = 0x7f0400c3;
        public static final int closeIconEndPadding = 0x7f0400c4;
        public static final int closeIconSize = 0x7f0400c5;
        public static final int closeIconStartPadding = 0x7f0400c6;
        public static final int closeIconTint = 0x7f0400c7;
        public static final int closeIconVisible = 0x7f0400c8;
        public static final int closeItemLayout = 0x7f0400c9;
        public static final int collapseContentDescription = 0x7f0400ca;
        public static final int collapseIcon = 0x7f0400cb;
        public static final int collapsedTitleGravity = 0x7f0400cc;
        public static final int collapsedTitleTextAppearance = 0x7f0400cd;
        public static final int color = 0x7f0400ce;
        public static final int colorAccent = 0x7f0400cf;
        public static final int colorBackgroundFloating = 0x7f0400d0;
        public static final int colorButtonNormal = 0x7f0400d1;
        public static final int colorControlActivated = 0x7f0400d2;
        public static final int colorControlHighlight = 0x7f0400d3;
        public static final int colorControlNormal = 0x7f0400d4;
        public static final int colorError = 0x7f0400d5;
        public static final int colorOnBackground = 0x7f0400d6;
        public static final int colorOnError = 0x7f0400d7;
        public static final int colorOnPrimary = 0x7f0400d8;
        public static final int colorOnPrimarySurface = 0x7f0400d9;
        public static final int colorOnSecondary = 0x7f0400da;
        public static final int colorOnSurface = 0x7f0400db;
        public static final int colorPrimary = 0x7f0400dc;
        public static final int colorPrimaryDark = 0x7f0400dd;
        public static final int colorPrimarySurface = 0x7f0400de;
        public static final int colorPrimaryVariant = 0x7f0400df;
        public static final int colorSecondary = 0x7f0400e0;
        public static final int colorSecondaryVariant = 0x7f0400e1;
        public static final int colorSurface = 0x7f0400e2;
        public static final int colorSwitchThumbNormal = 0x7f0400e3;
        public static final int commitIcon = 0x7f0400e4;
        public static final int constraintRotate = 0x7f0400e5;
        public static final int constraintSet = 0x7f0400e6;
        public static final int constraintSetEnd = 0x7f0400e7;
        public static final int constraintSetStart = 0x7f0400e8;
        public static final int constraint_referenced_ids = 0x7f0400e9;
        public static final int constraint_referenced_tags = 0x7f0400ea;
        public static final int constraints = 0x7f0400eb;
        public static final int content = 0x7f0400ec;
        public static final int contentDescription = 0x7f0400ed;
        public static final int contentInsetEnd = 0x7f0400ee;
        public static final int contentInsetEndWithActions = 0x7f0400ef;
        public static final int contentInsetLeft = 0x7f0400f0;
        public static final int contentInsetRight = 0x7f0400f1;
        public static final int contentInsetStart = 0x7f0400f2;
        public static final int contentInsetStartWithNavigation = 0x7f0400f3;
        public static final int contentPadding = 0x7f0400f4;
        public static final int contentPaddingBottom = 0x7f0400f5;
        public static final int contentPaddingLeft = 0x7f0400f6;
        public static final int contentPaddingRight = 0x7f0400f7;
        public static final int contentPaddingTop = 0x7f0400f8;
        public static final int contentScrim = 0x7f0400f9;
        public static final int contrast = 0x7f0400fa;
        public static final int controlBackground = 0x7f0400fb;
        public static final int coordinatorLayoutStyle = 0x7f0400fc;
        public static final int cornerFamily = 0x7f0400fd;
        public static final int cornerFamilyBottomLeft = 0x7f0400fe;
        public static final int cornerFamilyBottomRight = 0x7f0400ff;
        public static final int cornerFamilyTopLeft = 0x7f040100;
        public static final int cornerFamilyTopRight = 0x7f040101;
        public static final int cornerRadius = 0x7f040102;
        public static final int cornerSize = 0x7f040103;
        public static final int cornerSizeBottomLeft = 0x7f040104;
        public static final int cornerSizeBottomRight = 0x7f040105;
        public static final int cornerSizeTopLeft = 0x7f040106;
        public static final int cornerSizeTopRight = 0x7f040107;
        public static final int counterEnabled = 0x7f040108;
        public static final int counterMaxLength = 0x7f040109;
        public static final int counterOverflowTextAppearance = 0x7f04010a;
        public static final int counterOverflowTextColor = 0x7f04010b;
        public static final int counterTextAppearance = 0x7f04010c;
        public static final int counterTextColor = 0x7f04010d;
        public static final int crossfade = 0x7f04010e;
        public static final int currentState = 0x7f04010f;
        public static final int curveFit = 0x7f040110;
        public static final int customBoolean = 0x7f040111;
        public static final int customColorDrawableValue = 0x7f040112;
        public static final int customColorValue = 0x7f040113;
        public static final int customDimension = 0x7f040114;
        public static final int customFloatValue = 0x7f040115;
        public static final int customIntegerValue = 0x7f040116;
        public static final int customNavigationLayout = 0x7f040117;
        public static final int customPixelDimension = 0x7f040118;
        public static final int customReference = 0x7f040119;
        public static final int customStringValue = 0x7f04011a;
        public static final int dayInvalidStyle = 0x7f04011b;
        public static final int daySelectedStyle = 0x7f04011c;
        public static final int dayStyle = 0x7f04011d;
        public static final int dayTodayStyle = 0x7f04011e;
        public static final int defaultDuration = 0x7f04011f;
        public static final int defaultQueryHint = 0x7f040120;
        public static final int defaultState = 0x7f040121;
        public static final int defaultValue = 0x7f040122;
        public static final int deltaPolarAngle = 0x7f040123;
        public static final int deltaPolarRadius = 0x7f040124;
        public static final int dependency = 0x7f040125;
        public static final int deriveConstraintsFrom = 0x7f040126;
        public static final int dialogCornerRadius = 0x7f040127;
        public static final int dialogIcon = 0x7f040128;
        public static final int dialogLayout = 0x7f040129;
        public static final int dialogMessage = 0x7f04012a;
        public static final int dialogPreferenceStyle = 0x7f04012b;
        public static final int dialogPreferredPadding = 0x7f04012c;
        public static final int dialogTheme = 0x7f04012d;
        public static final int dialogTitle = 0x7f04012e;
        public static final int disableDependentsState = 0x7f04012f;
        public static final int displayOptions = 0x7f040130;
        public static final int divider = 0x7f040131;
        public static final int dividerHorizontal = 0x7f040132;
        public static final int dividerPadding = 0x7f040133;
        public static final int dividerVertical = 0x7f040134;
        public static final int dragDirection = 0x7f040135;
        public static final int dragScale = 0x7f040136;
        public static final int dragThreshold = 0x7f040137;
        public static final int drawPath = 0x7f040138;
        public static final int drawableBottomCompat = 0x7f040139;
        public static final int drawableEndCompat = 0x7f04013a;
        public static final int drawableLeftCompat = 0x7f04013b;
        public static final int drawableRightCompat = 0x7f04013c;
        public static final int drawableSize = 0x7f04013d;
        public static final int drawableStartCompat = 0x7f04013e;
        public static final int drawableTint = 0x7f04013f;
        public static final int drawableTintMode = 0x7f040140;
        public static final int drawableTopCompat = 0x7f040141;
        public static final int drawerArrowStyle = 0x7f040142;
        public static final int dropDownListViewStyle = 0x7f040143;
        public static final int dropdownListPreferredItemHeight = 0x7f040144;
        public static final int dropdownPreferenceStyle = 0x7f040145;
        public static final int duration = 0x7f040146;
        public static final int editTextBackground = 0x7f040147;
        public static final int editTextColor = 0x7f040148;
        public static final int editTextPreferenceStyle = 0x7f040149;
        public static final int editTextStyle = 0x7f04014a;
        public static final int elevation = 0x7f04014b;
        public static final int elevationOverlayColor = 0x7f04014c;
        public static final int elevationOverlayEnabled = 0x7f04014d;
        public static final int emojiCompatEnabled = 0x7f04014e;
        public static final int enableCopying = 0x7f04014f;
        public static final int enabled = 0x7f040150;
        public static final int endIconCheckable = 0x7f040151;
        public static final int endIconContentDescription = 0x7f040152;
        public static final int endIconDrawable = 0x7f040153;
        public static final int endIconMode = 0x7f040154;
        public static final int endIconTint = 0x7f040155;
        public static final int endIconTintMode = 0x7f040156;
        public static final int enforceMaterialTheme = 0x7f040157;
        public static final int enforceTextAppearance = 0x7f040158;
        public static final int ensureMinTouchTargetSize = 0x7f040159;
        public static final int entries = 0x7f04015a;
        public static final int entryValues = 0x7f04015b;
        public static final int errorContentDescription = 0x7f04015c;
        public static final int errorEnabled = 0x7f04015d;
        public static final int errorIconDrawable = 0x7f04015e;
        public static final int errorIconTint = 0x7f04015f;
        public static final int errorIconTintMode = 0x7f040160;
        public static final int errorTextAppearance = 0x7f040161;
        public static final int errorTextColor = 0x7f040162;
        public static final int expandActivityOverflowButtonDrawable = 0x7f040163;
        public static final int expanded = 0x7f040164;
        public static final int expandedTitleGravity = 0x7f040165;
        public static final int expandedTitleMargin = 0x7f040166;
        public static final int expandedTitleMarginBottom = 0x7f040167;
        public static final int expandedTitleMarginEnd = 0x7f040168;
        public static final int expandedTitleMarginStart = 0x7f040169;
        public static final int expandedTitleMarginTop = 0x7f04016a;
        public static final int expandedTitleTextAppearance = 0x7f04016b;
        public static final int extendMotionSpec = 0x7f04016c;
        public static final int extendedFloatingActionButtonStyle = 0x7f04016d;
        public static final int fabAlignmentMode = 0x7f04016e;
        public static final int fabAnimationMode = 0x7f04016f;
        public static final int fabCradleMargin = 0x7f040170;
        public static final int fabCradleRoundedCornerRadius = 0x7f040171;
        public static final int fabCradleVerticalOffset = 0x7f040172;
        public static final int fabCustomSize = 0x7f040173;
        public static final int fabSize = 0x7f040174;
        public static final int fastScrollEnabled = 0x7f040175;
        public static final int fastScrollHorizontalThumbDrawable = 0x7f040176;
        public static final int fastScrollHorizontalTrackDrawable = 0x7f040177;
        public static final int fastScrollVerticalThumbDrawable = 0x7f040178;
        public static final int fastScrollVerticalTrackDrawable = 0x7f040179;
        public static final int finishPrimaryWithSecondary = 0x7f04017a;
        public static final int finishSecondaryWithPrimary = 0x7f04017b;
        public static final int firstBaselineToTopHeight = 0x7f04017c;
        public static final int floatingActionButtonStyle = 0x7f04017d;
        public static final int flow_firstHorizontalBias = 0x7f04017e;
        public static final int flow_firstHorizontalStyle = 0x7f04017f;
        public static final int flow_firstVerticalBias = 0x7f040180;
        public static final int flow_firstVerticalStyle = 0x7f040181;
        public static final int flow_horizontalAlign = 0x7f040182;
        public static final int flow_horizontalBias = 0x7f040183;
        public static final int flow_horizontalGap = 0x7f040184;
        public static final int flow_horizontalStyle = 0x7f040185;
        public static final int flow_lastHorizontalBias = 0x7f040186;
        public static final int flow_lastHorizontalStyle = 0x7f040187;
        public static final int flow_lastVerticalBias = 0x7f040188;
        public static final int flow_lastVerticalStyle = 0x7f040189;
        public static final int flow_maxElementsWrap = 0x7f04018a;
        public static final int flow_padding = 0x7f04018b;
        public static final int flow_verticalAlign = 0x7f04018c;
        public static final int flow_verticalBias = 0x7f04018d;
        public static final int flow_verticalGap = 0x7f04018e;
        public static final int flow_verticalStyle = 0x7f04018f;
        public static final int flow_wrapMode = 0x7f040190;
        public static final int font = 0x7f040191;
        public static final int fontFamily = 0x7f040192;
        public static final int fontProviderAuthority = 0x7f040193;
        public static final int fontProviderCerts = 0x7f040194;
        public static final int fontProviderFetchStrategy = 0x7f040195;
        public static final int fontProviderFetchTimeout = 0x7f040196;
        public static final int fontProviderPackage = 0x7f040197;
        public static final int fontProviderQuery = 0x7f040198;
        public static final int fontProviderSystemFontFamily = 0x7f040199;
        public static final int fontStyle = 0x7f04019a;
        public static final int fontVariationSettings = 0x7f04019b;
        public static final int fontWeight = 0x7f04019c;
        public static final int foregroundInsidePadding = 0x7f04019d;
        public static final int fragment = 0x7f04019e;
        public static final int framePosition = 0x7f04019f;
        public static final int gapBetweenBars = 0x7f0401a0;
        public static final int gestureInsetBottomIgnored = 0x7f0401a1;
        public static final int goIcon = 0x7f0401a2;
        public static final int guidelineUseRtl = 0x7f0401a3;
        public static final int haloColor = 0x7f0401a4;
        public static final int haloRadius = 0x7f0401a5;
        public static final int headerLayout = 0x7f0401a6;
        public static final int height = 0x7f0401a7;
        public static final int helperText = 0x7f0401a8;
        public static final int helperTextEnabled = 0x7f0401a9;
        public static final int helperTextTextAppearance = 0x7f0401aa;
        public static final int helperTextTextColor = 0x7f0401ab;
        public static final int hideMotionSpec = 0x7f0401ac;
        public static final int hideOnContentScroll = 0x7f0401ad;
        public static final int hideOnScroll = 0x7f0401ae;
        public static final int hintAnimationEnabled = 0x7f0401af;
        public static final int hintEnabled = 0x7f0401b0;
        public static final int hintTextAppearance = 0x7f0401b1;
        public static final int hintTextColor = 0x7f0401b2;
        public static final int homeAsUpIndicator = 0x7f0401b3;
        public static final int homeLayout = 0x7f0401b4;
        public static final int horizontalOffset = 0x7f0401b5;
        public static final int hoveredFocusedTranslationZ = 0x7f0401b6;
        public static final int icon = 0x7f0401b7;
        public static final int iconEndPadding = 0x7f0401b8;
        public static final int iconGravity = 0x7f0401b9;
        public static final int iconPadding = 0x7f0401ba;
        public static final int iconSize = 0x7f0401bb;
        public static final int iconSpaceReserved = 0x7f0401bc;
        public static final int iconStartPadding = 0x7f0401bd;
        public static final int iconTint = 0x7f0401be;
        public static final int iconTintMode = 0x7f0401bf;
        public static final int iconifiedByDefault = 0x7f0401c0;
        public static final int ifTagNotSet = 0x7f0401c1;
        public static final int ifTagSet = 0x7f0401c2;
        public static final int imageButtonStyle = 0x7f0401c3;
        public static final int imagePanX = 0x7f0401c4;
        public static final int imagePanY = 0x7f0401c5;
        public static final int imageRotate = 0x7f0401c6;
        public static final int imageZoom = 0x7f0401c7;
        public static final int indeterminateProgressStyle = 0x7f0401c8;
        public static final int initialActivityCount = 0x7f0401c9;
        public static final int initialExpandedChildrenCount = 0x7f0401ca;
        public static final int insetForeground = 0x7f0401cb;
        public static final int isLightTheme = 0x7f0401cc;
        public static final int isMaterialTheme = 0x7f0401cd;
        public static final int isPreferenceVisible = 0x7f0401ce;
        public static final int itemBackground = 0x7f0401cf;
        public static final int itemFillColor = 0x7f0401d0;
        public static final int itemHorizontalPadding = 0x7f0401d1;
        public static final int itemHorizontalTranslationEnabled = 0x7f0401d2;
        public static final int itemIconPadding = 0x7f0401d3;
        public static final int itemIconSize = 0x7f0401d4;
        public static final int itemIconTint = 0x7f0401d5;
        public static final int itemMaxLines = 0x7f0401d6;
        public static final int itemPadding = 0x7f0401d7;
        public static final int itemRippleColor = 0x7f0401d8;
        public static final int itemShapeAppearance = 0x7f0401d9;
        public static final int itemShapeAppearanceOverlay = 0x7f0401da;
        public static final int itemShapeFillColor = 0x7f0401db;
        public static final int itemShapeInsetBottom = 0x7f0401dc;
        public static final int itemShapeInsetEnd = 0x7f0401dd;
        public static final int itemShapeInsetStart = 0x7f0401de;
        public static final int itemShapeInsetTop = 0x7f0401df;
        public static final int itemSpacing = 0x7f0401e0;
        public static final int itemStrokeColor = 0x7f0401e1;
        public static final int itemStrokeWidth = 0x7f0401e2;
        public static final int itemTextAppearance = 0x7f0401e3;
        public static final int itemTextAppearanceActive = 0x7f0401e4;
        public static final int itemTextAppearanceInactive = 0x7f0401e5;
        public static final int itemTextColor = 0x7f0401e6;
        public static final int key = 0x7f0401e7;
        public static final int keyPositionType = 0x7f0401e8;
        public static final int keylines = 0x7f0401e9;
        public static final int lStar = 0x7f0401ea;
        public static final int labelBehavior = 0x7f0401eb;
        public static final int labelStyle = 0x7f0401ec;
        public static final int labelVisibilityMode = 0x7f0401ed;
        public static final int lastBaselineToBottomHeight = 0x7f0401ee;
        public static final int layout = 0x7f0401ef;
        public static final int layoutDescription = 0x7f0401f0;
        public static final int layoutDuringTransition = 0x7f0401f1;
        public static final int layoutManager = 0x7f0401f2;
        public static final int layout_anchor = 0x7f0401f3;
        public static final int layout_anchorGravity = 0x7f0401f4;
        public static final int layout_behavior = 0x7f0401f5;
        public static final int layout_collapseMode = 0x7f0401f6;
        public static final int layout_collapseParallaxMultiplier = 0x7f0401f7;
        public static final int layout_constrainedHeight = 0x7f0401f8;
        public static final int layout_constrainedWidth = 0x7f0401f9;
        public static final int layout_constraintBaseline_creator = 0x7f0401fa;
        public static final int layout_constraintBaseline_toBaselineOf = 0x7f0401fb;
        public static final int layout_constraintBaseline_toBottomOf = 0x7f0401fc;
        public static final int layout_constraintBaseline_toTopOf = 0x7f0401fd;
        public static final int layout_constraintBottom_creator = 0x7f0401fe;
        public static final int layout_constraintBottom_toBottomOf = 0x7f0401ff;
        public static final int layout_constraintBottom_toTopOf = 0x7f040200;
        public static final int layout_constraintCircle = 0x7f040201;
        public static final int layout_constraintCircleAngle = 0x7f040202;
        public static final int layout_constraintCircleRadius = 0x7f040203;
        public static final int layout_constraintDimensionRatio = 0x7f040204;
        public static final int layout_constraintEnd_toEndOf = 0x7f040205;
        public static final int layout_constraintEnd_toStartOf = 0x7f040206;
        public static final int layout_constraintGuide_begin = 0x7f040207;
        public static final int layout_constraintGuide_end = 0x7f040208;
        public static final int layout_constraintGuide_percent = 0x7f040209;
        public static final int layout_constraintHeight = 0x7f04020a;
        public static final int layout_constraintHeight_default = 0x7f04020b;
        public static final int layout_constraintHeight_max = 0x7f04020c;
        public static final int layout_constraintHeight_min = 0x7f04020d;
        public static final int layout_constraintHeight_percent = 0x7f04020e;
        public static final int layout_constraintHorizontal_bias = 0x7f04020f;
        public static final int layout_constraintHorizontal_chainStyle = 0x7f040210;
        public static final int layout_constraintHorizontal_weight = 0x7f040211;
        public static final int layout_constraintLeft_creator = 0x7f040212;
        public static final int layout_constraintLeft_toLeftOf = 0x7f040213;
        public static final int layout_constraintLeft_toRightOf = 0x7f040214;
        public static final int layout_constraintRight_creator = 0x7f040215;
        public static final int layout_constraintRight_toLeftOf = 0x7f040216;
        public static final int layout_constraintRight_toRightOf = 0x7f040217;
        public static final int layout_constraintStart_toEndOf = 0x7f040218;
        public static final int layout_constraintStart_toStartOf = 0x7f040219;
        public static final int layout_constraintTag = 0x7f04021a;
        public static final int layout_constraintTop_creator = 0x7f04021b;
        public static final int layout_constraintTop_toBottomOf = 0x7f04021c;
        public static final int layout_constraintTop_toTopOf = 0x7f04021d;
        public static final int layout_constraintVertical_bias = 0x7f04021e;
        public static final int layout_constraintVertical_chainStyle = 0x7f04021f;
        public static final int layout_constraintVertical_weight = 0x7f040220;
        public static final int layout_constraintWidth = 0x7f040221;
        public static final int layout_constraintWidth_default = 0x7f040222;
        public static final int layout_constraintWidth_max = 0x7f040223;
        public static final int layout_constraintWidth_min = 0x7f040224;
        public static final int layout_constraintWidth_percent = 0x7f040225;
        public static final int layout_dodgeInsetEdges = 0x7f040226;
        public static final int layout_editor_absoluteX = 0x7f040227;
        public static final int layout_editor_absoluteY = 0x7f040228;
        public static final int layout_goneMarginBaseline = 0x7f040229;
        public static final int layout_goneMarginBottom = 0x7f04022a;
        public static final int layout_goneMarginEnd = 0x7f04022b;
        public static final int layout_goneMarginLeft = 0x7f04022c;
        public static final int layout_goneMarginRight = 0x7f04022d;
        public static final int layout_goneMarginStart = 0x7f04022e;
        public static final int layout_goneMarginTop = 0x7f04022f;
        public static final int layout_insetEdge = 0x7f040230;
        public static final int layout_keyline = 0x7f040231;
        public static final int layout_marginBaseline = 0x7f040232;
        public static final int layout_optimizationLevel = 0x7f040233;
        public static final int layout_scrollFlags = 0x7f040234;
        public static final int layout_scrollInterpolator = 0x7f040235;
        public static final int layout_wrapBehaviorInParent = 0x7f040236;
        public static final int liftOnScroll = 0x7f040237;
        public static final int liftOnScrollTargetViewId = 0x7f040238;
        public static final int limitBoundsTo = 0x7f040239;
        public static final int lineHeight = 0x7f04023a;
        public static final int lineSpacing = 0x7f04023b;
        public static final int listChoiceBackgroundIndicator = 0x7f04023c;
        public static final int listChoiceIndicatorMultipleAnimated = 0x7f04023d;
        public static final int listChoiceIndicatorSingleAnimated = 0x7f04023e;
        public static final int listDividerAlertDialog = 0x7f04023f;
        public static final int listItemLayout = 0x7f040240;
        public static final int listLayout = 0x7f040241;
        public static final int listMenuViewStyle = 0x7f040242;
        public static final int listPopupWindowStyle = 0x7f040243;
        public static final int listPreferredItemHeight = 0x7f040244;
        public static final int listPreferredItemHeightLarge = 0x7f040245;
        public static final int listPreferredItemHeightSmall = 0x7f040246;
        public static final int listPreferredItemPaddingEnd = 0x7f040247;
        public static final int listPreferredItemPaddingLeft = 0x7f040248;
        public static final int listPreferredItemPaddingRight = 0x7f040249;
        public static final int listPreferredItemPaddingStart = 0x7f04024a;
        public static final int logo = 0x7f04024b;
        public static final int logoDescription = 0x7f04024c;
        public static final int materialAlertDialogBodyTextStyle = 0x7f04024d;
        public static final int materialAlertDialogTheme = 0x7f04024e;
        public static final int materialAlertDialogTitleIconStyle = 0x7f04024f;
        public static final int materialAlertDialogTitlePanelStyle = 0x7f040250;
        public static final int materialAlertDialogTitleTextStyle = 0x7f040251;
        public static final int materialButtonOutlinedStyle = 0x7f040252;
        public static final int materialButtonStyle = 0x7f040253;
        public static final int materialButtonToggleGroupStyle = 0x7f040254;
        public static final int materialCalendarDay = 0x7f040255;
        public static final int materialCalendarFullscreenTheme = 0x7f040256;
        public static final int materialCalendarHeaderConfirmButton = 0x7f040257;
        public static final int materialCalendarHeaderDivider = 0x7f040258;
        public static final int materialCalendarHeaderLayout = 0x7f040259;
        public static final int materialCalendarHeaderSelection = 0x7f04025a;
        public static final int materialCalendarHeaderTitle = 0x7f04025b;
        public static final int materialCalendarHeaderToggleButton = 0x7f04025c;
        public static final int materialCalendarStyle = 0x7f04025d;
        public static final int materialCalendarTheme = 0x7f04025e;
        public static final int materialCardViewStyle = 0x7f04025f;
        public static final int materialThemeOverlay = 0x7f040260;
        public static final int maxAcceleration = 0x7f040261;
        public static final int maxActionInlineWidth = 0x7f040262;
        public static final int maxButtonHeight = 0x7f040263;
        public static final int maxCharacterCount = 0x7f040264;
        public static final int maxHeight = 0x7f040265;
        public static final int maxImageSize = 0x7f040266;
        public static final int maxLines = 0x7f040267;
        public static final int maxVelocity = 0x7f040268;
        public static final int maxWidth = 0x7f040269;
        public static final int measureWithLargestChild = 0x7f04026a;
        public static final int menu = 0x7f04026b;
        public static final int methodName = 0x7f04026c;
        public static final int min = 0x7f04026d;
        public static final int minHeight = 0x7f04026e;
        public static final int minTouchTargetSize = 0x7f04026f;
        public static final int minWidth = 0x7f040270;
        public static final int mock_diagonalsColor = 0x7f040271;
        public static final int mock_label = 0x7f040272;
        public static final int mock_labelBackgroundColor = 0x7f040273;
        public static final int mock_labelColor = 0x7f040274;
        public static final int mock_showDiagonals = 0x7f040275;
        public static final int mock_showLabel = 0x7f040276;
        public static final int motionDebug = 0x7f040277;
        public static final int motionEffect_alpha = 0x7f040278;
        public static final int motionEffect_end = 0x7f040279;
        public static final int motionEffect_move = 0x7f04027a;
        public static final int motionEffect_start = 0x7f04027b;
        public static final int motionEffect_strict = 0x7f04027c;
        public static final int motionEffect_translationX = 0x7f04027d;
        public static final int motionEffect_translationY = 0x7f04027e;
        public static final int motionEffect_viewTransition = 0x7f04027f;
        public static final int motionInterpolator = 0x7f040280;
        public static final int motionPathRotate = 0x7f040281;
        public static final int motionProgress = 0x7f040282;
        public static final int motionStagger = 0x7f040283;
        public static final int motionTarget = 0x7f040284;
        public static final int motion_postLayoutCollision = 0x7f040285;
        public static final int motion_triggerOnCollision = 0x7f040286;
        public static final int moveWhenScrollAtTop = 0x7f040287;
        public static final int mrl_rippleAlpha = 0x7f040288;
        public static final int mrl_rippleBackground = 0x7f040289;
        public static final int mrl_rippleColor = 0x7f04028a;
        public static final int mrl_rippleDelayClick = 0x7f04028b;
        public static final int mrl_rippleDimension = 0x7f04028c;
        public static final int mrl_rippleDuration = 0x7f04028d;
        public static final int mrl_rippleFadeDuration = 0x7f04028e;
        public static final int mrl_rippleHover = 0x7f04028f;
        public static final int mrl_rippleInAdapter = 0x7f040290;
        public static final int mrl_rippleOverlay = 0x7f040291;
        public static final int mrl_ripplePersistent = 0x7f040292;
        public static final int mrl_rippleRoundedCorners = 0x7f040293;
        public static final int multiChoiceItemLayout = 0x7f040294;
        public static final int navigationContentDescription = 0x7f040295;
        public static final int navigationIcon = 0x7f040296;
        public static final int navigationMode = 0x7f040297;
        public static final int navigationViewStyle = 0x7f040298;
        public static final int negativeButtonText = 0x7f040299;
        public static final int nestedScrollFlags = 0x7f04029a;
        public static final int nestedScrollViewStyle = 0x7f04029b;
        public static final int number = 0x7f04029c;
        public static final int numericModifiers = 0x7f04029d;
        public static final int onCross = 0x7f04029e;
        public static final int onHide = 0x7f04029f;
        public static final int onNegativeCross = 0x7f0402a0;
        public static final int onPositiveCross = 0x7f0402a1;
        public static final int onShow = 0x7f0402a2;
        public static final int onStateTransition = 0x7f0402a3;
        public static final int onTouchUp = 0x7f0402a4;
        public static final int order = 0x7f0402a5;
        public static final int orderingFromXml = 0x7f0402a6;
        public static final int overlapAnchor = 0x7f0402a7;
        public static final int overlay = 0x7f0402a8;
        public static final int paddingBottomNoButtons = 0x7f0402a9;
        public static final int paddingBottomSystemWindowInsets = 0x7f0402aa;
        public static final int paddingEnd = 0x7f0402ab;
        public static final int paddingLeftSystemWindowInsets = 0x7f0402ac;
        public static final int paddingRightSystemWindowInsets = 0x7f0402ad;
        public static final int paddingStart = 0x7f0402ae;
        public static final int paddingTopNoTitle = 0x7f0402af;
        public static final int panelBackground = 0x7f0402b0;
        public static final int panelMenuListTheme = 0x7f0402b1;
        public static final int panelMenuListWidth = 0x7f0402b2;
        public static final int passwordToggleContentDescription = 0x7f0402b3;
        public static final int passwordToggleDrawable = 0x7f0402b4;
        public static final int passwordToggleEnabled = 0x7f0402b5;
        public static final int passwordToggleTint = 0x7f0402b6;
        public static final int passwordToggleTintMode = 0x7f0402b7;
        public static final int pathMotionArc = 0x7f0402b8;
        public static final int path_percent = 0x7f0402b9;
        public static final int percentHeight = 0x7f0402ba;
        public static final int percentWidth = 0x7f0402bb;
        public static final int percentX = 0x7f0402bc;
        public static final int percentY = 0x7f0402bd;
        public static final int perpendicularPath_percent = 0x7f0402be;
        public static final int persistent = 0x7f0402bf;
        public static final int pivotAnchor = 0x7f0402c0;
        public static final int placeholderActivityName = 0x7f0402c1;
        public static final int placeholderText = 0x7f0402c2;
        public static final int placeholderTextAppearance = 0x7f0402c3;
        public static final int placeholderTextColor = 0x7f0402c4;
        public static final int placeholder_emptyVisibility = 0x7f0402c5;
        public static final int polarRelativeTo = 0x7f0402c6;
        public static final int popupMenuBackground = 0x7f0402c7;
        public static final int popupMenuStyle = 0x7f0402c8;
        public static final int popupTheme = 0x7f0402c9;
        public static final int popupWindowStyle = 0x7f0402ca;
        public static final int positiveButtonText = 0x7f0402cb;
        public static final int preferenceCategoryStyle = 0x7f0402cc;
        public static final int preferenceCategoryTitleTextAppearance = 0x7f0402cd;
        public static final int preferenceCategoryTitleTextColor = 0x7f0402ce;
        public static final int preferenceFragmentCompatStyle = 0x7f0402cf;
        public static final int preferenceFragmentListStyle = 0x7f0402d0;
        public static final int preferenceFragmentStyle = 0x7f0402d1;
        public static final int preferenceInformationStyle = 0x7f0402d2;
        public static final int preferenceScreenStyle = 0x7f0402d3;
        public static final int preferenceStyle = 0x7f0402d4;
        public static final int preferenceTheme = 0x7f0402d5;
        public static final int prefixText = 0x7f0402d6;
        public static final int prefixTextAppearance = 0x7f0402d7;
        public static final int prefixTextColor = 0x7f0402d8;
        public static final int preserveIconSpacing = 0x7f0402d9;
        public static final int pressedTranslationZ = 0x7f0402da;
        public static final int primaryActivityName = 0x7f0402db;
        public static final int progressBarPadding = 0x7f0402dc;
        public static final int progressBarStyle = 0x7f0402dd;
        public static final int quantizeMotionInterpolator = 0x7f0402de;
        public static final int quantizeMotionPhase = 0x7f0402df;
        public static final int quantizeMotionSteps = 0x7f0402e0;
        public static final int queryBackground = 0x7f0402e1;
        public static final int queryHint = 0x7f0402e2;
        public static final int queryPatterns = 0x7f0402e3;
        public static final int radioButtonStyle = 0x7f0402e4;
        public static final int rangeFillColor = 0x7f0402e5;
        public static final int ratingBarStyle = 0x7f0402e6;
        public static final int ratingBarStyleIndicator = 0x7f0402e7;
        public static final int ratingBarStyleSmall = 0x7f0402e8;
        public static final int reactiveGuide_animateChange = 0x7f0402e9;
        public static final int reactiveGuide_applyToAllConstraintSets = 0x7f0402ea;
        public static final int reactiveGuide_applyToConstraintSet = 0x7f0402eb;
        public static final int reactiveGuide_valueId = 0x7f0402ec;
        public static final int recyclerViewStyle = 0x7f0402ed;
        public static final int region_heightLessThan = 0x7f0402ee;
        public static final int region_heightMoreThan = 0x7f0402ef;
        public static final int region_widthLessThan = 0x7f0402f0;
        public static final int region_widthMoreThan = 0x7f0402f1;
        public static final int reverseLayout = 0x7f0402f2;
        public static final int rippleColor = 0x7f0402f3;
        public static final int rotationCenterId = 0x7f0402f4;
        public static final int round = 0x7f0402f5;
        public static final int roundPercent = 0x7f0402f6;
        public static final int saturation = 0x7f0402f7;
        public static final int sb_background = 0x7f0402f8;
        public static final int sb_border_width = 0x7f0402f9;
        public static final int sb_button_color = 0x7f0402fa;
        public static final int sb_checked = 0x7f0402fb;
        public static final int sb_checked_color = 0x7f0402fc;
        public static final int sb_checkline_color = 0x7f0402fd;
        public static final int sb_checkline_width = 0x7f0402fe;
        public static final int sb_effect_duration = 0x7f0402ff;
        public static final int sb_enable_effect = 0x7f040300;
        public static final int sb_shadow_color = 0x7f040301;
        public static final int sb_shadow_effect = 0x7f040302;
        public static final int sb_shadow_offset = 0x7f040303;
        public static final int sb_shadow_radius = 0x7f040304;
        public static final int sb_show_indicator = 0x7f040305;
        public static final int sb_uncheck_color = 0x7f040306;
        public static final int sb_uncheckcircle_color = 0x7f040307;
        public static final int sb_uncheckcircle_radius = 0x7f040308;
        public static final int sb_uncheckcircle_width = 0x7f040309;
        public static final int scaleFromTextSize = 0x7f04030a;
        public static final int scrimAnimationDuration = 0x7f04030b;
        public static final int scrimBackground = 0x7f04030c;
        public static final int scrimVisibleHeightTrigger = 0x7f04030d;
        public static final int searchHintIcon = 0x7f04030e;
        public static final int searchIcon = 0x7f04030f;
        public static final int searchViewStyle = 0x7f040310;
        public static final int secondaryActivityAction = 0x7f040311;
        public static final int secondaryActivityName = 0x7f040312;
        public static final int seekBarIncrement = 0x7f040313;
        public static final int seekBarPreferenceStyle = 0x7f040314;
        public static final int seekBarStyle = 0x7f040315;
        public static final int selectable = 0x7f040316;
        public static final int selectableItemBackground = 0x7f040317;
        public static final int selectableItemBackgroundBorderless = 0x7f040318;
        public static final int selectionRequired = 0x7f040319;
        public static final int setsTag = 0x7f04031a;
        public static final int shapeAppearance = 0x7f04031b;
        public static final int shapeAppearanceLargeComponent = 0x7f04031c;
        public static final int shapeAppearanceMediumComponent = 0x7f04031d;
        public static final int shapeAppearanceOverlay = 0x7f04031e;
        public static final int shapeAppearanceSmallComponent = 0x7f04031f;
        public static final int shortcutMatchRequired = 0x7f040320;
        public static final int shouldDisableView = 0x7f040321;
        public static final int showAsAction = 0x7f040322;
        public static final int showDividers = 0x7f040323;
        public static final int showMotionSpec = 0x7f040324;
        public static final int showPaths = 0x7f040325;
        public static final int showSeekBarValue = 0x7f040326;
        public static final int showText = 0x7f040327;
        public static final int showTitle = 0x7f040328;
        public static final int shrinkMotionSpec = 0x7f040329;
        public static final int singleChoiceItemLayout = 0x7f04032a;
        public static final int singleLine = 0x7f04032b;
        public static final int singleLineTitle = 0x7f04032c;
        public static final int singleSelection = 0x7f04032d;
        public static final int sizePercent = 0x7f04032e;
        public static final int sliderStyle = 0x7f04032f;
        public static final int snackbarButtonStyle = 0x7f040330;
        public static final int snackbarStyle = 0x7f040331;
        public static final int snackbarTextViewStyle = 0x7f040332;
        public static final int spanCount = 0x7f040333;
        public static final int spinBars = 0x7f040334;
        public static final int spinnerDropDownItemStyle = 0x7f040335;
        public static final int spinnerStyle = 0x7f040336;
        public static final int splitLayoutDirection = 0x7f040337;
        public static final int splitMinSmallestWidth = 0x7f040338;
        public static final int splitMinWidth = 0x7f040339;
        public static final int splitRatio = 0x7f04033a;
        public static final int splitTrack = 0x7f04033b;
        public static final int springBoundary = 0x7f04033c;
        public static final int springDamping = 0x7f04033d;
        public static final int springMass = 0x7f04033e;
        public static final int springStiffness = 0x7f04033f;
        public static final int springStopThreshold = 0x7f040340;
        public static final int srcCompat = 0x7f040341;
        public static final int stackFromEnd = 0x7f040342;
        public static final int staggered = 0x7f040343;
        public static final int startIconCheckable = 0x7f040344;
        public static final int startIconContentDescription = 0x7f040345;
        public static final int startIconDrawable = 0x7f040346;
        public static final int startIconTint = 0x7f040347;
        public static final int startIconTintMode = 0x7f040348;
        public static final int state_above_anchor = 0x7f040349;
        public static final int state_collapsed = 0x7f04034a;
        public static final int state_collapsible = 0x7f04034b;
        public static final int state_dragged = 0x7f04034c;
        public static final int state_liftable = 0x7f04034d;
        public static final int state_lifted = 0x7f04034e;
        public static final int statusBarBackground = 0x7f04034f;
        public static final int statusBarForeground = 0x7f040350;
        public static final int statusBarScrim = 0x7f040351;
        public static final int strokeColor = 0x7f040352;
        public static final int strokeWidth = 0x7f040353;
        public static final int subMenuArrow = 0x7f040354;
        public static final int submitBackground = 0x7f040355;
        public static final int subtitle = 0x7f040356;
        public static final int subtitleTextAppearance = 0x7f040357;
        public static final int subtitleTextColor = 0x7f040358;
        public static final int subtitleTextStyle = 0x7f040359;
        public static final int suffixText = 0x7f04035a;
        public static final int suffixTextAppearance = 0x7f04035b;
        public static final int suffixTextColor = 0x7f04035c;
        public static final int suggestionRowLayout = 0x7f04035d;
        public static final int summary = 0x7f04035e;
        public static final int summaryOff = 0x7f04035f;
        public static final int summaryOn = 0x7f040360;
        public static final int switchMinWidth = 0x7f040361;
        public static final int switchPadding = 0x7f040362;
        public static final int switchPreferenceCompatStyle = 0x7f040363;
        public static final int switchPreferenceStyle = 0x7f040364;
        public static final int switchStyle = 0x7f040365;
        public static final int switchTextAppearance = 0x7f040366;
        public static final int switchTextOff = 0x7f040367;
        public static final int switchTextOn = 0x7f040368;
        public static final int tabBackground = 0x7f040369;
        public static final int tabContentStart = 0x7f04036a;
        public static final int tabGravity = 0x7f04036b;
        public static final int tabIconTint = 0x7f04036c;
        public static final int tabIconTintMode = 0x7f04036d;
        public static final int tabIndicator = 0x7f04036e;
        public static final int tabIndicatorAnimationDuration = 0x7f04036f;
        public static final int tabIndicatorColor = 0x7f040370;
        public static final int tabIndicatorFullWidth = 0x7f040371;
        public static final int tabIndicatorGravity = 0x7f040372;
        public static final int tabIndicatorHeight = 0x7f040373;
        public static final int tabInlineLabel = 0x7f040374;
        public static final int tabMaxWidth = 0x7f040375;
        public static final int tabMinWidth = 0x7f040376;
        public static final int tabMode = 0x7f040377;
        public static final int tabPadding = 0x7f040378;
        public static final int tabPaddingBottom = 0x7f040379;
        public static final int tabPaddingEnd = 0x7f04037a;
        public static final int tabPaddingStart = 0x7f04037b;
        public static final int tabPaddingTop = 0x7f04037c;
        public static final int tabRippleColor = 0x7f04037d;
        public static final int tabSelectedTextColor = 0x7f04037e;
        public static final int tabStyle = 0x7f04037f;
        public static final int tabTextAppearance = 0x7f040380;
        public static final int tabTextColor = 0x7f040381;
        public static final int tabUnboundedRipple = 0x7f040382;
        public static final int targetId = 0x7f040383;
        public static final int telltales_tailColor = 0x7f040384;
        public static final int telltales_tailScale = 0x7f040385;
        public static final int telltales_velocityMode = 0x7f040386;
        public static final int textAllCaps = 0x7f040387;
        public static final int textAppearanceBody1 = 0x7f040388;
        public static final int textAppearanceBody2 = 0x7f040389;
        public static final int textAppearanceButton = 0x7f04038a;
        public static final int textAppearanceCaption = 0x7f04038b;
        public static final int textAppearanceHeadline1 = 0x7f04038c;
        public static final int textAppearanceHeadline2 = 0x7f04038d;
        public static final int textAppearanceHeadline3 = 0x7f04038e;
        public static final int textAppearanceHeadline4 = 0x7f04038f;
        public static final int textAppearanceHeadline5 = 0x7f040390;
        public static final int textAppearanceHeadline6 = 0x7f040391;
        public static final int textAppearanceLargePopupMenu = 0x7f040392;
        public static final int textAppearanceLineHeightEnabled = 0x7f040393;
        public static final int textAppearanceListItem = 0x7f040394;
        public static final int textAppearanceListItemSecondary = 0x7f040395;
        public static final int textAppearanceListItemSmall = 0x7f040396;
        public static final int textAppearanceOverline = 0x7f040397;
        public static final int textAppearancePopupMenuHeader = 0x7f040398;
        public static final int textAppearanceSearchResultSubtitle = 0x7f040399;
        public static final int textAppearanceSearchResultTitle = 0x7f04039a;
        public static final int textAppearanceSmallPopupMenu = 0x7f04039b;
        public static final int textAppearanceSubtitle1 = 0x7f04039c;
        public static final int textAppearanceSubtitle2 = 0x7f04039d;
        public static final int textBackground = 0x7f04039e;
        public static final int textBackgroundPanX = 0x7f04039f;
        public static final int textBackgroundPanY = 0x7f0403a0;
        public static final int textBackgroundRotate = 0x7f0403a1;
        public static final int textBackgroundZoom = 0x7f0403a2;
        public static final int textColorAlertDialogListItem = 0x7f0403a3;
        public static final int textColorSearchUrl = 0x7f0403a4;
        public static final int textEndPadding = 0x7f0403a5;
        public static final int textFillColor = 0x7f0403a6;
        public static final int textInputLayoutFocusedRectEnabled = 0x7f0403a7;
        public static final int textInputStyle = 0x7f0403a8;
        public static final int textLocale = 0x7f0403a9;
        public static final int textOutlineColor = 0x7f0403aa;
        public static final int textOutlineThickness = 0x7f0403ab;
        public static final int textPanX = 0x7f0403ac;
        public static final int textPanY = 0x7f0403ad;
        public static final int textStartPadding = 0x7f0403ae;
        public static final int textureBlurFactor = 0x7f0403af;
        public static final int textureEffect = 0x7f0403b0;
        public static final int textureHeight = 0x7f0403b1;
        public static final int textureWidth = 0x7f0403b2;
        public static final int theme = 0x7f0403b3;
        public static final int themeLineHeight = 0x7f0403b4;
        public static final int thickness = 0x7f0403b5;
        public static final int thumbColor = 0x7f0403b6;
        public static final int thumbElevation = 0x7f0403b7;
        public static final int thumbRadius = 0x7f0403b8;
        public static final int thumbTextPadding = 0x7f0403b9;
        public static final int thumbTint = 0x7f0403ba;
        public static final int thumbTintMode = 0x7f0403bb;
        public static final int tickColor = 0x7f0403bc;
        public static final int tickColorActive = 0x7f0403bd;
        public static final int tickColorInactive = 0x7f0403be;
        public static final int tickMark = 0x7f0403bf;
        public static final int tickMarkTint = 0x7f0403c0;
        public static final int tickMarkTintMode = 0x7f0403c1;
        public static final int tint = 0x7f0403c2;
        public static final int tintMode = 0x7f0403c3;
        public static final int title = 0x7f0403c4;
        public static final int titleEnabled = 0x7f0403c5;
        public static final int titleMargin = 0x7f0403c6;
        public static final int titleMarginBottom = 0x7f0403c7;
        public static final int titleMarginEnd = 0x7f0403c8;
        public static final int titleMarginStart = 0x7f0403c9;
        public static final int titleMarginTop = 0x7f0403ca;
        public static final int titleMargins = 0x7f0403cb;
        public static final int titleTextAppearance = 0x7f0403cc;
        public static final int titleTextColor = 0x7f0403cd;
        public static final int titleTextStyle = 0x7f0403ce;
        public static final int toolbarId = 0x7f0403cf;
        public static final int toolbarNavigationButtonStyle = 0x7f0403d0;
        public static final int toolbarStyle = 0x7f0403d1;
        public static final int tooltipForegroundColor = 0x7f0403d2;
        public static final int tooltipFrameBackground = 0x7f0403d3;
        public static final int tooltipStyle = 0x7f0403d4;
        public static final int tooltipText = 0x7f0403d5;
        public static final int touchAnchorId = 0x7f0403d6;
        public static final int touchAnchorSide = 0x7f0403d7;
        public static final int touchRegionId = 0x7f0403d8;
        public static final int track = 0x7f0403d9;
        public static final int trackColor = 0x7f0403da;
        public static final int trackColorActive = 0x7f0403db;
        public static final int trackColorInactive = 0x7f0403dc;
        public static final int trackHeight = 0x7f0403dd;
        public static final int trackTint = 0x7f0403de;
        public static final int trackTintMode = 0x7f0403df;
        public static final int transformPivotTarget = 0x7f0403e0;
        public static final int transitionDisable = 0x7f0403e1;
        public static final int transitionEasing = 0x7f0403e2;
        public static final int transitionFlags = 0x7f0403e3;
        public static final int transitionPathRotate = 0x7f0403e4;
        public static final int transitionShapeAppearance = 0x7f0403e5;
        public static final int triggerId = 0x7f0403e6;
        public static final int triggerReceiver = 0x7f0403e7;
        public static final int triggerSlack = 0x7f0403e8;
        public static final int ttcIndex = 0x7f0403e9;
        public static final int upDuration = 0x7f0403ea;
        public static final int updatesContinuously = 0x7f0403eb;
        public static final int useCompatPadding = 0x7f0403ec;
        public static final int useMaterialThemeColors = 0x7f0403ed;
        public static final int useSimpleSummaryProvider = 0x7f0403ee;
        public static final int values = 0x7f0403ef;
        public static final int verticalOffset = 0x7f0403f0;
        public static final int viewInflaterClass = 0x7f0403f1;
        public static final int viewTransitionMode = 0x7f0403f2;
        public static final int viewTransitionOnCross = 0x7f0403f3;
        public static final int viewTransitionOnNegativeCross = 0x7f0403f4;
        public static final int viewTransitionOnPositiveCross = 0x7f0403f5;
        public static final int visibilityMode = 0x7f0403f6;
        public static final int voiceIcon = 0x7f0403f7;
        public static final int warmth = 0x7f0403f8;
        public static final int waveDecay = 0x7f0403f9;
        public static final int waveOffset = 0x7f0403fa;
        public static final int wavePeriod = 0x7f0403fb;
        public static final int wavePhase = 0x7f0403fc;
        public static final int waveShape = 0x7f0403fd;
        public static final int waveVariesBy = 0x7f0403fe;
        public static final int widgetLayout = 0x7f0403ff;
        public static final int windowActionBar = 0x7f040400;
        public static final int windowActionBarOverlay = 0x7f040401;
        public static final int windowActionModeOverlay = 0x7f040402;
        public static final int windowFixedHeightMajor = 0x7f040403;
        public static final int windowFixedHeightMinor = 0x7f040404;
        public static final int windowFixedWidthMajor = 0x7f040405;
        public static final int windowFixedWidthMinor = 0x7f040406;
        public static final int windowMinWidthMajor = 0x7f040407;
        public static final int windowMinWidthMinor = 0x7f040408;
        public static final int windowNoTitle = 0x7f040409;
        public static final int yearSelectedStyle = 0x7f04040a;
        public static final int yearStyle = 0x7f04040b;
        public static final int yearTodayStyle = 0x7f04040c;
    }

    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 0x7f050000;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f050001;
        public static final int config_materialPreferenceIconSpaceReserved = 0x7f050002;
        public static final int mtrl_btn_textappearance_all_caps = 0x7f050003;
    }

    public static final class color {
        public static final int abc_background_cache_hint_selector_material_dark = 0x7f060000;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f060001;
        public static final int abc_btn_colored_borderless_text_material = 0x7f060002;
        public static final int abc_btn_colored_text_material = 0x7f060003;
        public static final int abc_color_highlight_material = 0x7f060004;
        public static final int abc_decor_view_status_guard = 0x7f060005;
        public static final int abc_decor_view_status_guard_light = 0x7f060006;
        public static final int abc_hint_foreground_material_dark = 0x7f060007;
        public static final int abc_hint_foreground_material_light = 0x7f060008;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f060009;
        public static final int abc_primary_text_disable_only_material_light = 0x7f06000a;
        public static final int abc_primary_text_material_dark = 0x7f06000b;
        public static final int abc_primary_text_material_light = 0x7f06000c;
        public static final int abc_search_url_text = 0x7f06000d;
        public static final int abc_search_url_text_normal = 0x7f06000e;
        public static final int abc_search_url_text_pressed = 0x7f06000f;
        public static final int abc_search_url_text_selected = 0x7f060010;
        public static final int abc_secondary_text_material_dark = 0x7f060011;
        public static final int abc_secondary_text_material_light = 0x7f060012;
        public static final int abc_tint_btn_checkable = 0x7f060013;
        public static final int abc_tint_default = 0x7f060014;
        public static final int abc_tint_edittext = 0x7f060015;
        public static final int abc_tint_seek_thumb = 0x7f060016;
        public static final int abc_tint_spinner = 0x7f060017;
        public static final int abc_tint_switch_track = 0x7f060018;
        public static final int accent_material_dark = 0x7f060019;
        public static final int accent_material_light = 0x7f06001a;
        public static final int aliceblue = 0x7f06001b;
        public static final int androidx_core_ripple_material_light = 0x7f06001c;
        public static final int androidx_core_secondary_text_default_material_light = 0x7f06001d;
        public static final int antiquewhite = 0x7f06001e;
        public static final int aqua = 0x7f06001f;
        public static final int aquamarine = 0x7f060020;
        public static final int azure = 0x7f060021;
        public static final int background_floating_material_dark = 0x7f060022;
        public static final int background_floating_material_light = 0x7f060023;
        public static final int background_material_dark = 0x7f060024;
        public static final int background_material_light = 0x7f060025;
        public static final int beige = 0x7f060026;
        public static final int bisque = 0x7f060027;
        public static final int black = 0x7f060028;
        public static final int black_semi_transparent = 0x7f060029;
        public static final int blanchedalmond = 0x7f06002a;
        public static final int blue = 0x7f06002b;
        public static final int blue_semi_transparent = 0x7f06002c;
        public static final int blue_semi_transparent_pressed = 0x7f06002d;
        public static final int blueviolet = 0x7f06002e;
        public static final int bright_foreground_disabled_material_dark = 0x7f06002f;
        public static final int bright_foreground_disabled_material_light = 0x7f060030;
        public static final int bright_foreground_inverse_material_dark = 0x7f060031;
        public static final int bright_foreground_inverse_material_light = 0x7f060032;
        public static final int bright_foreground_material_dark = 0x7f060033;
        public static final int bright_foreground_material_light = 0x7f060034;
        public static final int brown = 0x7f060035;
        public static final int burlywood = 0x7f060036;
        public static final int button_material_dark = 0x7f060037;
        public static final int button_material_light = 0x7f060038;
        public static final int cadetblue = 0x7f060039;
        public static final int cardview_dark_background = 0x7f06003a;
        public static final int cardview_light_background = 0x7f06003b;
        public static final int cardview_shadow_end_color = 0x7f06003c;
        public static final int cardview_shadow_start_color = 0x7f06003d;
        public static final int chartreuse = 0x7f06003e;
        public static final int checkbox_themeable_attribute_color = 0x7f06003f;
        public static final int chocolate = 0x7f060040;
        public static final int colorAccent = 0x7f060041;
        public static final int colorPrimary = 0x7f060042;
        public static final int colorPrimaryDark = 0x7f060043;
        public static final int coral = 0x7f060044;
        public static final int cornflowerblue = 0x7f060045;
        public static final int cornsilk = 0x7f060046;
        public static final int crimson = 0x7f060047;
        public static final int cyan = 0x7f060048;
        public static final int darkblue = 0x7f060049;
        public static final int darkcyan = 0x7f06004a;
        public static final int darkgoldenrod = 0x7f06004b;
        public static final int darkgray = 0x7f06004c;
        public static final int darkgreen = 0x7f06004d;
        public static final int darkgrey = 0x7f06004e;
        public static final int darkkhaki = 0x7f06004f;
        public static final int darkmagenta = 0x7f060050;
        public static final int darkolivegreen = 0x7f060051;
        public static final int darkorange = 0x7f060052;
        public static final int darkorchid = 0x7f060053;
        public static final int darkred = 0x7f060054;
        public static final int darksalmon = 0x7f060055;
        public static final int darkseagreen = 0x7f060056;
        public static final int darkslateblue = 0x7f060057;
        public static final int darkslategray = 0x7f060058;
        public static final int darkslategrey = 0x7f060059;
        public static final int darkturquoise = 0x7f06005a;
        public static final int darkviolet = 0x7f06005b;
        public static final int deeppink = 0x7f06005c;
        public static final int deepskyblue = 0x7f06005d;
        public static final int design_bottom_navigation_shadow_color = 0x7f06005e;
        public static final int design_box_stroke_color = 0x7f06005f;
        public static final int design_dark_default_color_background = 0x7f060060;
        public static final int design_dark_default_color_error = 0x7f060061;
        public static final int design_dark_default_color_on_background = 0x7f060062;
        public static final int design_dark_default_color_on_error = 0x7f060063;
        public static final int design_dark_default_color_on_primary = 0x7f060064;
        public static final int design_dark_default_color_on_secondary = 0x7f060065;
        public static final int design_dark_default_color_on_surface = 0x7f060066;
        public static final int design_dark_default_color_primary = 0x7f060067;
        public static final int design_dark_default_color_primary_dark = 0x7f060068;
        public static final int design_dark_default_color_primary_variant = 0x7f060069;
        public static final int design_dark_default_color_secondary = 0x7f06006a;
        public static final int design_dark_default_color_secondary_variant = 0x7f06006b;
        public static final int design_dark_default_color_surface = 0x7f06006c;
        public static final int design_default_color_background = 0x7f06006d;
        public static final int design_default_color_error = 0x7f06006e;
        public static final int design_default_color_on_background = 0x7f06006f;
        public static final int design_default_color_on_error = 0x7f060070;
        public static final int design_default_color_on_primary = 0x7f060071;
        public static final int design_default_color_on_secondary = 0x7f060072;
        public static final int design_default_color_on_surface = 0x7f060073;
        public static final int design_default_color_primary = 0x7f060074;
        public static final int design_default_color_primary_dark = 0x7f060075;
        public static final int design_default_color_primary_variant = 0x7f060076;
        public static final int design_default_color_secondary = 0x7f060077;
        public static final int design_default_color_secondary_variant = 0x7f060078;
        public static final int design_default_color_surface = 0x7f060079;
        public static final int design_error = 0x7f06007a;
        public static final int design_fab_shadow_end_color = 0x7f06007b;
        public static final int design_fab_shadow_mid_color = 0x7f06007c;
        public static final int design_fab_shadow_start_color = 0x7f06007d;
        public static final int design_fab_stroke_end_inner_color = 0x7f06007e;
        public static final int design_fab_stroke_end_outer_color = 0x7f06007f;
        public static final int design_fab_stroke_top_inner_color = 0x7f060080;
        public static final int design_fab_stroke_top_outer_color = 0x7f060081;
        public static final int design_icon_tint = 0x7f060082;
        public static final int design_snackbar_background_color = 0x7f060083;
        public static final int dim_foreground_disabled_material_dark = 0x7f060084;
        public static final int dim_foreground_disabled_material_light = 0x7f060085;
        public static final int dim_foreground_material_dark = 0x7f060086;
        public static final int dim_foreground_material_light = 0x7f060087;
        public static final int dimgray = 0x7f060088;
        public static final int dimgrey = 0x7f060089;
        public static final int dodgerblue = 0x7f06008a;
        public static final int eggplant = 0x7f06008b;
        public static final int error_color_material_dark = 0x7f06008c;
        public static final int error_color_material_light = 0x7f06008d;
        public static final int firebrick = 0x7f06008e;
        public static final int floralwhite = 0x7f06008f;
        public static final int foreground_material_dark = 0x7f060090;
        public static final int foreground_material_light = 0x7f060091;
        public static final int forestgreen = 0x7f060092;
        public static final int fuchsia = 0x7f060093;
        public static final int gainsboro = 0x7f060094;
        public static final int ghostwhite = 0x7f060095;
        public static final int gold = 0x7f060096;
        public static final int goldenrod = 0x7f060097;
        public static final int gray = 0x7f060098;
        public static final int green = 0x7f060099;
        public static final int greenyellow = 0x7f06009a;
        public static final int grey = 0x7f06009b;
        public static final int half_black = 0x7f06009c;
        public static final int highlighted_text_material_dark = 0x7f06009d;
        public static final int highlighted_text_material_light = 0x7f06009e;
        public static final int honeydew = 0x7f06009f;
        public static final int hotpink = 0x7f0600a0;
        public static final int indianred = 0x7f0600a1;
        public static final int indigo = 0x7f0600a2;
        public static final int ivory = 0x7f0600a3;
        public static final int khaki = 0x7f0600a4;
        public static final int lavender = 0x7f0600a5;
        public static final int lavenderblush = 0x7f0600a6;
        public static final int lawngreen = 0x7f0600a7;
        public static final int lemonchiffon = 0x7f0600a8;
        public static final int lightblue = 0x7f0600a9;
        public static final int lightcoral = 0x7f0600aa;
        public static final int lightcyan = 0x7f0600ab;
        public static final int lightgoldenrodyellow = 0x7f0600ac;
        public static final int lightgray = 0x7f0600ad;
        public static final int lightgreen = 0x7f0600ae;
        public static final int lightgrey = 0x7f0600af;
        public static final int lightpink = 0x7f0600b0;
        public static final int lightsalmon = 0x7f0600b1;
        public static final int lightseagreen = 0x7f0600b2;
        public static final int lightskyblue = 0x7f0600b3;
        public static final int lightslategray = 0x7f0600b4;
        public static final int lightslategrey = 0x7f0600b5;
        public static final int lightsteelblue = 0x7f0600b6;
        public static final int lightyellow = 0x7f0600b7;
        public static final int lime = 0x7f0600b8;
        public static final int limegreen = 0x7f0600b9;
        public static final int linen = 0x7f0600ba;
        public static final int magenta = 0x7f0600bb;
        public static final int maroon = 0x7f0600bc;
        public static final int material_blue_grey_800 = 0x7f0600bd;
        public static final int material_blue_grey_900 = 0x7f0600be;
        public static final int material_blue_grey_950 = 0x7f0600bf;
        public static final int material_deep_teal_200 = 0x7f0600c0;
        public static final int material_deep_teal_500 = 0x7f0600c1;
        public static final int material_grey_100 = 0x7f0600c2;
        public static final int material_grey_300 = 0x7f0600c3;
        public static final int material_grey_50 = 0x7f0600c4;
        public static final int material_grey_600 = 0x7f0600c5;
        public static final int material_grey_800 = 0x7f0600c6;
        public static final int material_grey_850 = 0x7f0600c7;
        public static final int material_grey_900 = 0x7f0600c8;
        public static final int material_on_background_disabled = 0x7f0600c9;
        public static final int material_on_background_emphasis_high_type = 0x7f0600ca;
        public static final int material_on_background_emphasis_medium = 0x7f0600cb;
        public static final int material_on_primary_disabled = 0x7f0600cc;
        public static final int material_on_primary_emphasis_high_type = 0x7f0600cd;
        public static final int material_on_primary_emphasis_medium = 0x7f0600ce;
        public static final int material_on_surface_disabled = 0x7f0600cf;
        public static final int material_on_surface_emphasis_high_type = 0x7f0600d0;
        public static final int material_on_surface_emphasis_medium = 0x7f0600d1;
        public static final int material_on_surface_stroke = 0x7f0600d2;
        public static final int material_slider_active_tick_marks_color = 0x7f0600d3;
        public static final int material_slider_active_track_color = 0x7f0600d4;
        public static final int material_slider_halo_color = 0x7f0600d5;
        public static final int material_slider_inactive_tick_marks_color = 0x7f0600d6;
        public static final int material_slider_inactive_track_color = 0x7f0600d7;
        public static final int material_slider_thumb_color = 0x7f0600d8;
        public static final int mediumaquamarine = 0x7f0600d9;
        public static final int mediumblue = 0x7f0600da;
        public static final int mediumorchid = 0x7f0600db;
        public static final int mediumpurple = 0x7f0600dc;
        public static final int mediumseagreen = 0x7f0600dd;
        public static final int mediumslateblue = 0x7f0600de;
        public static final int mediumspringgreen = 0x7f0600df;
        public static final int mediumturquoise = 0x7f0600e0;
        public static final int mediumvioletred = 0x7f0600e1;
        public static final int midnightblue = 0x7f0600e2;
        public static final int mintcream = 0x7f0600e3;
        public static final int mistyrose = 0x7f0600e4;
        public static final int moccasin = 0x7f0600e5;
        public static final int mtrl_bottom_nav_colored_item_tint = 0x7f0600e6;
        public static final int mtrl_bottom_nav_colored_ripple_color = 0x7f0600e7;
        public static final int mtrl_bottom_nav_item_tint = 0x7f0600e8;
        public static final int mtrl_bottom_nav_ripple_color = 0x7f0600e9;
        public static final int mtrl_btn_bg_color_selector = 0x7f0600ea;
        public static final int mtrl_btn_ripple_color = 0x7f0600eb;
        public static final int mtrl_btn_stroke_color_selector = 0x7f0600ec;
        public static final int mtrl_btn_text_btn_bg_color_selector = 0x7f0600ed;
        public static final int mtrl_btn_text_btn_ripple_color = 0x7f0600ee;
        public static final int mtrl_btn_text_color_disabled = 0x7f0600ef;
        public static final int mtrl_btn_text_color_selector = 0x7f0600f0;
        public static final int mtrl_btn_transparent_bg_color = 0x7f0600f1;
        public static final int mtrl_calendar_item_stroke_color = 0x7f0600f2;
        public static final int mtrl_calendar_selected_range = 0x7f0600f3;
        public static final int mtrl_card_view_foreground = 0x7f0600f4;
        public static final int mtrl_card_view_ripple = 0x7f0600f5;
        public static final int mtrl_chip_background_color = 0x7f0600f6;
        public static final int mtrl_chip_close_icon_tint = 0x7f0600f7;
        public static final int mtrl_chip_ripple_color = 0x7f0600f8;
        public static final int mtrl_chip_surface_color = 0x7f0600f9;
        public static final int mtrl_chip_text_color = 0x7f0600fa;
        public static final int mtrl_choice_chip_background_color = 0x7f0600fb;
        public static final int mtrl_choice_chip_ripple_color = 0x7f0600fc;
        public static final int mtrl_choice_chip_text_color = 0x7f0600fd;
        public static final int mtrl_error = 0x7f0600fe;
        public static final int mtrl_fab_bg_color_selector = 0x7f0600ff;
        public static final int mtrl_fab_icon_text_color_selector = 0x7f060100;
        public static final int mtrl_fab_ripple_color = 0x7f060101;
        public static final int mtrl_filled_background_color = 0x7f060102;
        public static final int mtrl_filled_icon_tint = 0x7f060103;
        public static final int mtrl_filled_stroke_color = 0x7f060104;
        public static final int mtrl_indicator_text_color = 0x7f060105;
        public static final int mtrl_navigation_item_background_color = 0x7f060106;
        public static final int mtrl_navigation_item_icon_tint = 0x7f060107;
        public static final int mtrl_navigation_item_text_color = 0x7f060108;
        public static final int mtrl_on_primary_text_btn_text_color_selector = 0x7f060109;
        public static final int mtrl_outlined_icon_tint = 0x7f06010a;
        public static final int mtrl_outlined_stroke_color = 0x7f06010b;
        public static final int mtrl_popupmenu_overlay_color = 0x7f06010c;
        public static final int mtrl_scrim_color = 0x7f06010d;
        public static final int mtrl_tabs_colored_ripple_color = 0x7f06010e;
        public static final int mtrl_tabs_icon_color_selector = 0x7f06010f;
        public static final int mtrl_tabs_icon_color_selector_colored = 0x7f060110;
        public static final int mtrl_tabs_legacy_text_color_selector = 0x7f060111;
        public static final int mtrl_tabs_ripple_color = 0x7f060112;
        public static final int mtrl_text_btn_text_color_selector = 0x7f060113;
        public static final int mtrl_textinput_default_box_stroke_color = 0x7f060114;
        public static final int mtrl_textinput_disabled_color = 0x7f060115;
        public static final int mtrl_textinput_filled_box_default_background_color = 0x7f060116;
        public static final int mtrl_textinput_focused_box_stroke_color = 0x7f060117;
        public static final int mtrl_textinput_hovered_box_stroke_color = 0x7f060118;
        public static final int navajowhite = 0x7f060119;
        public static final int navy = 0x7f06011a;
        public static final int notification_action_color_filter = 0x7f06011b;
        public static final int notification_icon_bg_color = 0x7f06011c;
        public static final int oldlace = 0x7f06011d;
        public static final int olive = 0x7f06011e;
        public static final int olivedrab = 0x7f06011f;
        public static final int orange = 0x7f060120;
        public static final int orangered = 0x7f060121;
        public static final int orchid = 0x7f060122;
        public static final int palegoldenrod = 0x7f060123;
        public static final int palegreen = 0x7f060124;
        public static final int paleturquoise = 0x7f060125;
        public static final int palevioletred = 0x7f060126;
        public static final int papayawhip = 0x7f060127;
        public static final int peachpuff = 0x7f060128;
        public static final int peru = 0x7f060129;
        public static final int pink = 0x7f06012a;
        public static final int plum = 0x7f06012b;
        public static final int powderblue = 0x7f06012c;
        public static final int preference_fallback_accent_color = 0x7f06012d;
        public static final int primary_dark_material_dark = 0x7f06012e;
        public static final int primary_dark_material_light = 0x7f06012f;
        public static final int primary_material_dark = 0x7f060130;
        public static final int primary_material_light = 0x7f060131;
        public static final int primary_text_default_material_dark = 0x7f060132;
        public static final int primary_text_default_material_light = 0x7f060133;
        public static final int primary_text_disabled_material_dark = 0x7f060134;
        public static final int primary_text_disabled_material_light = 0x7f060135;
        public static final int purple = 0x7f060136;
        public static final int radiobutton_themeable_attribute_color = 0x7f060137;
        public static final int red = 0x7f060138;
        public static final int ripple_material_dark = 0x7f060139;
        public static final int ripple_material_light = 0x7f06013a;
        public static final int rosybrown = 0x7f06013b;
        public static final int royalblue = 0x7f06013c;
        public static final int saddlebrown = 0x7f06013d;
        public static final int saffron = 0x7f06013e;
        public static final int salmon = 0x7f06013f;
        public static final int sandybrown = 0x7f060140;
        public static final int seaShell = 0x7f060141;
        public static final int seagreen = 0x7f060142;
        public static final int secondary_text_default_material_dark = 0x7f060143;
        public static final int secondary_text_default_material_light = 0x7f060144;
        public static final int secondary_text_disabled_material_dark = 0x7f060145;
        public static final int secondary_text_disabled_material_light = 0x7f060146;
        public static final int sienna = 0x7f060147;
        public static final int silver = 0x7f060148;
        public static final int skyblue = 0x7f060149;
        public static final int slateblue = 0x7f06014a;
        public static final int slategray = 0x7f06014b;
        public static final int slategrey = 0x7f06014c;
        public static final int snow = 0x7f06014d;
        public static final int springgreen = 0x7f06014e;
        public static final int steelblue = 0x7f06014f;
        public static final int switch_thumb_disabled_material_dark = 0x7f060150;
        public static final int switch_thumb_disabled_material_light = 0x7f060151;
        public static final int switch_thumb_material_dark = 0x7f060152;
        public static final int switch_thumb_material_light = 0x7f060153;
        public static final int switch_thumb_normal_material_dark = 0x7f060154;
        public static final int switch_thumb_normal_material_light = 0x7f060155;
        public static final int tan = 0x7f060156;
        public static final int teal = 0x7f060157;
        public static final int test_mtrl_calendar_day = 0x7f060158;
        public static final int test_mtrl_calendar_day_selected = 0x7f060159;
        public static final int thistle = 0x7f06015a;
        public static final int tomato = 0x7f06015b;
        public static final int tooltip_background_dark = 0x7f06015c;
        public static final int tooltip_background_light = 0x7f06015d;
        public static final int turquoise = 0x7f06015e;
        public static final int violet = 0x7f06015f;
        public static final int wheat = 0x7f060160;
        public static final int white = 0x7f060161;
        public static final int white_pressed = 0x7f060162;
        public static final int whitesmoke = 0x7f060163;
        public static final int yellow = 0x7f060164;
    }

    public static final class dimen {
        public static final int abc_action_bar_content_inset_material = 0x7f070000;
        public static final int abc_action_bar_content_inset_with_nav = 0x7f070001;
        public static final int abc_action_bar_default_height_material = 0x7f070002;
        public static final int abc_action_bar_default_padding_end_material = 0x7f070003;
        public static final int abc_action_bar_default_padding_start_material = 0x7f070004;
        public static final int abc_action_bar_elevation_material = 0x7f070005;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f070006;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f070007;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f070008;
        public static final int abc_action_bar_stacked_max_height = 0x7f070009;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f07000a;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f07000c;
        public static final int abc_action_button_min_height_material = 0x7f07000d;
        public static final int abc_action_button_min_width_material = 0x7f07000e;
        public static final int abc_action_button_min_width_overflow_material = 0x7f07000f;
        public static final int abc_alert_dialog_button_bar_height = 0x7f070010;
        public static final int abc_alert_dialog_button_dimen = 0x7f070011;
        public static final int abc_button_inset_horizontal_material = 0x7f070012;
        public static final int abc_button_inset_vertical_material = 0x7f070013;
        public static final int abc_button_padding_horizontal_material = 0x7f070014;
        public static final int abc_button_padding_vertical_material = 0x7f070015;
        public static final int abc_cascading_menus_min_smallest_width = 0x7f070016;
        public static final int abc_config_prefDialogWidth = 0x7f070017;
        public static final int abc_control_corner_material = 0x7f070018;
        public static final int abc_control_inset_material = 0x7f070019;
        public static final int abc_control_padding_material = 0x7f07001a;
        public static final int abc_dialog_corner_radius_material = 0x7f07001b;
        public static final int abc_dialog_fixed_height_major = 0x7f07001c;
        public static final int abc_dialog_fixed_height_minor = 0x7f07001d;
        public static final int abc_dialog_fixed_width_major = 0x7f07001e;
        public static final int abc_dialog_fixed_width_minor = 0x7f07001f;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f070020;
        public static final int abc_dialog_list_padding_top_no_title = 0x7f070021;
        public static final int abc_dialog_min_width_major = 0x7f070022;
        public static final int abc_dialog_min_width_minor = 0x7f070023;
        public static final int abc_dialog_padding_material = 0x7f070024;
        public static final int abc_dialog_padding_top_material = 0x7f070025;
        public static final int abc_dialog_title_divider_material = 0x7f070026;
        public static final int abc_disabled_alpha_material_dark = 0x7f070027;
        public static final int abc_disabled_alpha_material_light = 0x7f070028;
        public static final int abc_dropdownitem_icon_width = 0x7f070029;
        public static final int abc_dropdownitem_text_padding_left = 0x7f07002a;
        public static final int abc_dropdownitem_text_padding_right = 0x7f07002b;
        public static final int abc_edit_text_inset_bottom_material = 0x7f07002c;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f07002d;
        public static final int abc_edit_text_inset_top_material = 0x7f07002e;
        public static final int abc_floating_window_z = 0x7f07002f;
        public static final int abc_list_item_height_large_material = 0x7f070030;
        public static final int abc_list_item_height_material = 0x7f070031;
        public static final int abc_list_item_height_small_material = 0x7f070032;
        public static final int abc_list_item_padding_horizontal_material = 0x7f070033;
        public static final int abc_panel_menu_list_width = 0x7f070034;
        public static final int abc_progress_bar_height_material = 0x7f070035;
        public static final int abc_search_view_preferred_height = 0x7f070036;
        public static final int abc_search_view_preferred_width = 0x7f070037;
        public static final int abc_seekbar_track_background_height_material = 0x7f070038;
        public static final int abc_seekbar_track_progress_height_material = 0x7f070039;
        public static final int abc_select_dialog_padding_start_material = 0x7f07003a;
        public static final int abc_star_big = 0x7f07003b;
        public static final int abc_star_medium = 0x7f07003c;
        public static final int abc_star_small = 0x7f07003d;
        public static final int abc_switch_padding = 0x7f07003e;
        public static final int abc_text_size_body_1_material = 0x7f07003f;
        public static final int abc_text_size_body_2_material = 0x7f070040;
        public static final int abc_text_size_button_material = 0x7f070041;
        public static final int abc_text_size_caption_material = 0x7f070042;
        public static final int abc_text_size_display_1_material = 0x7f070043;
        public static final int abc_text_size_display_2_material = 0x7f070044;
        public static final int abc_text_size_display_3_material = 0x7f070045;
        public static final int abc_text_size_display_4_material = 0x7f070046;
        public static final int abc_text_size_headline_material = 0x7f070047;
        public static final int abc_text_size_large_material = 0x7f070048;
        public static final int abc_text_size_medium_material = 0x7f070049;
        public static final int abc_text_size_menu_header_material = 0x7f07004a;
        public static final int abc_text_size_menu_material = 0x7f07004b;
        public static final int abc_text_size_small_material = 0x7f07004c;
        public static final int abc_text_size_subhead_material = 0x7f07004d;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f07004e;
        public static final int abc_text_size_title_material = 0x7f07004f;
        public static final int abc_text_size_title_material_toolbar = 0x7f070050;
        public static final int action_bar_size = 0x7f070051;
        public static final int activity_horizontal_margin = 0x7f070052;
        public static final int activity_vertical_margin = 0x7f070053;
        public static final int appcompat_dialog_background_inset = 0x7f070054;
        public static final int cardview_compat_inset_shadow = 0x7f070055;
        public static final int cardview_default_elevation = 0x7f070056;
        public static final int cardview_default_radius = 0x7f070057;
        public static final int compat_button_inset_horizontal_material = 0x7f070058;
        public static final int compat_button_inset_vertical_material = 0x7f070059;
        public static final int compat_button_padding_horizontal_material = 0x7f07005a;
        public static final int compat_button_padding_vertical_material = 0x7f07005b;
        public static final int compat_control_corner_material = 0x7f07005c;
        public static final int compat_notification_large_icon_max_height = 0x7f07005d;
        public static final int compat_notification_large_icon_max_width = 0x7f07005e;
        public static final int default_dimension = 0x7f07005f;
        public static final int design_appbar_elevation = 0x7f070060;
        public static final int design_bottom_navigation_active_item_max_width = 0x7f070061;
        public static final int design_bottom_navigation_active_item_min_width = 0x7f070062;
        public static final int design_bottom_navigation_active_text_size = 0x7f070063;
        public static final int design_bottom_navigation_elevation = 0x7f070064;
        public static final int design_bottom_navigation_height = 0x7f070065;
        public static final int design_bottom_navigation_icon_size = 0x7f070066;
        public static final int design_bottom_navigation_item_max_width = 0x7f070067;
        public static final int design_bottom_navigation_item_min_width = 0x7f070068;
        public static final int design_bottom_navigation_margin = 0x7f070069;
        public static final int design_bottom_navigation_shadow_height = 0x7f07006a;
        public static final int design_bottom_navigation_text_size = 0x7f07006b;
        public static final int design_bottom_sheet_elevation = 0x7f07006c;
        public static final int design_bottom_sheet_modal_elevation = 0x7f07006d;
        public static final int design_bottom_sheet_peek_height_min = 0x7f07006e;
        public static final int design_fab_border_width = 0x7f07006f;
        public static final int design_fab_elevation = 0x7f070070;
        public static final int design_fab_image_size = 0x7f070071;
        public static final int design_fab_size_mini = 0x7f070072;
        public static final int design_fab_size_normal = 0x7f070073;
        public static final int design_fab_translation_z_hovered_focused = 0x7f070074;
        public static final int design_fab_translation_z_pressed = 0x7f070075;
        public static final int design_navigation_elevation = 0x7f070076;
        public static final int design_navigation_icon_padding = 0x7f070077;
        public static final int design_navigation_icon_size = 0x7f070078;
        public static final int design_navigation_item_horizontal_padding = 0x7f070079;
        public static final int design_navigation_item_icon_padding = 0x7f07007a;
        public static final int design_navigation_max_width = 0x7f07007b;
        public static final int design_navigation_padding_bottom = 0x7f07007c;
        public static final int design_navigation_separator_vertical_padding = 0x7f07007d;
        public static final int design_snackbar_action_inline_max_width = 0x7f07007e;
        public static final int design_snackbar_action_text_color_alpha = 0x7f07007f;
        public static final int design_snackbar_background_corner_radius = 0x7f070080;
        public static final int design_snackbar_elevation = 0x7f070081;
        public static final int design_snackbar_extra_spacing_horizontal = 0x7f070082;
        public static final int design_snackbar_max_width = 0x7f070083;
        public static final int design_snackbar_min_width = 0x7f070084;
        public static final int design_snackbar_padding_horizontal = 0x7f070085;
        public static final int design_snackbar_padding_vertical = 0x7f070086;
        public static final int design_snackbar_padding_vertical_2lines = 0x7f070087;
        public static final int design_snackbar_text_size = 0x7f070088;
        public static final int design_tab_max_width = 0x7f070089;
        public static final int design_tab_scrollable_min_width = 0x7f07008a;
        public static final int design_tab_text_size = 0x7f07008b;
        public static final int design_tab_text_size_2line = 0x7f07008c;
        public static final int design_textinput_caption_translate_y = 0x7f07008d;
        public static final int disabled_alpha_material_dark = 0x7f07008e;
        public static final int disabled_alpha_material_light = 0x7f07008f;
        public static final int fastscroll_default_thickness = 0x7f070090;
        public static final int fastscroll_margin = 0x7f070091;
        public static final int fastscroll_minimum_range = 0x7f070092;
        public static final int highlight_alpha_material_colored = 0x7f070093;
        public static final int highlight_alpha_material_dark = 0x7f070094;
        public static final int highlight_alpha_material_light = 0x7f070095;
        public static final int hint_alpha_material_dark = 0x7f070096;
        public static final int hint_alpha_material_light = 0x7f070097;
        public static final int hint_pressed_alpha_material_dark = 0x7f070098;
        public static final int hint_pressed_alpha_material_light = 0x7f070099;
        public static final int item_touch_helper_max_drag_scroll_per_frame = 0x7f07009a;
        public static final int item_touch_helper_swipe_escape_max_velocity = 0x7f07009b;
        public static final int item_touch_helper_swipe_escape_velocity = 0x7f07009c;
        public static final int joystick_btn_height = 0x7f07009d;
        public static final int joystick_btn_width = 0x7f07009e;
        public static final int joystick_ctrl_btn_height = 0x7f07009f;
        public static final int joystick_ctrl_btn_width = 0x7f0700a0;
        public static final int joystick_rockerview_height = 0x7f0700a1;
        public static final int joystick_rockerview_width = 0x7f0700a2;
        public static final int material_emphasis_disabled = 0x7f0700a3;
        public static final int material_emphasis_high_type = 0x7f0700a4;
        public static final int material_emphasis_medium = 0x7f0700a5;
        public static final int material_text_view_test_line_height = 0x7f0700a6;
        public static final int material_text_view_test_line_height_override = 0x7f0700a7;
        public static final int mtrl_alert_dialog_background_inset_bottom = 0x7f0700a8;
        public static final int mtrl_alert_dialog_background_inset_end = 0x7f0700a9;
        public static final int mtrl_alert_dialog_background_inset_start = 0x7f0700aa;
        public static final int mtrl_alert_dialog_background_inset_top = 0x7f0700ab;
        public static final int mtrl_alert_dialog_picker_background_inset = 0x7f0700ac;
        public static final int mtrl_badge_horizontal_edge_offset = 0x7f0700ad;
        public static final int mtrl_badge_long_text_horizontal_padding = 0x7f0700ae;
        public static final int mtrl_badge_radius = 0x7f0700af;
        public static final int mtrl_badge_text_horizontal_edge_offset = 0x7f0700b0;
        public static final int mtrl_badge_text_size = 0x7f0700b1;
        public static final int mtrl_badge_with_text_radius = 0x7f0700b2;
        public static final int mtrl_bottomappbar_fabOffsetEndMode = 0x7f0700b3;
        public static final int mtrl_bottomappbar_fab_bottom_margin = 0x7f0700b4;
        public static final int mtrl_bottomappbar_fab_cradle_margin = 0x7f0700b5;
        public static final int mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700b6;
        public static final int mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f0700b7;
        public static final int mtrl_bottomappbar_height = 0x7f0700b8;
        public static final int mtrl_btn_corner_radius = 0x7f0700b9;
        public static final int mtrl_btn_dialog_btn_min_width = 0x7f0700ba;
        public static final int mtrl_btn_disabled_elevation = 0x7f0700bb;
        public static final int mtrl_btn_disabled_z = 0x7f0700bc;
        public static final int mtrl_btn_elevation = 0x7f0700bd;
        public static final int mtrl_btn_focused_z = 0x7f0700be;
        public static final int mtrl_btn_hovered_z = 0x7f0700bf;
        public static final int mtrl_btn_icon_btn_padding_left = 0x7f0700c0;
        public static final int mtrl_btn_icon_padding = 0x7f0700c1;
        public static final int mtrl_btn_inset = 0x7f0700c2;
        public static final int mtrl_btn_letter_spacing = 0x7f0700c3;
        public static final int mtrl_btn_padding_bottom = 0x7f0700c4;
        public static final int mtrl_btn_padding_left = 0x7f0700c5;
        public static final int mtrl_btn_padding_right = 0x7f0700c6;
        public static final int mtrl_btn_padding_top = 0x7f0700c7;
        public static final int mtrl_btn_pressed_z = 0x7f0700c8;
        public static final int mtrl_btn_stroke_size = 0x7f0700c9;
        public static final int mtrl_btn_text_btn_icon_padding = 0x7f0700ca;
        public static final int mtrl_btn_text_btn_padding_left = 0x7f0700cb;
        public static final int mtrl_btn_text_btn_padding_right = 0x7f0700cc;
        public static final int mtrl_btn_text_size = 0x7f0700cd;
        public static final int mtrl_btn_z = 0x7f0700ce;
        public static final int mtrl_calendar_action_height = 0x7f0700cf;
        public static final int mtrl_calendar_action_padding = 0x7f0700d0;
        public static final int mtrl_calendar_bottom_padding = 0x7f0700d1;
        public static final int mtrl_calendar_content_padding = 0x7f0700d2;
        public static final int mtrl_calendar_day_corner = 0x7f0700d3;
        public static final int mtrl_calendar_day_height = 0x7f0700d4;
        public static final int mtrl_calendar_day_horizontal_padding = 0x7f0700d5;
        public static final int mtrl_calendar_day_today_stroke = 0x7f0700d6;
        public static final int mtrl_calendar_day_vertical_padding = 0x7f0700d7;
        public static final int mtrl_calendar_day_width = 0x7f0700d8;
        public static final int mtrl_calendar_days_of_week_height = 0x7f0700d9;
        public static final int mtrl_calendar_dialog_background_inset = 0x7f0700da;
        public static final int mtrl_calendar_header_content_padding = 0x7f0700db;
        public static final int mtrl_calendar_header_content_padding_fullscreen = 0x7f0700dc;
        public static final int mtrl_calendar_header_divider_thickness = 0x7f0700dd;
        public static final int mtrl_calendar_header_height = 0x7f0700de;
        public static final int mtrl_calendar_header_height_fullscreen = 0x7f0700df;
        public static final int mtrl_calendar_header_selection_line_height = 0x7f0700e0;
        public static final int mtrl_calendar_header_text_padding = 0x7f0700e1;
        public static final int mtrl_calendar_header_toggle_margin_bottom = 0x7f0700e2;
        public static final int mtrl_calendar_header_toggle_margin_top = 0x7f0700e3;
        public static final int mtrl_calendar_landscape_header_width = 0x7f0700e4;
        public static final int mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0700e5;
        public static final int mtrl_calendar_month_horizontal_padding = 0x7f0700e6;
        public static final int mtrl_calendar_month_vertical_padding = 0x7f0700e7;
        public static final int mtrl_calendar_navigation_bottom_padding = 0x7f0700e8;
        public static final int mtrl_calendar_navigation_height = 0x7f0700e9;
        public static final int mtrl_calendar_navigation_top_padding = 0x7f0700ea;
        public static final int mtrl_calendar_pre_l_text_clip_padding = 0x7f0700eb;
        public static final int mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0700ec;
        public static final int mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0700ed;
        public static final int mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0700ee;
        public static final int mtrl_calendar_selection_text_baseline_to_top = 0x7f0700ef;
        public static final int mtrl_calendar_text_input_padding_top = 0x7f0700f0;
        public static final int mtrl_calendar_title_baseline_to_top = 0x7f0700f1;
        public static final int mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0700f2;
        public static final int mtrl_calendar_year_corner = 0x7f0700f3;
        public static final int mtrl_calendar_year_height = 0x7f0700f4;
        public static final int mtrl_calendar_year_horizontal_padding = 0x7f0700f5;
        public static final int mtrl_calendar_year_vertical_padding = 0x7f0700f6;
        public static final int mtrl_calendar_year_width = 0x7f0700f7;
        public static final int mtrl_card_checked_icon_margin = 0x7f0700f8;
        public static final int mtrl_card_checked_icon_size = 0x7f0700f9;
        public static final int mtrl_card_corner_radius = 0x7f0700fa;
        public static final int mtrl_card_dragged_z = 0x7f0700fb;
        public static final int mtrl_card_elevation = 0x7f0700fc;
        public static final int mtrl_card_spacing = 0x7f0700fd;
        public static final int mtrl_chip_pressed_translation_z = 0x7f0700fe;
        public static final int mtrl_chip_text_size = 0x7f0700ff;
        public static final int mtrl_edittext_rectangle_top_offset = 0x7f070100;
        public static final int mtrl_exposed_dropdown_menu_popup_elevation = 0x7f070101;
        public static final int mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f070102;
        public static final int mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f070103;
        public static final int mtrl_extended_fab_bottom_padding = 0x7f070104;
        public static final int mtrl_extended_fab_corner_radius = 0x7f070105;
        public static final int mtrl_extended_fab_disabled_elevation = 0x7f070106;
        public static final int mtrl_extended_fab_disabled_translation_z = 0x7f070107;
        public static final int mtrl_extended_fab_elevation = 0x7f070108;
        public static final int mtrl_extended_fab_end_padding = 0x7f070109;
        public static final int mtrl_extended_fab_end_padding_icon = 0x7f07010a;
        public static final int mtrl_extended_fab_icon_size = 0x7f07010b;
        public static final int mtrl_extended_fab_icon_text_spacing = 0x7f07010c;
        public static final int mtrl_extended_fab_min_height = 0x7f07010d;
        public static final int mtrl_extended_fab_min_width = 0x7f07010e;
        public static final int mtrl_extended_fab_start_padding = 0x7f07010f;
        public static final int mtrl_extended_fab_start_padding_icon = 0x7f070110;
        public static final int mtrl_extended_fab_top_padding = 0x7f070111;
        public static final int mtrl_extended_fab_translation_z_base = 0x7f070112;
        public static final int mtrl_extended_fab_translation_z_hovered_focused = 0x7f070113;
        public static final int mtrl_extended_fab_translation_z_pressed = 0x7f070114;
        public static final int mtrl_fab_elevation = 0x7f070115;
        public static final int mtrl_fab_min_touch_target = 0x7f070116;
        public static final int mtrl_fab_translation_z_hovered_focused = 0x7f070117;
        public static final int mtrl_fab_translation_z_pressed = 0x7f070118;
        public static final int mtrl_high_ripple_default_alpha = 0x7f070119;
        public static final int mtrl_high_ripple_focused_alpha = 0x7f07011a;
        public static final int mtrl_high_ripple_hovered_alpha = 0x7f07011b;
        public static final int mtrl_high_ripple_pressed_alpha = 0x7f07011c;
        public static final int mtrl_large_touch_target = 0x7f07011d;
        public static final int mtrl_low_ripple_default_alpha = 0x7f07011e;
        public static final int mtrl_low_ripple_focused_alpha = 0x7f07011f;
        public static final int mtrl_low_ripple_hovered_alpha = 0x7f070120;
        public static final int mtrl_low_ripple_pressed_alpha = 0x7f070121;
        public static final int mtrl_min_touch_target_size = 0x7f070122;
        public static final int mtrl_navigation_elevation = 0x7f070123;
        public static final int mtrl_navigation_item_horizontal_padding = 0x7f070124;
        public static final int mtrl_navigation_item_icon_padding = 0x7f070125;
        public static final int mtrl_navigation_item_icon_size = 0x7f070126;
        public static final int mtrl_navigation_item_shape_horizontal_margin = 0x7f070127;
        public static final int mtrl_navigation_item_shape_vertical_margin = 0x7f070128;
        public static final int mtrl_shape_corner_size_large_component = 0x7f070129;
        public static final int mtrl_shape_corner_size_medium_component = 0x7f07012a;
        public static final int mtrl_shape_corner_size_small_component = 0x7f07012b;
        public static final int mtrl_slider_halo_radius = 0x7f07012c;
        public static final int mtrl_slider_label_padding = 0x7f07012d;
        public static final int mtrl_slider_label_radius = 0x7f07012e;
        public static final int mtrl_slider_label_square_side = 0x7f07012f;
        public static final int mtrl_slider_thumb_elevation = 0x7f070130;
        public static final int mtrl_slider_thumb_radius = 0x7f070131;
        public static final int mtrl_slider_track_height = 0x7f070132;
        public static final int mtrl_slider_track_side_padding = 0x7f070133;
        public static final int mtrl_slider_track_top = 0x7f070134;
        public static final int mtrl_slider_widget_height = 0x7f070135;
        public static final int mtrl_snackbar_action_text_color_alpha = 0x7f070136;
        public static final int mtrl_snackbar_background_corner_radius = 0x7f070137;
        public static final int mtrl_snackbar_background_overlay_color_alpha = 0x7f070138;
        public static final int mtrl_snackbar_margin = 0x7f070139;
        public static final int mtrl_switch_thumb_elevation = 0x7f07013a;
        public static final int mtrl_textinput_box_corner_radius_medium = 0x7f07013b;
        public static final int mtrl_textinput_box_corner_radius_small = 0x7f07013c;
        public static final int mtrl_textinput_box_label_cutout_padding = 0x7f07013d;
        public static final int mtrl_textinput_box_stroke_width_default = 0x7f07013e;
        public static final int mtrl_textinput_box_stroke_width_focused = 0x7f07013f;
        public static final int mtrl_textinput_counter_margin_start = 0x7f070140;
        public static final int mtrl_textinput_end_icon_margin_start = 0x7f070141;
        public static final int mtrl_textinput_outline_box_expanded_padding = 0x7f070142;
        public static final int mtrl_textinput_start_icon_margin_end = 0x7f070143;
        public static final int mtrl_toolbar_default_height = 0x7f070144;
        public static final int mtrl_tooltip_arrowSize = 0x7f070145;
        public static final int mtrl_tooltip_cornerSize = 0x7f070146;
        public static final int mtrl_tooltip_minHeight = 0x7f070147;
        public static final int mtrl_tooltip_minWidth = 0x7f070148;
        public static final int mtrl_tooltip_padding = 0x7f070149;
        public static final int mtrl_transition_shared_axis_slide_distance = 0x7f07014a;
        public static final int nav_header_height = 0x7f07014b;
        public static final int nav_header_vertical_spacing = 0x7f07014c;
        public static final int notification_action_icon_size = 0x7f07014d;
        public static final int notification_action_text_size = 0x7f07014e;
        public static final int notification_big_circle_margin = 0x7f07014f;
        public static final int notification_content_margin_start = 0x7f070150;
        public static final int notification_large_icon_height = 0x7f070151;
        public static final int notification_large_icon_width = 0x7f070152;
        public static final int notification_main_column_padding_top = 0x7f070153;
        public static final int notification_media_narrow_margin = 0x7f070154;
        public static final int notification_right_icon_size = 0x7f070155;
        public static final int notification_right_side_padding_top = 0x7f070156;
        public static final int notification_small_icon_background_padding = 0x7f070157;
        public static final int notification_small_icon_size_as_large = 0x7f070158;
        public static final int notification_subtext_size = 0x7f070159;
        public static final int notification_top_pad = 0x7f07015a;
        public static final int notification_top_pad_large_text = 0x7f07015b;
        public static final int preference_dropdown_padding_start = 0x7f07015c;
        public static final int preference_icon_minWidth = 0x7f07015d;
        public static final int preference_seekbar_padding_horizontal = 0x7f07015e;
        public static final int preference_seekbar_padding_vertical = 0x7f07015f;
        public static final int preference_seekbar_value_minWidth = 0x7f070160;
        public static final int preferences_detail_width = 0x7f070161;
        public static final int preferences_header_width = 0x7f070162;
        public static final int test_mtrl_calendar_day_cornerSize = 0x7f070163;
        public static final int tooltip_corner_radius = 0x7f070164;
        public static final int tooltip_horizontal_padding = 0x7f070165;
        public static final int tooltip_margin = 0x7f070166;
        public static final int tooltip_precise_anchor_extra_offset = 0x7f070167;
        public static final int tooltip_precise_anchor_threshold = 0x7f070168;
        public static final int tooltip_vertical_padding = 0x7f070169;
        public static final int tooltip_y_offset_non_touch = 0x7f07016a;
        public static final int tooltip_y_offset_touch = 0x7f07016b;
    }

    public static final class drawable {
        public static final int res_0x7f080000_avd_hide_password__0 = 0x7f080000;
        public static final int res_0x7f080001_avd_hide_password__1 = 0x7f080001;
        public static final int res_0x7f080002_avd_hide_password__2 = 0x7f080002;
        public static final int res_0x7f080003_avd_show_password__0 = 0x7f080003;
        public static final int res_0x7f080004_avd_show_password__1 = 0x7f080004;
        public static final int res_0x7f080005_avd_show_password__2 = 0x7f080005;
        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f080006;
        public static final int abc_action_bar_item_background_material = 0x7f080007;
        public static final int abc_btn_borderless_material = 0x7f080008;
        public static final int abc_btn_check_material = 0x7f080009;
        public static final int abc_btn_check_material_anim = 0x7f08000a;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f08000b;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f08000c;
        public static final int abc_btn_colored_material = 0x7f08000d;
        public static final int abc_btn_default_mtrl_shape = 0x7f08000e;
        public static final int abc_btn_radio_material = 0x7f08000f;
        public static final int abc_btn_radio_material_anim = 0x7f080010;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f080011;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f080012;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f080013;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f080014;
        public static final int abc_cab_background_internal_bg = 0x7f080015;
        public static final int abc_cab_background_top_material = 0x7f080016;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f080017;
        public static final int abc_control_background_material = 0x7f080018;
        public static final int abc_dialog_material_background = 0x7f080019;
        public static final int abc_edit_text_material = 0x7f08001a;
        public static final int abc_ic_ab_back_material = 0x7f08001b;
        public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f08001c;
        public static final int abc_ic_clear_material = 0x7f08001d;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f08001e;
        public static final int abc_ic_go_search_api_material = 0x7f08001f;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f080020;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f080021;
        public static final int abc_ic_menu_overflow_material = 0x7f080022;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f080023;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f080024;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f080025;
        public static final int abc_ic_search_api_material = 0x7f080026;
        public static final int abc_ic_voice_search_api_material = 0x7f080027;
        public static final int abc_item_background_holo_dark = 0x7f080028;
        public static final int abc_item_background_holo_light = 0x7f080029;
        public static final int abc_list_divider_material = 0x7f08002a;
        public static final int abc_list_divider_mtrl_alpha = 0x7f08002b;
        public static final int abc_list_focused_holo = 0x7f08002c;
        public static final int abc_list_longpressed_holo = 0x7f08002d;
        public static final int abc_list_pressed_holo_dark = 0x7f08002e;
        public static final int abc_list_pressed_holo_light = 0x7f08002f;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f080030;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f080031;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f080032;
        public static final int abc_list_selector_disabled_holo_light = 0x7f080033;
        public static final int abc_list_selector_holo_dark = 0x7f080034;
        public static final int abc_list_selector_holo_light = 0x7f080035;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f080036;
        public static final int abc_popup_background_mtrl_mult = 0x7f080037;
        public static final int abc_ratingbar_indicator_material = 0x7f080038;
        public static final int abc_ratingbar_material = 0x7f080039;
        public static final int abc_ratingbar_small_material = 0x7f08003a;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f08003b;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08003c;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08003d;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f08003e;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f08003f;
        public static final int abc_seekbar_thumb_material = 0x7f080040;
        public static final int abc_seekbar_tick_mark_material = 0x7f080041;
        public static final int abc_seekbar_track_material = 0x7f080042;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f080043;
        public static final int abc_spinner_textfield_background_material = 0x7f080044;
        public static final int abc_star_black_48dp = 0x7f080045;
        public static final int abc_star_half_black_48dp = 0x7f080046;
        public static final int abc_switch_thumb_material = 0x7f080047;
        public static final int abc_switch_track_mtrl_alpha = 0x7f080048;
        public static final int abc_tab_indicator_material = 0x7f080049;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f08004a;
        public static final int abc_text_cursor_material = 0x7f08004b;
        public static final int abc_text_select_handle_left_mtrl = 0x7f08004c;
        public static final int abc_text_select_handle_middle_mtrl = 0x7f08004d;
        public static final int abc_text_select_handle_right_mtrl = 0x7f08004e;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f08004f;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f080050;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f080051;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f080052;
        public static final int abc_textfield_search_material = 0x7f080053;
        public static final int abc_vector_test = 0x7f080054;
        public static final int avd_hide_password = 0x7f080055;
        public static final int avd_show_password = 0x7f080056;
        public static final int back = 0x7f080057;
        public static final int bg_gray = 0x7f080058;
        public static final int bg_white = 0x7f080059;
        public static final int bg_white2 = 0x7f08005a;
        public static final int bg_white9 = 0x7f08005b;
        public static final int border_gray = 0x7f08005c;
        public static final int border_window = 0x7f08005d;
        public static final int btn_checkbox_checked_mtrl = 0x7f08005e;
        public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08005f;
        public static final int btn_checkbox_unchecked_mtrl = 0x7f080060;
        public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080061;
        public static final int btn_radio_off_mtrl = 0x7f080062;
        public static final int btn_radio_off_to_on_mtrl_animation = 0x7f080063;
        public static final int btn_radio_on_mtrl = 0x7f080064;
        public static final int btn_radio_on_to_off_mtrl_animation = 0x7f080065;
        public static final int circle_shape_bg = 0x7f080066;
        public static final int circle_shape_normal = 0x7f080067;
        public static final int circle_shape_pressed = 0x7f080068;
        public static final int design_bottom_navigation_item_background = 0x7f080069;
        public static final int design_fab_background = 0x7f08006a;
        public static final int design_ic_visibility = 0x7f08006b;
        public static final int design_ic_visibility_off = 0x7f08006c;
        public static final int design_password_eye = 0x7f08006d;
        public static final int design_snackbar_background = 0x7f08006e;
        public static final int empty = 0x7f08006f;
        public static final int ic_arrow_down_24dp = 0x7f080070;
        public static final int ic_bike = 0x7f080071;
        public static final int ic_close = 0x7f080072;
        public static final int ic_contact = 0x7f080073;
        public static final int ic_copy = 0x7f080074;
        public static final int ic_date = 0x7f080075;
        public static final int ic_delete = 0x7f080076;
        public static final int ic_down = 0x7f080077;
        public static final int ic_fly = 0x7f080078;
        public static final int ic_history = 0x7f080079;
        public static final int ic_home_position = 0x7f08007a;
        public static final int ic_input = 0x7f08007b;
        public static final int ic_key = 0x7f08007c;
        public static final int ic_launcher_background = 0x7f08007d;
        public static final int ic_launcher_foreground = 0x7f08007e;
        public static final int ic_leading_in = 0x7f08007f;
        public static final int ic_leading_out = 0x7f080080;
        public static final int ic_left = 0x7f080081;
        public static final int ic_left_down = 0x7f080082;
        public static final int ic_left_up = 0x7f080083;
        public static final int ic_lock_close = 0x7f080084;
        public static final int ic_lock_open = 0x7f080085;
        public static final int ic_map = 0x7f080086;
        public static final int ic_menu_dev = 0x7f080087;
        public static final int ic_menu_feedback = 0x7f080088;
        public static final int ic_menu_history = 0x7f080089;
        public static final int ic_menu_settings = 0x7f08008a;
        public static final int ic_menu_upgrade = 0x7f08008b;
        public static final int ic_move = 0x7f08008c;
        public static final int ic_msg = 0x7f08008d;
        public static final int ic_mtrl_checked_circle = 0x7f08008e;
        public static final int ic_mtrl_chip_checked_black = 0x7f08008f;
        public static final int ic_mtrl_chip_checked_circle = 0x7f080090;
        public static final int ic_mtrl_chip_close_circle = 0x7f080091;
        public static final int ic_position = 0x7f080092;
        public static final int ic_pwd = 0x7f080093;
        public static final int ic_right = 0x7f080094;
        public static final int ic_right_down = 0x7f080095;
        public static final int ic_right_up = 0x7f080096;
        public static final int ic_run = 0x7f080097;
        public static final int ic_save = 0x7f080098;
        public static final int ic_search = 0x7f080099;
        public static final int ic_share = 0x7f08009a;
        public static final int ic_up = 0x7f08009b;
        public static final int ic_user = 0x7f08009c;
        public static final int ic_walk = 0x7f08009d;
        public static final int ic_zoom_in = 0x7f08009e;
        public static final int ic_zoom_out = 0x7f08009f;
        public static final int icon_gcoding = 0x7f0800a0;
        public static final int material_ic_calendar_black_24dp = 0x7f0800a1;
        public static final int material_ic_clear_black_24dp = 0x7f0800a2;
        public static final int material_ic_edit_black_24dp = 0x7f0800a3;
        public static final int material_ic_keyboard_arrow_left_black_24dp = 0x7f0800a4;
        public static final int material_ic_keyboard_arrow_right_black_24dp = 0x7f0800a5;
        public static final int material_ic_menu_arrow_down_black_24dp = 0x7f0800a6;
        public static final int material_ic_menu_arrow_up_black_24dp = 0x7f0800a7;
        public static final int mtrl_dialog_background = 0x7f0800a8;
        public static final int mtrl_dropdown_arrow = 0x7f0800a9;
        public static final int mtrl_ic_arrow_drop_down = 0x7f0800aa;
        public static final int mtrl_ic_arrow_drop_up = 0x7f0800ab;
        public static final int mtrl_ic_cancel = 0x7f0800ac;
        public static final int mtrl_ic_error = 0x7f0800ad;
        public static final int mtrl_popupmenu_background = 0x7f0800ae;
        public static final int mtrl_popupmenu_background_dark = 0x7f0800af;
        public static final int mtrl_tabs_default_indicator = 0x7f0800b0;
        public static final int navigation_empty_icon = 0x7f0800b1;
        public static final int notification_action_background = 0x7f0800b2;
        public static final int notification_bg = 0x7f0800b3;
        public static final int notification_bg_low = 0x7f0800b4;
        public static final int notification_bg_low_normal = 0x7f0800b5;
        public static final int notification_bg_low_pressed = 0x7f0800b6;
        public static final int notification_bg_normal = 0x7f0800b7;
        public static final int notification_bg_normal_pressed = 0x7f0800b8;
        public static final int notification_icon_background = 0x7f0800b9;
        public static final int notification_template_icon_bg = 0x7f0800ba;
        public static final int notification_template_icon_low_bg = 0x7f0800bb;
        public static final int notification_tile_bg = 0x7f0800bc;
        public static final int notify_panel_notification_icon_bg = 0x7f0800bd;
        public static final int position = 0x7f0800be;
        public static final int preference_list_divider_material = 0x7f0800bf;
        public static final int sel_guide_btn6 = 0x7f0800c0;
        public static final int sel_guide_btn7 = 0x7f0800c1;
        public static final int sel_orange_btn7 = 0x7f0800c2;
        public static final int side_nav_bar = 0x7f0800c3;
        public static final int test_custom_background = 0x7f0800c4;
        public static final int test_level_drawable = 0x7f0800c5;
        public static final int tooltip_frame_dark = 0x7f0800c6;
        public static final int tooltip_frame_light = 0x7f0800c7;
        public static final int welcome = 0x7f0800c8;
        public static final int white_tran = 0x7f0800c9;
    }

    public static final class id {
        public static final int ALT = 0x7f090000;
        public static final int BDLatLngText = 0x7f090001;
        public static final int BOTTOM_END = 0x7f090002;
        public static final int BOTTOM_START = 0x7f090003;
        public static final int CTRL = 0x7f090004;
        public static final int FUNCTION = 0x7f090005;
        public static final int LocationID = 0x7f090006;
        public static final int LocationText = 0x7f090007;
        public static final int META = 0x7f090008;
        public static final int NO_DEBUG = 0x7f090009;
        public static final int RadioGroupMapType = 0x7f09000a;
        public static final int SHIFT = 0x7f09000b;
        public static final int SHOW_ALL = 0x7f09000c;
        public static final int SHOW_PATH = 0x7f09000d;
        public static final int SHOW_PROGRESS = 0x7f09000e;
        public static final int SYM = 0x7f09000f;
        public static final int TOP_END = 0x7f090010;
        public static final int TOP_START = 0x7f090011;
        public static final int TimeText = 0x7f090012;
        public static final int WGSLatLngText = 0x7f090013;
        public static final int accelerate = 0x7f090014;
        public static final int accessibility_action_clickable_span = 0x7f090015;
        public static final int accessibility_custom_action_0 = 0x7f090016;
        public static final int accessibility_custom_action_1 = 0x7f090017;
        public static final int accessibility_custom_action_10 = 0x7f090018;
        public static final int accessibility_custom_action_11 = 0x7f090019;
        public static final int accessibility_custom_action_12 = 0x7f09001a;
        public static final int accessibility_custom_action_13 = 0x7f09001b;
        public static final int accessibility_custom_action_14 = 0x7f09001c;
        public static final int accessibility_custom_action_15 = 0x7f09001d;
        public static final int accessibility_custom_action_16 = 0x7f09001e;
        public static final int accessibility_custom_action_17 = 0x7f09001f;
        public static final int accessibility_custom_action_18 = 0x7f090020;
        public static final int accessibility_custom_action_19 = 0x7f090021;
        public static final int accessibility_custom_action_2 = 0x7f090022;
        public static final int accessibility_custom_action_20 = 0x7f090023;
        public static final int accessibility_custom_action_21 = 0x7f090024;
        public static final int accessibility_custom_action_22 = 0x7f090025;
        public static final int accessibility_custom_action_23 = 0x7f090026;
        public static final int accessibility_custom_action_24 = 0x7f090027;
        public static final int accessibility_custom_action_25 = 0x7f090028;
        public static final int accessibility_custom_action_26 = 0x7f090029;
        public static final int accessibility_custom_action_27 = 0x7f09002a;
        public static final int accessibility_custom_action_28 = 0x7f09002b;
        public static final int accessibility_custom_action_29 = 0x7f09002c;
        public static final int accessibility_custom_action_3 = 0x7f09002d;
        public static final int accessibility_custom_action_30 = 0x7f09002e;
        public static final int accessibility_custom_action_31 = 0x7f09002f;
        public static final int accessibility_custom_action_4 = 0x7f090030;
        public static final int accessibility_custom_action_5 = 0x7f090031;
        public static final int accessibility_custom_action_6 = 0x7f090032;
        public static final int accessibility_custom_action_7 = 0x7f090033;
        public static final int accessibility_custom_action_8 = 0x7f090034;
        public static final int accessibility_custom_action_9 = 0x7f090035;
        public static final int actionDown = 0x7f090036;
        public static final int actionDownUp = 0x7f090037;
        public static final int actionUp = 0x7f090038;
        public static final int action_bar = 0x7f090039;
        public static final int action_bar_activity_content = 0x7f09003a;
        public static final int action_bar_container = 0x7f09003b;
        public static final int action_bar_root = 0x7f09003c;
        public static final int action_bar_spinner = 0x7f09003d;
        public static final int action_bar_subtitle = 0x7f09003e;
        public static final int action_bar_title = 0x7f09003f;
        public static final int action_container = 0x7f090040;
        public static final int action_context_bar = 0x7f090041;
        public static final int action_delete = 0x7f090042;
        public static final int action_divider = 0x7f090043;
        public static final int action_image = 0x7f090044;
        public static final int action_menu_divider = 0x7f090045;
        public static final int action_menu_presenter = 0x7f090046;
        public static final int action_mode_bar = 0x7f090047;
        public static final int action_mode_bar_stub = 0x7f090048;
        public static final int action_mode_close_button = 0x7f090049;
        public static final int action_search = 0x7f09004a;
        public static final int action_text = 0x7f09004b;
        public static final int actions = 0x7f09004c;
        public static final int activity_chooser_view_content = 0x7f09004d;
        public static final int add = 0x7f09004e;
        public static final int alertTitle = 0x7f09004f;
        public static final int aligned = 0x7f090050;
        public static final int all = 0x7f090051;
        public static final int allStates = 0x7f090052;
        public static final int always = 0x7f090053;
        public static final int androidx_window_activity_scope = 0x7f090054;
        public static final int animateToEnd = 0x7f090055;
        public static final int animateToStart = 0x7f090056;
        public static final int antiClockwise = 0x7f090057;
        public static final int anticipate = 0x7f090058;
        public static final int asConfigured = 0x7f090059;
        public static final int async = 0x7f09005a;
        public static final int auto = 0x7f09005b;
        public static final int autoComplete = 0x7f09005c;
        public static final int autoCompleteToEnd = 0x7f09005d;
        public static final int autoCompleteToStart = 0x7f09005e;
        public static final int backioc = 0x7f09005f;
        public static final int baohuimg = 0x7f090060;
        public static final int baohutv = 0x7f090061;
        public static final int barrier = 0x7f090062;
        public static final int baseline = 0x7f090063;
        public static final int bdMapView = 0x7f090064;
        public static final int beginOnFirstDraw = 0x7f090065;
        public static final int beginning = 0x7f090066;
        public static final int bestChoice = 0x7f090067;
        public static final int bianjiimg = 0x7f090068;
        public static final int blocking = 0x7f090069;
        public static final int bmapView = 0x7f09006a;
        public static final int bottom = 0x7f09006b;
        public static final int bounce = 0x7f09006c;
        public static final int bounceBoth = 0x7f09006d;
        public static final int bounceEnd = 0x7f09006e;
        public static final int bounceStart = 0x7f09006f;
        public static final int btnBack = 0x7f090070;
        public static final int btnGo = 0x7f090071;
        public static final int btn_center = 0x7f090072;
        public static final int btn_east = 0x7f090073;
        public static final int btn_north = 0x7f090074;
        public static final int btn_north_east = 0x7f090075;
        public static final int btn_north_west = 0x7f090076;
        public static final int btn_south = 0x7f090077;
        public static final int btn_south_east = 0x7f090078;
        public static final int btn_south_west = 0x7f090079;
        public static final int btn_west = 0x7f09007a;
        public static final int buttonPanel = 0x7f09007b;
        public static final int cache_measures = 0x7f09007c;
        public static final int callMeasure = 0x7f09007d;
        public static final int cancel_button = 0x7f09007e;
        public static final int carryVelocity = 0x7f09007f;
        public static final int center = 0x7f090080;
        public static final int center_horizontal = 0x7f090081;
        public static final int center_vertical = 0x7f090082;
        public static final int chain = 0x7f090083;
        public static final int chain2 = 0x7f090084;
        public static final int chains = 0x7f090085;
        public static final int checkbox = 0x7f090086;
        public static final int checked = 0x7f090087;
        public static final int chip = 0x7f090088;
        public static final int chip1 = 0x7f090089;
        public static final int chip2 = 0x7f09008a;
        public static final int chip3 = 0x7f09008b;
        public static final int chip_group = 0x7f09008c;
        public static final int chronometer = 0x7f09008d;
        public static final int city = 0x7f09008e;
        public static final int clPosition = 0x7f09008f;
        public static final int clear_text = 0x7f090090;
        public static final int clip_horizontal = 0x7f090091;
        public static final int clip_vertical = 0x7f090092;
        public static final int clockwise = 0x7f090093;
        public static final int closest = 0x7f090094;
        public static final int collapseActionView = 0x7f090095;
        public static final int confirm_button = 0x7f090096;
        public static final int constraint = 0x7f090097;
        public static final int container = 0x7f090098;
        public static final int content = 0x7f090099;
        public static final int contentPanel = 0x7f09009a;
        public static final int continuousVelocity = 0x7f09009b;
        public static final int coordinator = 0x7f09009c;
        public static final int cos = 0x7f09009d;
        public static final int cur_position = 0x7f09009e;
        public static final int currentState = 0x7f09009f;
        public static final int custom = 0x7f0900a0;
        public static final int customPanel = 0x7f0900a1;
        public static final int cut = 0x7f0900a2;
        public static final int date_picker = 0x7f0900a3;
        public static final int date_picker_actions = 0x7f0900a4;
        public static final int decelerate = 0x7f0900a5;
        public static final int decelerateAndComplete = 0x7f0900a6;
        public static final int decor_content_parent = 0x7f0900a7;
        public static final int default_activity_button = 0x7f0900a8;
        public static final int deltaRelative = 0x7f0900a9;
        public static final int dependency_ordering = 0x7f0900aa;
        public static final int design_bottom_sheet = 0x7f0900ab;
        public static final int design_menu_item_action_area = 0x7f0900ac;
        public static final int design_menu_item_action_area_stub = 0x7f0900ad;
        public static final int design_menu_item_text = 0x7f0900ae;
        public static final int design_navigation_view = 0x7f0900af;
        public static final int dialog_button = 0x7f0900b0;
        public static final int dimensions = 0x7f0900b1;
        public static final int dingweiimg = 0x7f0900b2;
        public static final int direct = 0x7f0900b3;
        public static final int disableHome = 0x7f0900b4;
        public static final int disableIntraAutoTransition = 0x7f0900b5;
        public static final int disablePostScroll = 0x7f0900b6;
        public static final int disableScroll = 0x7f0900b7;
        public static final int dragAnticlockwise = 0x7f0900b8;
        public static final int dragClockwise = 0x7f0900b9;
        public static final int dragDown = 0x7f0900ba;
        public static final int dragEnd = 0x7f0900bb;
        public static final int dragLeft = 0x7f0900bc;
        public static final int dragRight = 0x7f0900bd;
        public static final int dragStart = 0x7f0900be;
        public static final int dragUp = 0x7f0900bf;
        public static final int drawer_layout = 0x7f0900c0;
        public static final int dropdown_menu = 0x7f0900c1;
        public static final int easeIn = 0x7f0900c2;
        public static final int easeInOut = 0x7f0900c3;
        public static final int easeOut = 0x7f0900c4;
        public static final int east = 0x7f0900c5;
        public static final int edit_query = 0x7f0900c6;
        public static final int end = 0x7f0900c7;
        public static final int enterAlways = 0x7f0900c8;
        public static final int enterAlwaysCollapsed = 0x7f0900c9;
        public static final int etCode = 0x7f0900ca;
        public static final int et_address = 0x7f0900cb;
        public static final int exitUntilCollapsed = 0x7f0900cc;
        public static final int expand_activities_button = 0x7f0900cd;
        public static final int expanded_menu = 0x7f0900ce;
        public static final int faBtnStart = 0x7f0900cf;
        public static final int fade = 0x7f0900d0;
        public static final int fill = 0x7f0900d1;
        public static final int fill_horizontal = 0x7f0900d2;
        public static final int fill_vertical = 0x7f0900d3;
        public static final int filled = 0x7f0900d4;
        public static final int fitToContents = 0x7f0900d5;
        public static final int fixed = 0x7f0900d6;
        public static final int flip = 0x7f0900d7;
        public static final int floating = 0x7f0900d8;
        public static final int forever = 0x7f0900d9;
        public static final int fragment_container_view_tag = 0x7f0900da;
        public static final int frost = 0x7f0900db;
        public static final int ghost_view = 0x7f0900dc;
        public static final int ghost_view_holder = 0x7f0900dd;
        public static final int gone = 0x7f0900de;
        public static final int graph = 0x7f0900df;
        public static final int graph_wrap = 0x7f0900e0;
        public static final int group_divider = 0x7f0900e1;
        public static final int grouping = 0x7f0900e2;
        public static final int groups = 0x7f0900e3;
        public static final int hideable = 0x7f0900e4;
        public static final int home = 0x7f0900e5;
        public static final int homeAsUp = 0x7f0900e6;
        public static final int honorRequest = 0x7f0900e7;
        public static final int horizontal_only = 0x7f0900e8;
        public static final int icon = 0x7f0900e9;
        public static final int icon_frame = 0x7f0900ea;
        public static final int icon_group = 0x7f0900eb;
        public static final int ifRoom = 0x7f0900ec;
        public static final int ignore = 0x7f0900ed;
        public static final int ignoreRequest = 0x7f0900ee;
        public static final int image = 0x7f0900ef;
        public static final int immediateStop = 0x7f0900f0;
        public static final int included = 0x7f0900f1;
        public static final int info = 0x7f0900f2;
        public static final int input_pos = 0x7f0900f3;
        public static final int input_position_cancel = 0x7f0900f4;
        public static final int input_position_ok = 0x7f0900f5;
        public static final int invisible = 0x7f0900f6;
        public static final int italic = 0x7f0900f7;
        public static final int item_touch_helper_previous_elevation = 0x7f0900f8;
        public static final int ivBack = 0x7f0900f9;
        public static final int ivLogo = 0x7f0900fa;
        public static final int joystick_his_close = 0x7f0900fb;
        public static final int joystick_his_record_list_view = 0x7f0900fc;
        public static final int joystick_his_record_no_textview = 0x7f0900fd;
        public static final int joystick_his_searchView = 0x7f0900fe;
        public static final int joystick_his_tips = 0x7f0900ff;
        public static final int joystick_latitude = 0x7f090100;
        public static final int joystick_longitude = 0x7f090101;
        public static final int joystick_map_searchView = 0x7f090102;
        public static final int joystick_map_tips = 0x7f090103;
        public static final int jumpToEnd = 0x7f090104;
        public static final int jumpToStart = 0x7f090105;
        public static final int labeled = 0x7f090106;
        public static final int largeLabel = 0x7f090107;
        public static final int layout = 0x7f090108;
        public static final int left = 0x7f090109;
        public static final int legacy = 0x7f09010a;
        public static final int line = 0x7f09010b;
        public static final int line1 = 0x7f09010c;
        public static final int line3 = 0x7f09010d;
        public static final int linear = 0x7f09010e;
        public static final int listMode = 0x7f09010f;
        public static final int list_item = 0x7f090110;
        public static final int llContent = 0x7f090111;
        public static final int llTop = 0x7f090112;
        public static final int ll_bottom = 0x7f090113;
        public static final int ll_et = 0x7f090114;
        public static final int ll_root = 0x7f090115;
        public static final int locale = 0x7f090116;
        public static final int ltr = 0x7f090117;
        public static final int mapNormal = 0x7f090118;
        public static final int mapSatellite = 0x7f090119;
        public static final int map_close = 0x7f09011a;
        public static final int map_joystick = 0x7f09011b;
        public static final int map_search_linear = 0x7f09011c;
        public static final int map_search_list_view = 0x7f09011d;
        public static final int markwon_drawables_scheduler = 0x7f09011e;
        public static final int markwon_drawables_scheduler_last_text_hashcode = 0x7f09011f;
        public static final int masked = 0x7f090120;
        public static final int match_constraint = 0x7f090121;
        public static final int match_parent = 0x7f090122;
        public static final int message = 0x7f090123;
        public static final int middle = 0x7f090124;
        public static final int mini = 0x7f090125;
        public static final int month_grid = 0x7f090126;
        public static final int month_navigation_bar = 0x7f090127;
        public static final int month_navigation_fragment_toggle = 0x7f090128;
        public static final int month_navigation_next = 0x7f090129;
        public static final int month_navigation_previous = 0x7f09012a;
        public static final int month_title = 0x7f09012b;
        public static final int motion_base = 0x7f09012c;
        public static final int mtrl_calendar_day_selector_frame = 0x7f09012d;
        public static final int mtrl_calendar_days_of_week = 0x7f09012e;
        public static final int mtrl_calendar_frame = 0x7f09012f;
        public static final int mtrl_calendar_main_pane = 0x7f090130;
        public static final int mtrl_calendar_months = 0x7f090131;
        public static final int mtrl_calendar_selection_frame = 0x7f090132;
        public static final int mtrl_calendar_text_input_frame = 0x7f090133;
        public static final int mtrl_calendar_year_selector_frame = 0x7f090134;
        public static final int mtrl_card_checked_layer_id = 0x7f090135;
        public static final int mtrl_child_content_container = 0x7f090136;
        public static final int mtrl_internal_children_alpha_tag = 0x7f090137;
        public static final int mtrl_motion_snapshot_view = 0x7f090138;
        public static final int mtrl_picker_fullscreen = 0x7f090139;
        public static final int mtrl_picker_header = 0x7f09013a;
        public static final int mtrl_picker_header_selection_text = 0x7f09013b;
        public static final int mtrl_picker_header_title_and_selection = 0x7f09013c;
        public static final int mtrl_picker_header_toggle = 0x7f09013d;
        public static final int mtrl_picker_text_input_date = 0x7f09013e;
        public static final int mtrl_picker_text_input_range_end = 0x7f09013f;
        public static final int mtrl_picker_text_input_range_start = 0x7f090140;
        public static final int mtrl_picker_title_text = 0x7f090141;
        public static final int multiply = 0x7f090142;
        public static final int nav_contact = 0x7f090143;
        public static final int nav_dev = 0x7f090144;
        public static final int nav_feedback = 0x7f090145;
        public static final int nav_history = 0x7f090146;
        public static final int nav_settings = 0x7f090147;
        public static final int nav_update = 0x7f090148;
        public static final int nav_view = 0x7f090149;
        public static final int navigation_header_container = 0x7f09014a;
        public static final int never = 0x7f09014b;
        public static final int neverCompleteToEnd = 0x7f09014c;
        public static final int neverCompleteToStart = 0x7f09014d;
        public static final int noScroll = 0x7f09014e;
        public static final int noState = 0x7f09014f;
        public static final int none = 0x7f090150;
        public static final int normal = 0x7f090151;
        public static final int north = 0x7f090152;
        public static final int notification_background = 0x7f090153;
        public static final int notification_main_column = 0x7f090154;
        public static final int notification_main_column_container = 0x7f090155;
        public static final int off = 0x7f090156;
        public static final int on = 0x7f090157;
        public static final int onInterceptTouchReturnSwipe = 0x7f090158;
        public static final int outline = 0x7f090159;
        public static final int overshoot = 0x7f09015a;
        public static final int packed = 0x7f09015b;
        public static final int parallax = 0x7f09015c;
        public static final int parent = 0x7f09015d;
        public static final int parentPanel = 0x7f09015e;
        public static final int parentRelative = 0x7f09015f;
        public static final int parent_matrix = 0x7f090160;
        public static final int password_toggle = 0x7f090161;
        public static final int path = 0x7f090162;
        public static final int pathRelative = 0x7f090163;
        public static final int peekHeight = 0x7f090164;
        public static final int percent = 0x7f090165;
        public static final int pin = 0x7f090166;
        public static final int poi_address = 0x7f090167;
        public static final int poi_copy = 0x7f090168;
        public static final int poi_fly = 0x7f090169;
        public static final int poi_latitude = 0x7f09016a;
        public static final int poi_longitude = 0x7f09016b;
        public static final int poi_name = 0x7f09016c;
        public static final int poi_save = 0x7f09016d;
        public static final int poi_share = 0x7f09016e;
        public static final int pos_type_bd = 0x7f09016f;
        public static final int position = 0x7f090170;
        public static final int postLayout = 0x7f090171;
        public static final int preferences_detail = 0x7f090172;
        public static final int preferences_header = 0x7f090173;
        public static final int preferences_sliding_pane_layout = 0x7f090174;
        public static final int progress_circular = 0x7f090175;
        public static final int progress_horizontal = 0x7f090176;
        public static final int radio = 0x7f090177;
        public static final int ratio = 0x7f090178;
        public static final int record_list_view = 0x7f090179;
        public static final int record_no_textview = 0x7f09017a;
        public static final int rectangles = 0x7f09017b;
        public static final int recycler_view = 0x7f09017c;
        public static final int reg_agree = 0x7f09017d;
        public static final int reg_cancel = 0x7f09017e;
        public static final int reg_check = 0x7f09017f;
        public static final int reg_request = 0x7f090180;
        public static final int reg_response = 0x7f090181;
        public static final int reg_user_name = 0x7f090182;
        public static final int reverseSawtooth = 0x7f090183;
        public static final int right = 0x7f090184;
        public static final int right_icon = 0x7f090185;
        public static final int right_side = 0x7f090186;
        public static final int rlTitles = 0x7f090187;
        public static final int rounded = 0x7f090188;
        public static final int row_index_key = 0x7f090189;
        public static final int rtl = 0x7f09018a;
        public static final int save_non_transition_alpha = 0x7f09018b;
        public static final int save_overlay_view = 0x7f09018c;
        public static final int sawtooth = 0x7f09018d;
        public static final int scale = 0x7f09018e;
        public static final int scb = 0x7f09018f;
        public static final int screen = 0x7f090190;
        public static final int scroll = 0x7f090191;
        public static final int scrollIndicatorDown = 0x7f090192;
        public static final int scrollIndicatorUp = 0x7f090193;
        public static final int scrollView = 0x7f090194;
        public static final int scroll_view = 0x7f090195;
        public static final int scrollable = 0x7f090196;
        public static final int searchView = 0x7f090197;
        public static final int search_badge = 0x7f090198;
        public static final int search_bar = 0x7f090199;
        public static final int search_button = 0x7f09019a;
        public static final int search_close_btn = 0x7f09019b;
        public static final int search_description = 0x7f09019c;
        public static final int search_edit_frame = 0x7f09019d;
        public static final int search_go_btn = 0x7f09019e;
        public static final int search_history_linear = 0x7f09019f;
        public static final int search_history_list_view = 0x7f0901a0;
        public static final int search_isLoc = 0x7f0901a1;
        public static final int search_key = 0x7f0901a2;
        public static final int search_latitude = 0x7f0901a3;
        public static final int search_linear = 0x7f0901a4;
        public static final int search_list_view = 0x7f0901a5;
        public static final int search_longitude = 0x7f0901a6;
        public static final int search_mag_icon = 0x7f0901a7;
        public static final int search_plate = 0x7f0901a8;
        public static final int search_src_text = 0x7f0901a9;
        public static final int search_timestamp = 0x7f0901aa;
        public static final int search_voice_btn = 0x7f0901ab;
        public static final int seekbar = 0x7f0901ac;
        public static final int seekbar_value = 0x7f0901ad;
        public static final int select_dialog_listview = 0x7f0901ae;
        public static final int selected = 0x7f0901af;
        public static final int selelayout = 0x7f0901b0;
        public static final int settings = 0x7f0901b1;
        public static final int sharedValueSet = 0x7f0901b2;
        public static final int sharedValueUnset = 0x7f0901b3;
        public static final int shortcut = 0x7f0901b4;
        public static final int showCustom = 0x7f0901b5;
        public static final int showHome = 0x7f0901b6;
        public static final int showTitle = 0x7f0901b7;
        public static final int show_address2 = 0x7f0901b8;
        public static final int show_jingweidu2 = 0x7f0901b9;
        public static final int sin = 0x7f0901ba;
        public static final int skipCollapsed = 0x7f0901bb;
        public static final int skipped = 0x7f0901bc;
        public static final int slide = 0x7f0901bd;
        public static final int smallLabel = 0x7f0901be;
        public static final int snackbar_action = 0x7f0901bf;
        public static final int snackbar_text = 0x7f0901c0;
        public static final int snap = 0x7f0901c1;
        public static final int snapMargins = 0x7f0901c2;
        public static final int south = 0x7f0901c3;
        public static final int spacer = 0x7f0901c4;
        public static final int special_effects_controller_view_tag = 0x7f0901c5;
        public static final int spinner = 0x7f0901c6;
        public static final int spline = 0x7f0901c7;
        public static final int split_action_bar = 0x7f0901c8;
        public static final int spread = 0x7f0901c9;
        public static final int spread_inside = 0x7f0901ca;
        public static final int spring = 0x7f0901cb;
        public static final int square = 0x7f0901cc;
        public static final int src_atop = 0x7f0901cd;
        public static final int src_in = 0x7f0901ce;
        public static final int src_over = 0x7f0901cf;
        public static final int standard = 0x7f0901d0;
        public static final int start = 0x7f0901d1;
        public static final int startHorizontal = 0x7f0901d2;
        public static final int startVertical = 0x7f0901d3;
        public static final int staticLayout = 0x7f0901d4;
        public static final int staticPostLayout = 0x7f0901d5;
        public static final int stop = 0x7f0901d6;
        public static final int stretch = 0x7f0901d7;
        public static final int submenuarrow = 0x7f0901d8;
        public static final int submit_area = 0x7f0901d9;
        public static final int sug_city = 0x7f0901da;
        public static final int sug_dis = 0x7f0901db;
        public static final int sug_key = 0x7f0901dc;
        public static final int sug_list = 0x7f0901dd;
        public static final int supportScrollUp = 0x7f0901de;
        public static final int switchWidget = 0x7f0901df;
        public static final int tabMode = 0x7f0901e0;
        public static final int tag_accessibility_actions = 0x7f0901e1;
        public static final int tag_accessibility_clickable_spans = 0x7f0901e2;
        public static final int tag_accessibility_heading = 0x7f0901e3;
        public static final int tag_accessibility_pane_title = 0x7f0901e4;
        public static final int tag_on_apply_window_listener = 0x7f0901e5;
        public static final int tag_on_receive_content_listener = 0x7f0901e6;
        public static final int tag_on_receive_content_mime_types = 0x7f0901e7;
        public static final int tag_screen_reader_focusable = 0x7f0901e8;
        public static final int tag_state_description = 0x7f0901e9;
        public static final int tag_transition_group = 0x7f0901ea;
        public static final int tag_unhandled_key_event_manager = 0x7f0901eb;
        public static final int tag_unhandled_key_listeners = 0x7f0901ec;
        public static final int tag_window_insets_animation_callback = 0x7f0901ed;
        public static final int test_checkbox_android_button_tint = 0x7f0901ee;
        public static final int test_checkbox_app_button_tint = 0x7f0901ef;
        public static final int test_radiobutton_android_button_tint = 0x7f0901f0;
        public static final int test_radiobutton_app_button_tint = 0x7f0901f1;
        public static final int text = 0x7f0901f2;
        public static final int text2 = 0x7f0901f3;
        public static final int textEnd = 0x7f0901f4;
        public static final int textSpacerNoButtons = 0x7f0901f5;
        public static final int textSpacerNoTitle = 0x7f0901f6;
        public static final int textStart = 0x7f0901f7;
        public static final int text_input_end_icon = 0x7f0901f8;
        public static final int text_input_start_icon = 0x7f0901f9;
        public static final int textinput_counter = 0x7f0901fa;
        public static final int textinput_error = 0x7f0901fb;
        public static final int textinput_helper_text = 0x7f0901fc;
        public static final int textinput_placeholder = 0x7f0901fd;
        public static final int textinput_prefix_text = 0x7f0901fe;
        public static final int textinput_suffix_text = 0x7f0901ff;
        public static final int tiaozhuan = 0x7f090200;
        public static final int time = 0x7f090201;
        public static final int title = 0x7f090202;
        public static final int titleDividerNoCustom = 0x7f090203;
        public static final int title_template = 0x7f090204;
        public static final int toGo = 0x7f090205;
        public static final int toggle = 0x7f090206;
        public static final int toolbar = 0x7f090207;
        public static final int top = 0x7f090208;
        public static final int topPanel = 0x7f090209;
        public static final int touch_outside = 0x7f09020a;
        public static final int transitionToEnd = 0x7f09020b;
        public static final int transitionToStart = 0x7f09020c;
        public static final int transition_current_scene = 0x7f09020d;
        public static final int transition_layout_save = 0x7f09020e;
        public static final int transition_position = 0x7f09020f;
        public static final int transition_scene_layoutid_cache = 0x7f090210;
        public static final int transition_transform = 0x7f090211;
        public static final int triangle = 0x7f090212;
        public static final int tvHis = 0x7f090213;
        public static final int tvN = 0x7f090214;
        public static final int tvPosition = 0x7f090215;
        public static final int tvSelect = 0x7f090216;
        public static final int tv_agree = 0x7f090217;
        public static final int tv_cancel = 0x7f090218;
        public static final int tv_cancle = 0x7f090219;
        public static final int tv_confirm = 0x7f09021a;
        public static final int tv_content = 0x7f09021b;
        public static final int tv_splash_tag = 0x7f09021c;
        public static final int tv_top = 0x7f09021d;
        public static final int unchecked = 0x7f09021e;
        public static final int uniform = 0x7f09021f;
        public static final int unlabeled = 0x7f090220;
        public static final int up = 0x7f090221;
        public static final int update_agree = 0x7f090222;
        public static final int update_commit = 0x7f090223;
        public static final int update_content = 0x7f090224;
        public static final int update_ignore = 0x7f090225;
        public static final int update_time = 0x7f090226;
        public static final int update_title = 0x7f090227;
        public static final int useLogo = 0x7f090228;
        public static final int user_icon = 0x7f090229;
        public static final int user_name = 0x7f09022a;
        public static final int vertical_only = 0x7f09022b;
        public static final int view_offset_helper = 0x7f09022c;
        public static final int view_transition = 0x7f09022d;
        public static final int view_tree_lifecycle_owner = 0x7f09022e;
        public static final int view_tree_on_back_pressed_dispatcher_owner = 0x7f09022f;
        public static final int view_tree_saved_state_registry_owner = 0x7f090230;
        public static final int view_tree_view_model_store_owner = 0x7f090231;
        public static final int visible = 0x7f090232;
        public static final int visible_removing_fragment_view_tag = 0x7f090233;
        public static final int west = 0x7f090234;
        public static final int withText = 0x7f090235;
        public static final int withinBounds = 0x7f090236;
        public static final int wrap = 0x7f090237;
        public static final int wrap_content = 0x7f090238;
        public static final int wrap_content_constrained = 0x7f090239;
        public static final int x_left = 0x7f09023a;
        public static final int x_right = 0x7f09023b;
        public static final int zero_corner_chip = 0x7f09023c;
        public static final int zoom_in = 0x7f09023d;
        public static final int zoom_out = 0x7f09023e;
    }

    public static final class integer {
        public static final int abc_config_activityDefaultDur = 0x7f0a0000;
        public static final int abc_config_activityShortDur = 0x7f0a0001;
        public static final int app_bar_elevation_anim_duration = 0x7f0a0002;
        public static final int bottom_sheet_slide_duration = 0x7f0a0003;
        public static final int cancel_button_image_alpha = 0x7f0a0004;
        public static final int config_tooltipAnimTime = 0x7f0a0005;
        public static final int design_snackbar_text_max_lines = 0x7f0a0006;
        public static final int design_tab_indicator_anim_duration_ms = 0x7f0a0007;
        public static final int hide_password_duration = 0x7f0a0008;
        public static final int mtrl_badge_max_character_count = 0x7f0a0009;
        public static final int mtrl_btn_anim_delay_ms = 0x7f0a000a;
        public static final int mtrl_btn_anim_duration_ms = 0x7f0a000b;
        public static final int mtrl_calendar_header_orientation = 0x7f0a000c;
        public static final int mtrl_calendar_selection_text_lines = 0x7f0a000d;
        public static final int mtrl_calendar_year_selector_span = 0x7f0a000e;
        public static final int mtrl_card_anim_delay_ms = 0x7f0a000f;
        public static final int mtrl_card_anim_duration_ms = 0x7f0a0010;
        public static final int mtrl_chip_anim_duration = 0x7f0a0011;
        public static final int mtrl_tab_indicator_anim_duration_ms = 0x7f0a0012;
        public static final int preferences_detail_pane_weight = 0x7f0a0013;
        public static final int preferences_header_pane_weight = 0x7f0a0014;
        public static final int show_password_duration = 0x7f0a0015;
        public static final int status_bar_notification_info_maxnum = 0x7f0a0016;
    }

    public static final class interpolator {
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000;
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003;
        public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004;
        public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005;
        public static final int fast_out_slow_in = 0x7f0b0006;
        public static final int mtrl_fast_out_linear_in = 0x7f0b0007;
        public static final int mtrl_fast_out_slow_in = 0x7f0b0008;
        public static final int mtrl_linear = 0x7f0b0009;
        public static final int mtrl_linear_out_slow_in = 0x7f0b000a;
    }

    public static final class layout {
        public static final int abc_action_bar_title_item = 0x7f0c0000;
        public static final int abc_action_bar_up_container = 0x7f0c0001;
        public static final int abc_action_menu_item_layout = 0x7f0c0002;
        public static final int abc_action_menu_layout = 0x7f0c0003;
        public static final int abc_action_mode_bar = 0x7f0c0004;
        public static final int abc_action_mode_close_item_material = 0x7f0c0005;
        public static final int abc_activity_chooser_view = 0x7f0c0006;
        public static final int abc_activity_chooser_view_list_item = 0x7f0c0007;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0c0008;
        public static final int abc_alert_dialog_material = 0x7f0c0009;
        public static final int abc_alert_dialog_title_material = 0x7f0c000a;
        public static final int abc_cascading_menu_item_layout = 0x7f0c000b;
        public static final int abc_dialog_title_material = 0x7f0c000c;
        public static final int abc_expanded_menu_layout = 0x7f0c000d;
        public static final int abc_list_menu_item_checkbox = 0x7f0c000e;
        public static final int abc_list_menu_item_icon = 0x7f0c000f;
        public static final int abc_list_menu_item_layout = 0x7f0c0010;
        public static final int abc_list_menu_item_radio = 0x7f0c0011;
        public static final int abc_popup_menu_header_item_layout = 0x7f0c0012;
        public static final int abc_popup_menu_item_layout = 0x7f0c0013;
        public static final int abc_screen_content_include = 0x7f0c0014;
        public static final int abc_screen_simple = 0x7f0c0015;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0c0016;
        public static final int abc_screen_toolbar = 0x7f0c0017;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0c0018;
        public static final int abc_search_view = 0x7f0c0019;
        public static final int abc_select_dialog_material = 0x7f0c001a;
        public static final int abc_tooltip = 0x7f0c001b;
        public static final int activity_history = 0x7f0c001c;
        public static final int activity_home = 0x7f0c001d;
        public static final int activity_location_type2 = 0x7f0c001e;
        public static final int activity_login = 0x7f0c001f;
        public static final int activity_main = 0x7f0c0020;
        public static final int activity_select = 0x7f0c0021;
        public static final int activity_settings = 0x7f0c0022;
        public static final int activity_welcome = 0x7f0c0023;
        public static final int custom_dialog = 0x7f0c0024;
        public static final int design_bottom_navigation_item = 0x7f0c0025;
        public static final int design_bottom_sheet_dialog = 0x7f0c0026;
        public static final int design_layout_snackbar = 0x7f0c0027;
        public static final int design_layout_snackbar_include = 0x7f0c0028;
        public static final int design_layout_tab_icon = 0x7f0c0029;
        public static final int design_layout_tab_text = 0x7f0c002a;
        public static final int design_menu_item_action_area = 0x7f0c002b;
        public static final int design_navigation_item = 0x7f0c002c;
        public static final int design_navigation_item_header = 0x7f0c002d;
        public static final int design_navigation_item_separator = 0x7f0c002e;
        public static final int design_navigation_item_subheader = 0x7f0c002f;
        public static final int design_navigation_menu = 0x7f0c0030;
        public static final int design_navigation_menu_item = 0x7f0c0031;
        public static final int design_text_input_end_icon = 0x7f0c0032;
        public static final int design_text_input_start_icon = 0x7f0c0033;
        public static final int dialog_agreement = 0x7f0c0034;
        public static final int expand_button = 0x7f0c0035;
        public static final int history_item = 0x7f0c0036;
        public static final int image_frame = 0x7f0c0037;
        public static final int item_layout = 0x7f0c0038;
        public static final int joystick = 0x7f0c0039;
        public static final int joystick_button = 0x7f0c003a;
        public static final int joystick_history = 0x7f0c003b;
        public static final int joystick_map = 0x7f0c003c;
        public static final int location_input = 0x7f0c003d;
        public static final int location_poi_info = 0x7f0c003e;
        public static final int main_content = 0x7f0c003f;
        public static final int main_map = 0x7f0c0040;
        public static final int mtrl_alert_dialog = 0x7f0c0041;
        public static final int mtrl_alert_dialog_actions = 0x7f0c0042;
        public static final int mtrl_alert_dialog_title = 0x7f0c0043;
        public static final int mtrl_alert_select_dialog_item = 0x7f0c0044;
        public static final int mtrl_alert_select_dialog_multichoice = 0x7f0c0045;
        public static final int mtrl_alert_select_dialog_singlechoice = 0x7f0c0046;
        public static final int mtrl_calendar_day = 0x7f0c0047;
        public static final int mtrl_calendar_day_of_week = 0x7f0c0048;
        public static final int mtrl_calendar_days_of_week = 0x7f0c0049;
        public static final int mtrl_calendar_horizontal = 0x7f0c004a;
        public static final int mtrl_calendar_month = 0x7f0c004b;
        public static final int mtrl_calendar_month_labeled = 0x7f0c004c;
        public static final int mtrl_calendar_month_navigation = 0x7f0c004d;
        public static final int mtrl_calendar_months = 0x7f0c004e;
        public static final int mtrl_calendar_vertical = 0x7f0c004f;
        public static final int mtrl_calendar_year = 0x7f0c0050;
        public static final int mtrl_layout_snackbar = 0x7f0c0051;
        public static final int mtrl_layout_snackbar_include = 0x7f0c0052;
        public static final int mtrl_picker_actions = 0x7f0c0053;
        public static final int mtrl_picker_dialog = 0x7f0c0054;
        public static final int mtrl_picker_fullscreen = 0x7f0c0055;
        public static final int mtrl_picker_header_dialog = 0x7f0c0056;
        public static final int mtrl_picker_header_fullscreen = 0x7f0c0057;
        public static final int mtrl_picker_header_selection_text = 0x7f0c0058;
        public static final int mtrl_picker_header_title_text = 0x7f0c0059;
        public static final int mtrl_picker_header_toggle = 0x7f0c005a;
        public static final int mtrl_picker_text_input_date = 0x7f0c005b;
        public static final int mtrl_picker_text_input_date_range = 0x7f0c005c;
        public static final int nav_header = 0x7f0c005d;
        public static final int notification_action = 0x7f0c005e;
        public static final int notification_action_tombstone = 0x7f0c005f;
        public static final int notification_template_custom_big = 0x7f0c0060;
        public static final int notification_template_icon_group = 0x7f0c0061;
        public static final int notification_template_part_chronometer = 0x7f0c0062;
        public static final int notification_template_part_time = 0x7f0c0063;
        public static final int preference = 0x7f0c0064;
        public static final int preference_category = 0x7f0c0065;
        public static final int preference_category_material = 0x7f0c0066;
        public static final int preference_dialog_edittext = 0x7f0c0067;
        public static final int preference_dropdown = 0x7f0c0068;
        public static final int preference_dropdown_material = 0x7f0c0069;
        public static final int preference_information = 0x7f0c006a;
        public static final int preference_information_material = 0x7f0c006b;
        public static final int preference_list_fragment = 0x7f0c006c;
        public static final int preference_material = 0x7f0c006d;
        public static final int preference_recyclerview = 0x7f0c006e;
        public static final int preference_widget_checkbox = 0x7f0c006f;
        public static final int preference_widget_seekbar = 0x7f0c0070;
        public static final int preference_widget_seekbar_material = 0x7f0c0071;
        public static final int preference_widget_switch = 0x7f0c0072;
        public static final int preference_widget_switch_compat = 0x7f0c0073;
        public static final int register = 0x7f0c0074;
        public static final int search_item = 0x7f0c0075;
        public static final int search_poi_item = 0x7f0c0076;
        public static final int select_dialog_item_material = 0x7f0c0077;
        public static final int select_dialog_multichoice_material = 0x7f0c0078;
        public static final int select_dialog_singlechoice_material = 0x7f0c0079;
        public static final int support_simple_spinner_dropdown_item = 0x7f0c007a;
        public static final int test_action_chip = 0x7f0c007b;
        public static final int test_chip_zero_corner_radius = 0x7f0c007c;
        public static final int test_design_checkbox = 0x7f0c007d;
        public static final int test_design_radiobutton = 0x7f0c007e;
        public static final int test_reflow_chipgroup = 0x7f0c007f;
        public static final int test_toolbar = 0x7f0c0080;
        public static final int test_toolbar_custom_background = 0x7f0c0081;
        public static final int test_toolbar_elevation = 0x7f0c0082;
        public static final int test_toolbar_surface = 0x7f0c0083;
        public static final int text_view_with_line_height_from_appearance = 0x7f0c0084;
        public static final int text_view_with_line_height_from_layout = 0x7f0c0085;
        public static final int text_view_with_line_height_from_style = 0x7f0c0086;
        public static final int text_view_with_theme_line_height = 0x7f0c0087;
        public static final int text_view_without_line_height = 0x7f0c0088;
        public static final int update = 0x7f0c0089;
        public static final int user_agreement = 0x7f0c008a;
        public static final int user_privacy = 0x7f0c008b;
    }

    public static final class menu {
        public static final int menu_history = 0x7f0d0000;
        public static final int menu_main = 0x7f0d0001;
        public static final int menu_nav = 0x7f0d0002;
    }

    public static final class mipmap {
        public static final int ic_launcher = 0x7f0e0000;
        public static final int zhixiang = 0x7f0e0001;
    }

    public static final class plurals {
        public static final int mtrl_badge_content_description = 0x7f0f0000;
    }

    public static final class string {
        public static final int abc_action_bar_home_description = 0x7f100000;
        public static final int abc_action_bar_up_description = 0x7f100001;
        public static final int abc_action_menu_overflow_description = 0x7f100002;
        public static final int abc_action_mode_done = 0x7f100003;
        public static final int abc_activity_chooser_view_see_all = 0x7f100004;
        public static final int abc_activitychooserview_choose_application = 0x7f100005;
        public static final int abc_capital_off = 0x7f100006;
        public static final int abc_capital_on = 0x7f100007;
        public static final int abc_menu_alt_shortcut_label = 0x7f100008;
        public static final int abc_menu_ctrl_shortcut_label = 0x7f100009;
        public static final int abc_menu_delete_shortcut_label = 0x7f10000a;
        public static final int abc_menu_enter_shortcut_label = 0x7f10000b;
        public static final int abc_menu_function_shortcut_label = 0x7f10000c;
        public static final int abc_menu_meta_shortcut_label = 0x7f10000d;
        public static final int abc_menu_shift_shortcut_label = 0x7f10000e;
        public static final int abc_menu_space_shortcut_label = 0x7f10000f;
        public static final int abc_menu_sym_shortcut_label = 0x7f100010;
        public static final int abc_prepend_shortcut_label = 0x7f100011;
        public static final int abc_search_hint = 0x7f100012;
        public static final int abc_searchview_description_clear = 0x7f100013;
        public static final int abc_searchview_description_query = 0x7f100014;
        public static final int abc_searchview_description_search = 0x7f100015;
        public static final int abc_searchview_description_submit = 0x7f100016;
        public static final int abc_searchview_description_voice = 0x7f100017;
        public static final int abc_shareactionprovider_share_with = 0x7f100018;
        public static final int abc_shareactionprovider_share_with_application = 0x7f100019;
        public static final int abc_toolbar_collapse_description = 0x7f10001a;
        public static final int ak = 0x7f10001b;
        public static final int androidx_startup = 0x7f10001c;
        public static final int app_agreement = 0x7f10001d;
        public static final int app_agreement_content = 0x7f10001e;
        public static final int app_agreement_privacy = 0x7f10001f;
        public static final int app_author = 0x7f100020;
        public static final int app_btn_agree = 0x7f100021;
        public static final int app_btn_disagree = 0x7f100022;
        public static final int app_email = 0x7f100023;
        public static final int app_error_agreement = 0x7f100024;
        public static final int app_error_code = 0x7f100025;
        public static final int app_error_dev = 0x7f100026;
        public static final int app_error_gps = 0x7f100027;
        public static final int app_error_input = 0x7f100028;
        public static final int app_error_input_null = 0x7f100029;
        public static final int app_error_latitude = 0x7f10002a;
        public static final int app_error_location = 0x7f10002b;
        public static final int app_error_longitude = 0x7f10002c;
        public static final int app_error_network = 0x7f10002d;
        public static final int app_error_param = 0x7f10002e;
        public static final int app_error_permission = 0x7f10002f;
        public static final int app_error_protocol = 0x7f100030;
        public static final int app_error_read = 0x7f100031;
        public static final int app_error_search = 0x7f100032;
        public static final int app_error_username = 0x7f100033;
        public static final int app_history = 0x7f100034;
        public static final int app_location_copy = 0x7f100035;
        public static final int app_location_ok = 0x7f100036;
        public static final int app_location_save = 0x7f100037;
        public static final int app_name = 0x7f100038;
        public static final int app_privacy = 0x7f100039;
        public static final int app_privacy_content = 0x7f10003a;
        public static final int app_search_null = 0x7f10003b;
        public static final int app_search_tips = 0x7f10003c;
        public static final int app_service_tips = 0x7f10003d;
        public static final int app_settings = 0x7f10003e;
        public static final int app_statement = 0x7f10003f;
        public static final int appbar_scrolling_view_behavior = 0x7f100040;
        public static final int bottom_sheet_behavior = 0x7f100041;
        public static final int character_counter_content_description = 0x7f100042;
        public static final int character_counter_overflowed_content_description = 0x7f100043;
        public static final int character_counter_pattern = 0x7f100044;
        public static final int chip_text = 0x7f100045;
        public static final int clear_text_end_icon_content_description = 0x7f100046;
        public static final int copy = 0x7f100047;
        public static final int cur_position = 0x7f100048;
        public static final int error_icon_content_description = 0x7f100049;
        public static final int expand_button_title = 0x7f10004a;
        public static final int exposed_dropdown_menu_content_description = 0x7f10004b;
        public static final int fab_transformation_scrim_behavior = 0x7f10004c;
        public static final int fab_transformation_sheet_behavior = 0x7f10004d;
        public static final int hide_bottom_view_on_scroll_behavior = 0x7f10004e;
        public static final int history_delete_error = 0x7f10004f;
        public static final int history_delete_ok = 0x7f100050;
        public static final int history_error_location = 0x7f100051;
        public static final int history_error_search = 0x7f100052;
        public static final int history_expiration = 0x7f100053;
        public static final int history_idle = 0x7f100054;
        public static final int history_location_default_name = 0x7f100055;
        public static final int icon_content_description = 0x7f100056;
        public static final int input_button = 0x7f100057;
        public static final int input_position_baidu = 0x7f100058;
        public static final int input_position_cancel = 0x7f100059;
        public static final int input_position_gps = 0x7f10005a;
        public static final int input_position_ok = 0x7f10005b;
        public static final int item_view_role_description = 0x7f10005c;
        public static final int joystick_bike = 0x7f10005d;
        public static final int joystick_history = 0x7f10005e;
        public static final int joystick_history_tips = 0x7f10005f;
        public static final int joystick_map = 0x7f100060;
        public static final int joystick_map_tips = 0x7f100061;
        public static final int joystick_move = 0x7f100062;
        public static final int joystick_run = 0x7f100063;
        public static final int joystick_walk = 0x7f100064;
        public static final int label_latitude = 0x7f100065;
        public static final int label_longitude = 0x7f100066;
        public static final int map_pic_normal = 0x7f100067;
        public static final int map_pic_sate = 0x7f100068;
        public static final int material_slider_range_end = 0x7f100069;
        public static final int material_slider_range_start = 0x7f10006a;
        public static final int mtrl_badge_numberless_content_description = 0x7f10006b;
        public static final int mtrl_chip_close_icon_content_description = 0x7f10006c;
        public static final int mtrl_exceed_max_badge_number_content_description = 0x7f10006d;
        public static final int mtrl_exceed_max_badge_number_suffix = 0x7f10006e;
        public static final int mtrl_picker_a11y_next_month = 0x7f10006f;
        public static final int mtrl_picker_a11y_prev_month = 0x7f100070;
        public static final int mtrl_picker_announce_current_selection = 0x7f100071;
        public static final int mtrl_picker_cancel = 0x7f100072;
        public static final int mtrl_picker_confirm = 0x7f100073;
        public static final int mtrl_picker_date_header_selected = 0x7f100074;
        public static final int mtrl_picker_date_header_title = 0x7f100075;
        public static final int mtrl_picker_date_header_unselected = 0x7f100076;
        public static final int mtrl_picker_day_of_week_column_header = 0x7f100077;
        public static final int mtrl_picker_invalid_format = 0x7f100078;
        public static final int mtrl_picker_invalid_format_example = 0x7f100079;
        public static final int mtrl_picker_invalid_format_use = 0x7f10007a;
        public static final int mtrl_picker_invalid_range = 0x7f10007b;
        public static final int mtrl_picker_navigate_to_year_description = 0x7f10007c;
        public static final int mtrl_picker_out_of_range = 0x7f10007d;
        public static final int mtrl_picker_range_header_only_end_selected = 0x7f10007e;
        public static final int mtrl_picker_range_header_only_start_selected = 0x7f10007f;
        public static final int mtrl_picker_range_header_selected = 0x7f100080;
        public static final int mtrl_picker_range_header_title = 0x7f100081;
        public static final int mtrl_picker_range_header_unselected = 0x7f100082;
        public static final int mtrl_picker_save = 0x7f100083;
        public static final int mtrl_picker_text_input_date_hint = 0x7f100084;
        public static final int mtrl_picker_text_input_date_range_end_hint = 0x7f100085;
        public static final int mtrl_picker_text_input_date_range_start_hint = 0x7f100086;
        public static final int mtrl_picker_text_input_day_abbr = 0x7f100087;
        public static final int mtrl_picker_text_input_month_abbr = 0x7f100088;
        public static final int mtrl_picker_text_input_year_abbr = 0x7f100089;
        public static final int mtrl_picker_toggle_to_calendar_input_mode = 0x7f10008a;
        public static final int mtrl_picker_toggle_to_day_selection = 0x7f10008b;
        public static final int mtrl_picker_toggle_to_text_input_mode = 0x7f10008c;
        public static final int mtrl_picker_toggle_to_year_selection = 0x7f10008d;
        public static final int nav_drawer_close = 0x7f10008e;
        public static final int nav_drawer_open = 0x7f10008f;
        public static final int nav_menu_contact = 0x7f100090;
        public static final int nav_menu_dev = 0x7f100091;
        public static final int nav_menu_feedback = 0x7f100092;
        public static final int nav_menu_history = 0x7f100093;
        public static final int nav_menu_more = 0x7f100094;
        public static final int nav_menu_settings = 0x7f100095;
        public static final int nav_menu_upgrade = 0x7f100096;
        public static final int nav_user_limit_info = 0x7f100097;
        public static final int nav_user_name = 0x7f100098;
        public static final int not_set = 0x7f100099;
        public static final int note_hide = 0x7f10009a;
        public static final int note_show = 0x7f10009b;
        public static final int password_toggle_content_description = 0x7f10009c;
        public static final int path_password_eye = 0x7f10009d;
        public static final int path_password_eye_mask_strike_through = 0x7f10009e;
        public static final int path_password_eye_mask_visible = 0x7f10009f;
        public static final int path_password_strike_through = 0x7f1000a0;
        public static final int preference_copied = 0x7f1000a1;
        public static final int register_cancel = 0x7f1000a2;
        public static final int register_check = 0x7f1000a3;
        public static final int register_limit = 0x7f1000a4;
        public static final int register_ok = 0x7f1000a5;
        public static final int register_response = 0x7f1000a6;
        public static final int register_tips = 0x7f1000a7;
        public static final int register_title = 0x7f1000a8;
        public static final int register_user_name = 0x7f1000a9;
        public static final int safecode = 0x7f1000aa;
        public static final int search_menu_title = 0x7f1000ab;
        public static final int setting_author = 0x7f1000ac;
        public static final int setting_bike = 0x7f1000ad;
        public static final int setting_bike_default = 0x7f1000ae;
        public static final int setting_current_value = 0x7f1000af;
        public static final int setting_group_about = 0x7f1000b0;
        public static final int setting_group_log = 0x7f1000b1;
        public static final int setting_group_move = 0x7f1000b2;
        public static final int setting_group_sys = 0x7f1000b3;
        public static final int setting_joystick = 0x7f1000b4;
        public static final int setting_joystick_tips = 0x7f1000b5;
        public static final int setting_log_off = 0x7f1000b6;
        public static final int setting_pos_history = 0x7f1000b7;
        public static final int setting_run = 0x7f1000b8;
        public static final int setting_run_default = 0x7f1000b9;
        public static final int setting_version = 0x7f1000ba;
        public static final int setting_walk = 0x7f1000bb;
        public static final int setting_walk_default = 0x7f1000bc;
        public static final int status_bar_notification_info_overflow = 0x7f1000bd;
        public static final int summary_collapsed_preference_list = 0x7f1000be;
        public static final int update_commit = 0x7f1000bf;
        public static final int update_download = 0x7f1000c0;
        public static final int update_downloading = 0x7f1000c1;
        public static final int update_ignore = 0x7f1000c2;
        public static final int update_last = 0x7f1000c3;
        public static final int update_time = 0x7f1000c4;
        public static final int update_title = 0x7f1000c5;
        public static final int v7_preference_off = 0x7f1000c6;
        public static final int v7_preference_on = 0x7f1000c7;
        public static final int welcome_btn_txt = 0x7f1000c8;
        public static final int zoom_in = 0x7f1000c9;
        public static final int zoom_out = 0x7f1000ca;
    }

    public static final class style {
        public static final int AlertDialog_AppCompat = 0x7f110000;
        public static final int AlertDialog_AppCompat_Light = 0x7f110001;
        public static final int AndroidThemeColorAccentYellow = 0x7f110002;
        public static final int Animation_AppCompat_Dialog = 0x7f110003;
        public static final int Animation_AppCompat_DropDownUp = 0x7f110004;
        public static final int Animation_AppCompat_Tooltip = 0x7f110005;
        public static final int Animation_Design_BottomSheetDialog = 0x7f110006;
        public static final int Animation_MaterialComponents_BottomSheetDialog = 0x7f110007;
        public static final int AppTheme = 0x7f110008;
        public static final int AppTheme_AppBarOverlay = 0x7f110009;
        public static final int AppTheme_Button = 0x7f11000a;
        public static final int AppTheme_NoActionBar = 0x7f11000b;
        public static final int AppTheme_PopupOverlay = 0x7f11000c;
        public static final int Base_AlertDialog_AppCompat = 0x7f11000d;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f11000e;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f11000f;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f110010;
        public static final int Base_Animation_AppCompat_Tooltip = 0x7f110011;
        public static final int Base_CardView = 0x7f110012;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f110013;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f110014;
        public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f110015;
        public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f110016;
        public static final int Base_MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f110017;
        public static final int Base_TextAppearance_AppCompat = 0x7f110018;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f110019;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f11001a;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f11001b;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f11001c;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f11001d;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f11001e;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f11001f;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f110020;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f110021;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f110022;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f110023;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f110024;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f110025;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f110026;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f110027;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f110028;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f110029;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f11002a;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f11002b;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f11002c;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f11002d;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f11002e;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f11002f;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f110030;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f110031;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f110032;
        public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f110033;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f110034;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f110035;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f110036;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f110037;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f110038;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f110039;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f11003a;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f11003b;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f11003c;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f11003d;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f11003e;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f11003f;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f110040;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f110041;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f110042;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f110043;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f110044;
        public static final int Base_TextAppearance_MaterialComponents_Badge = 0x7f110045;
        public static final int Base_TextAppearance_MaterialComponents_Button = 0x7f110046;
        public static final int Base_TextAppearance_MaterialComponents_Headline6 = 0x7f110047;
        public static final int Base_TextAppearance_MaterialComponents_Subtitle2 = 0x7f110048;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f110049;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f11004a;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f11004b;
        public static final int Base_Theme_AppCompat = 0x7f11004c;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f11004d;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f11004e;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f11004f;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f110050;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f110051;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f110052;
        public static final int Base_Theme_AppCompat_Light = 0x7f110053;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f110054;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f110055;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f110056;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f110057;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f110058;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f110059;
        public static final int Base_Theme_MaterialComponents = 0x7f11005a;
        public static final int Base_Theme_MaterialComponents_Bridge = 0x7f11005b;
        public static final int Base_Theme_MaterialComponents_CompactMenu = 0x7f11005c;
        public static final int Base_Theme_MaterialComponents_Dialog = 0x7f11005d;
        public static final int Base_Theme_MaterialComponents_Dialog_Alert = 0x7f11005e;
        public static final int Base_Theme_MaterialComponents_Dialog_Bridge = 0x7f11005f;
        public static final int Base_Theme_MaterialComponents_Dialog_FixedSize = 0x7f110060;
        public static final int Base_Theme_MaterialComponents_Dialog_MinWidth = 0x7f110061;
        public static final int Base_Theme_MaterialComponents_DialogWhenLarge = 0x7f110062;
        public static final int Base_Theme_MaterialComponents_Light = 0x7f110063;
        public static final int Base_Theme_MaterialComponents_Light_Bridge = 0x7f110064;
        public static final int Base_Theme_MaterialComponents_Light_DarkActionBar = 0x7f110065;
        public static final int Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f110066;
        public static final int Base_Theme_MaterialComponents_Light_Dialog = 0x7f110067;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_Alert = 0x7f110068;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f110069;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f11006a;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f11006b;
        public static final int Base_Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f11006c;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f11006d;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f11006e;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f11006f;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f110070;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f110071;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f110072;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f110073;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog = 0x7f110074;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f110075;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework = 0x7f110076;
        public static final int Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework = 0x7f110077;
        public static final int Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f110078;
        public static final int Base_V14_Theme_MaterialComponents = 0x7f110079;
        public static final int Base_V14_Theme_MaterialComponents_Bridge = 0x7f11007a;
        public static final int Base_V14_Theme_MaterialComponents_Dialog = 0x7f11007b;
        public static final int Base_V14_Theme_MaterialComponents_Dialog_Bridge = 0x7f11007c;
        public static final int Base_V14_Theme_MaterialComponents_Light = 0x7f11007d;
        public static final int Base_V14_Theme_MaterialComponents_Light_Bridge = 0x7f11007e;
        public static final int Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f11007f;
        public static final int Base_V14_Theme_MaterialComponents_Light_Dialog = 0x7f110080;
        public static final int Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f110081;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog = 0x7f110082;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f110083;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f110084;
        public static final int Base_V21_Theme_AppCompat = 0x7f110085;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f110086;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f110087;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f110088;
        public static final int Base_V21_Theme_MaterialComponents = 0x7f110089;
        public static final int Base_V21_Theme_MaterialComponents_Dialog = 0x7f11008a;
        public static final int Base_V21_Theme_MaterialComponents_Light = 0x7f11008b;
        public static final int Base_V21_Theme_MaterialComponents_Light_Dialog = 0x7f11008c;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f11008d;
        public static final int Base_V22_Theme_AppCompat = 0x7f11008e;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f11008f;
        public static final int Base_V23_Theme_AppCompat = 0x7f110090;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f110091;
        public static final int Base_V26_Theme_AppCompat = 0x7f110092;
        public static final int Base_V26_Theme_AppCompat_Light = 0x7f110093;
        public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f110094;
        public static final int Base_V28_Theme_AppCompat = 0x7f110095;
        public static final int Base_V28_Theme_AppCompat_Light = 0x7f110096;
        public static final int Base_V7_Theme_AppCompat = 0x7f110097;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f110098;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f110099;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f11009a;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f11009b;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f11009c;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f11009d;
        public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f11009e;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f11009f;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1100a0;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1100a1;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1100a2;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1100a3;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f1100a4;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1100a5;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1100a6;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f1100a7;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f1100a8;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1100a9;
        public static final int Base_Widget_AppCompat_Button = 0x7f1100aa;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f1100ab;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1100ac;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1100ad;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f1100ae;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f1100af;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f1100b0;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1100b1;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1100b2;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1100b3;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1100b4;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1100b5;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1100b6;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1100b7;
        public static final int Base_Widget_AppCompat_EditText = 0x7f1100b8;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f1100b9;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f1100ba;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1100bb;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1100bc;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1100bd;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1100be;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1100bf;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1100c0;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1100c1;
        public static final int Base_Widget_AppCompat_ListMenuView = 0x7f1100c2;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f1100c3;
        public static final int Base_Widget_AppCompat_ListView = 0x7f1100c4;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f1100c5;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f1100c6;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f1100c7;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1100c8;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f1100c9;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f1100ca;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1100cb;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f1100cc;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1100cd;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f1100ce;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f1100cf;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1100d0;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f1100d1;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1100d2;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f1100d3;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1100d4;
        public static final int Base_Widget_AppCompat_TextView = 0x7f1100d5;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1100d6;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f1100d7;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1100d8;
        public static final int Base_Widget_Design_TabLayout = 0x7f1100d9;
        public static final int Base_Widget_MaterialComponents_AutoCompleteTextView = 0x7f1100da;
        public static final int Base_Widget_MaterialComponents_CheckedTextView = 0x7f1100db;
        public static final int Base_Widget_MaterialComponents_Chip = 0x7f1100dc;
        public static final int Base_Widget_MaterialComponents_PopupMenu = 0x7f1100dd;
        public static final int Base_Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f1100de;
        public static final int Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f1100df;
        public static final int Base_Widget_MaterialComponents_PopupMenu_Overflow = 0x7f1100e0;
        public static final int Base_Widget_MaterialComponents_Slider = 0x7f1100e1;
        public static final int Base_Widget_MaterialComponents_TextInputEditText = 0x7f1100e2;
        public static final int Base_Widget_MaterialComponents_TextInputLayout = 0x7f1100e3;
        public static final int Base_Widget_MaterialComponents_TextView = 0x7f1100e4;
        public static final int BasePreferenceThemeOverlay = 0x7f1100e5;
        public static final int CardView = 0x7f1100e6;
        public static final int CardView_Dark = 0x7f1100e7;
        public static final int CardView_Light = 0x7f1100e8;
        public static final int DialogAnimFadeInFadeOut = 0x7f1100e9;
        public static final int DialogBaseAnimation = 0x7f1100ea;
        public static final int EmptyTheme = 0x7f1100eb;
        public static final int MaterialAlertDialog_MaterialComponents = 0x7f1100ec;
        public static final int MaterialAlertDialog_MaterialComponents_Body_Text = 0x7f1100ed;
        public static final int MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar = 0x7f1100ee;
        public static final int MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner = 0x7f1100ef;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f1100f0;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked = 0x7f1100f1;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f1100f2;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked = 0x7f1100f3;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f1100f4;
        public static final int MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked = 0x7f1100f5;
        public static final int Platform_AppCompat = 0x7f1100f6;
        public static final int Platform_AppCompat_Light = 0x7f1100f7;
        public static final int Platform_MaterialComponents = 0x7f1100f8;
        public static final int Platform_MaterialComponents_Dialog = 0x7f1100f9;
        public static final int Platform_MaterialComponents_Light = 0x7f1100fa;
        public static final int Platform_MaterialComponents_Light_Dialog = 0x7f1100fb;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f1100fc;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f1100fd;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f1100fe;
        public static final int Platform_V21_AppCompat = 0x7f1100ff;
        public static final int Platform_V21_AppCompat_Light = 0x7f110100;
        public static final int Platform_V25_AppCompat = 0x7f110101;
        public static final int Platform_V25_AppCompat_Light = 0x7f110102;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f110103;
        public static final int Preference = 0x7f110104;
        public static final int Preference_Category = 0x7f110105;
        public static final int Preference_Category_Material = 0x7f110106;
        public static final int Preference_CheckBoxPreference = 0x7f110107;
        public static final int Preference_CheckBoxPreference_Material = 0x7f110108;
        public static final int Preference_DialogPreference = 0x7f110109;
        public static final int Preference_DialogPreference_EditTextPreference = 0x7f11010a;
        public static final int Preference_DialogPreference_EditTextPreference_Material = 0x7f11010b;
        public static final int Preference_DialogPreference_Material = 0x7f11010c;
        public static final int Preference_DropDown = 0x7f11010d;
        public static final int Preference_DropDown_Material = 0x7f11010e;
        public static final int Preference_Information = 0x7f11010f;
        public static final int Preference_Information_Material = 0x7f110110;
        public static final int Preference_Material = 0x7f110111;
        public static final int Preference_PreferenceScreen = 0x7f110112;
        public static final int Preference_PreferenceScreen_Material = 0x7f110113;
        public static final int Preference_SeekBarPreference = 0x7f110114;
        public static final int Preference_SeekBarPreference_Material = 0x7f110115;
        public static final int Preference_SwitchPreference = 0x7f110116;
        public static final int Preference_SwitchPreference_Material = 0x7f110117;
        public static final int Preference_SwitchPreferenceCompat = 0x7f110118;
        public static final int Preference_SwitchPreferenceCompat_Material = 0x7f110119;
        public static final int PreferenceCategoryTitleTextStyle = 0x7f11011a;
        public static final int PreferenceFragment = 0x7f11011b;
        public static final int PreferenceFragment_Material = 0x7f11011c;
        public static final int PreferenceFragmentList = 0x7f11011d;
        public static final int PreferenceFragmentList_Material = 0x7f11011e;
        public static final int PreferenceSummaryTextStyle = 0x7f11011f;
        public static final int PreferenceThemeOverlay = 0x7f110120;
        public static final int PreferenceThemeOverlay_v14 = 0x7f110121;
        public static final int PreferenceThemeOverlay_v14_Material = 0x7f110122;
        public static final int RippleWhite = 0x7f110123;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f110124;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f110125;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f110126;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f110127;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f110128;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f110129;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f11012a;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f11012b;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f11012c;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f11012d;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f11012e;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f11012f;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f110130;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f110131;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f110132;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f110133;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f110134;
        public static final int ShapeAppearance_MaterialComponents = 0x7f110135;
        public static final int ShapeAppearance_MaterialComponents_LargeComponent = 0x7f110136;
        public static final int ShapeAppearance_MaterialComponents_MediumComponent = 0x7f110137;
        public static final int ShapeAppearance_MaterialComponents_SmallComponent = 0x7f110138;
        public static final int ShapeAppearance_MaterialComponents_Test = 0x7f110139;
        public static final int ShapeAppearance_MaterialComponents_Tooltip = 0x7f11013a;
        public static final int ShapeAppearanceOverlay = 0x7f11013b;
        public static final int ShapeAppearanceOverlay_BottomLeftDifferentCornerSize = 0x7f11013c;
        public static final int ShapeAppearanceOverlay_BottomRightCut = 0x7f11013d;
        public static final int ShapeAppearanceOverlay_Cut = 0x7f11013e;
        public static final int ShapeAppearanceOverlay_DifferentCornerSize = 0x7f11013f;
        public static final int ShapeAppearanceOverlay_MaterialComponents_BottomSheet = 0x7f110140;
        public static final int ShapeAppearanceOverlay_MaterialComponents_Chip = 0x7f110141;
        public static final int ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton = 0x7f110142;
        public static final int ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton = 0x7f110143;
        public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f110144;
        public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen = 0x7f110145;
        public static final int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year = 0x7f110146;
        public static final int ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox = 0x7f110147;
        public static final int ShapeAppearanceOverlay_TopLeftCut = 0x7f110148;
        public static final int ShapeAppearanceOverlay_TopRightDifferentCornerSize = 0x7f110149;
        public static final int Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f11014a;
        public static final int Test_Theme_MaterialComponents_MaterialCalendar = 0x7f11014b;
        public static final int Test_Widget_MaterialComponents_MaterialCalendar = 0x7f11014c;
        public static final int Test_Widget_MaterialComponents_MaterialCalendar_Day = 0x7f11014d;
        public static final int Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f11014e;
        public static final int TestStyleWithLineHeight = 0x7f11014f;
        public static final int TestStyleWithLineHeightAppearance = 0x7f110150;
        public static final int TestStyleWithThemeLineHeightAttribute = 0x7f110151;
        public static final int TestStyleWithoutLineHeight = 0x7f110152;
        public static final int TestThemeWithLineHeight = 0x7f110153;
        public static final int TestThemeWithLineHeightDisabled = 0x7f110154;
        public static final int TextAppearance_AppCompat = 0x7f110155;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f110156;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f110157;
        public static final int TextAppearance_AppCompat_Button = 0x7f110158;
        public static final int TextAppearance_AppCompat_Caption = 0x7f110159;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f11015a;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f11015b;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f11015c;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f11015d;
        public static final int TextAppearance_AppCompat_Headline = 0x7f11015e;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f11015f;
        public static final int TextAppearance_AppCompat_Large = 0x7f110160;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f110161;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f110162;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f110163;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f110164;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f110165;
        public static final int TextAppearance_AppCompat_Medium = 0x7f110166;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f110167;
        public static final int TextAppearance_AppCompat_Menu = 0x7f110168;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f110169;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f11016a;
        public static final int TextAppearance_AppCompat_Small = 0x7f11016b;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f11016c;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f11016d;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f11016e;
        public static final int TextAppearance_AppCompat_Title = 0x7f11016f;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f110170;
        public static final int TextAppearance_AppCompat_Tooltip = 0x7f110171;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f110172;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f110173;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f110174;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f110175;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f110176;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f110177;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f110178;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f110179;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f11017a;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f11017b;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f11017c;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f11017d;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f11017e;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f11017f;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f110180;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f110181;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f110182;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f110183;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f110184;
        public static final int TextAppearance_Compat_Notification = 0x7f110185;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f110186;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f110187;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f110188;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f110189;
        public static final int TextAppearance_Design_CollapsingToolbar_Expanded = 0x7f11018a;
        public static final int TextAppearance_Design_Counter = 0x7f11018b;
        public static final int TextAppearance_Design_Counter_Overflow = 0x7f11018c;
        public static final int TextAppearance_Design_Error = 0x7f11018d;
        public static final int TextAppearance_Design_HelperText = 0x7f11018e;
        public static final int TextAppearance_Design_Hint = 0x7f11018f;
        public static final int TextAppearance_Design_Placeholder = 0x7f110190;
        public static final int TextAppearance_Design_Prefix = 0x7f110191;
        public static final int TextAppearance_Design_Snackbar_Message = 0x7f110192;
        public static final int TextAppearance_Design_Suffix = 0x7f110193;
        public static final int TextAppearance_Design_Tab = 0x7f110194;
        public static final int TextAppearance_MaterialComponents_Badge = 0x7f110195;
        public static final int TextAppearance_MaterialComponents_Body1 = 0x7f110196;
        public static final int TextAppearance_MaterialComponents_Body2 = 0x7f110197;
        public static final int TextAppearance_MaterialComponents_Button = 0x7f110198;
        public static final int TextAppearance_MaterialComponents_Caption = 0x7f110199;
        public static final int TextAppearance_MaterialComponents_Chip = 0x7f11019a;
        public static final int TextAppearance_MaterialComponents_Headline1 = 0x7f11019b;
        public static final int TextAppearance_MaterialComponents_Headline2 = 0x7f11019c;
        public static final int TextAppearance_MaterialComponents_Headline3 = 0x7f11019d;
        public static final int TextAppearance_MaterialComponents_Headline4 = 0x7f11019e;
        public static final int TextAppearance_MaterialComponents_Headline5 = 0x7f11019f;
        public static final int TextAppearance_MaterialComponents_Headline6 = 0x7f1101a0;
        public static final int TextAppearance_MaterialComponents_Overline = 0x7f1101a1;
        public static final int TextAppearance_MaterialComponents_Subtitle1 = 0x7f1101a2;
        public static final int TextAppearance_MaterialComponents_Subtitle2 = 0x7f1101a3;
        public static final int TextAppearance_MaterialComponents_Tooltip = 0x7f1101a4;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f1101a5;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f1101a6;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f1101a7;
        public static final int Theme_AppCompat = 0x7f1101a8;
        public static final int Theme_AppCompat_CompactMenu = 0x7f1101a9;
        public static final int Theme_AppCompat_DayNight = 0x7f1101aa;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f1101ab;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f1101ac;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f1101ad;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f1101ae;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f1101af;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f1101b0;
        public static final int Theme_AppCompat_Dialog = 0x7f1101b1;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f1101b2;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f1101b3;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f1101b4;
        public static final int Theme_AppCompat_Empty = 0x7f1101b5;
        public static final int Theme_AppCompat_Light = 0x7f1101b6;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f1101b7;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f1101b8;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f1101b9;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f1101ba;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f1101bb;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f1101bc;
        public static final int Theme_AppCompat_NoActionBar = 0x7f1101bd;
        public static final int Theme_Design = 0x7f1101be;
        public static final int Theme_Design_BottomSheetDialog = 0x7f1101bf;
        public static final int Theme_Design_Light = 0x7f1101c0;
        public static final int Theme_Design_Light_BottomSheetDialog = 0x7f1101c1;
        public static final int Theme_Design_Light_NoActionBar = 0x7f1101c2;
        public static final int Theme_Design_NoActionBar = 0x7f1101c3;
        public static final int Theme_MaterialComponents = 0x7f1101c4;
        public static final int Theme_MaterialComponents_BottomSheetDialog = 0x7f1101c5;
        public static final int Theme_MaterialComponents_Bridge = 0x7f1101c6;
        public static final int Theme_MaterialComponents_CompactMenu = 0x7f1101c7;
        public static final int Theme_MaterialComponents_DayNight = 0x7f1101c8;
        public static final int Theme_MaterialComponents_DayNight_BottomSheetDialog = 0x7f1101c9;
        public static final int Theme_MaterialComponents_DayNight_Bridge = 0x7f1101ca;
        public static final int Theme_MaterialComponents_DayNight_DarkActionBar = 0x7f1101cb;
        public static final int Theme_MaterialComponents_DayNight_DarkActionBar_Bridge = 0x7f1101cc;
        public static final int Theme_MaterialComponents_DayNight_Dialog = 0x7f1101cd;
        public static final int Theme_MaterialComponents_DayNight_Dialog_Alert = 0x7f1101ce;
        public static final int Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge = 0x7f1101cf;
        public static final int Theme_MaterialComponents_DayNight_Dialog_Bridge = 0x7f1101d0;
        public static final int Theme_MaterialComponents_DayNight_Dialog_FixedSize = 0x7f1101d1;
        public static final int Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge = 0x7f1101d2;
        public static final int Theme_MaterialComponents_DayNight_Dialog_MinWidth = 0x7f1101d3;
        public static final int Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge = 0x7f1101d4;
        public static final int Theme_MaterialComponents_DayNight_DialogWhenLarge = 0x7f1101d5;
        public static final int Theme_MaterialComponents_DayNight_NoActionBar = 0x7f1101d6;
        public static final int Theme_MaterialComponents_DayNight_NoActionBar_Bridge = 0x7f1101d7;
        public static final int Theme_MaterialComponents_Dialog = 0x7f1101d8;
        public static final int Theme_MaterialComponents_Dialog_Alert = 0x7f1101d9;
        public static final int Theme_MaterialComponents_Dialog_Alert_Bridge = 0x7f1101da;
        public static final int Theme_MaterialComponents_Dialog_Bridge = 0x7f1101db;
        public static final int Theme_MaterialComponents_Dialog_FixedSize = 0x7f1101dc;
        public static final int Theme_MaterialComponents_Dialog_FixedSize_Bridge = 0x7f1101dd;
        public static final int Theme_MaterialComponents_Dialog_MinWidth = 0x7f1101de;
        public static final int Theme_MaterialComponents_Dialog_MinWidth_Bridge = 0x7f1101df;
        public static final int Theme_MaterialComponents_DialogWhenLarge = 0x7f1101e0;
        public static final int Theme_MaterialComponents_Light = 0x7f1101e1;
        public static final int Theme_MaterialComponents_Light_BarSize = 0x7f1101e2;
        public static final int Theme_MaterialComponents_Light_BottomSheetDialog = 0x7f1101e3;
        public static final int Theme_MaterialComponents_Light_Bridge = 0x7f1101e4;
        public static final int Theme_MaterialComponents_Light_DarkActionBar = 0x7f1101e5;
        public static final int Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f1101e6;
        public static final int Theme_MaterialComponents_Light_Dialog = 0x7f1101e7;
        public static final int Theme_MaterialComponents_Light_Dialog_Alert = 0x7f1101e8;
        public static final int Theme_MaterialComponents_Light_Dialog_Alert_Bridge = 0x7f1101e9;
        public static final int Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f1101ea;
        public static final int Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f1101eb;
        public static final int Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge = 0x7f1101ec;
        public static final int Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f1101ed;
        public static final int Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge = 0x7f1101ee;
        public static final int Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f1101ef;
        public static final int Theme_MaterialComponents_Light_LargeTouch = 0x7f1101f0;
        public static final int Theme_MaterialComponents_Light_NoActionBar = 0x7f1101f1;
        public static final int Theme_MaterialComponents_Light_NoActionBar_Bridge = 0x7f1101f2;
        public static final int Theme_MaterialComponents_NoActionBar = 0x7f1101f3;
        public static final int Theme_MaterialComponents_NoActionBar_Bridge = 0x7f1101f4;
        public static final int ThemeOverlay_AppCompat = 0x7f1101f5;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f1101f6;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f1101f7;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f1101f8;
        public static final int ThemeOverlay_AppCompat_DayNight = 0x7f1101f9;
        public static final int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f1101fa;
        public static final int ThemeOverlay_AppCompat_Dialog = 0x7f1101fb;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f1101fc;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f1101fd;
        public static final int ThemeOverlay_Design_TextInputEditText = 0x7f1101fe;
        public static final int ThemeOverlay_MaterialComponents = 0x7f1101ff;
        public static final int ThemeOverlay_MaterialComponents_ActionBar = 0x7f110200;
        public static final int ThemeOverlay_MaterialComponents_ActionBar_Primary = 0x7f110201;
        public static final int ThemeOverlay_MaterialComponents_ActionBar_Surface = 0x7f110202;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView = 0x7f110203;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f110204;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f110205;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f110206;
        public static final int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f110207;
        public static final int ThemeOverlay_MaterialComponents_BottomAppBar_Primary = 0x7f110208;
        public static final int ThemeOverlay_MaterialComponents_BottomAppBar_Surface = 0x7f110209;
        public static final int ThemeOverlay_MaterialComponents_BottomSheetDialog = 0x7f11020a;
        public static final int ThemeOverlay_MaterialComponents_Dark = 0x7f11020b;
        public static final int ThemeOverlay_MaterialComponents_Dark_ActionBar = 0x7f11020c;
        public static final int ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog = 0x7f11020d;
        public static final int ThemeOverlay_MaterialComponents_Dialog = 0x7f11020e;
        public static final int ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f11020f;
        public static final int ThemeOverlay_MaterialComponents_Dialog_Alert_Framework = 0x7f110210;
        public static final int ThemeOverlay_MaterialComponents_Light = 0x7f110211;
        public static final int ThemeOverlay_MaterialComponents_Light_BottomSheetDialog = 0x7f110212;
        public static final int ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework = 0x7f110213;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f110214;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered = 0x7f110215;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date = 0x7f110216;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar = 0x7f110217;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text = 0x7f110218;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day = 0x7f110219;
        public static final int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner = 0x7f11021a;
        public static final int ThemeOverlay_MaterialComponents_MaterialCalendar = 0x7f11021b;
        public static final int ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f11021c;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText = 0x7f11021d;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox = 0x7f11021e;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f11021f;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f110220;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f110221;
        public static final int ThemeOverlay_MaterialComponents_Toolbar_Primary = 0x7f110222;
        public static final int ThemeOverlay_MaterialComponents_Toolbar_Surface = 0x7f110223;
        public static final int ThemeOverlayColorAccentRed = 0x7f110224;
        public static final int Widget_AppCompat_ActionBar = 0x7f110225;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f110226;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f110227;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f110228;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f110229;
        public static final int Widget_AppCompat_ActionButton = 0x7f11022a;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f11022b;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f11022c;
        public static final int Widget_AppCompat_ActionMode = 0x7f11022d;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f11022e;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f11022f;
        public static final int Widget_AppCompat_Button = 0x7f110230;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f110231;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f110232;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f110233;
        public static final int Widget_AppCompat_Button_Colored = 0x7f110234;
        public static final int Widget_AppCompat_Button_Small = 0x7f110235;
        public static final int Widget_AppCompat_ButtonBar = 0x7f110236;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f110237;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f110238;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f110239;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f11023a;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f11023b;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f11023c;
        public static final int Widget_AppCompat_EditText = 0x7f11023d;
        public static final int Widget_AppCompat_ImageButton = 0x7f11023e;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f11023f;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f110240;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f110241;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f110242;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f110243;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f110244;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f110245;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f110246;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f110247;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f110248;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f110249;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f11024a;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f11024b;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f11024c;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f11024d;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f11024e;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f11024f;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f110250;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f110251;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f110252;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f110253;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f110254;
        public static final int Widget_AppCompat_ListMenuView = 0x7f110255;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f110256;
        public static final int Widget_AppCompat_ListView = 0x7f110257;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f110258;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f110259;
        public static final int Widget_AppCompat_PopupMenu = 0x7f11025a;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f11025b;
        public static final int Widget_AppCompat_PopupWindow = 0x7f11025c;
        public static final int Widget_AppCompat_ProgressBar = 0x7f11025d;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f11025e;
        public static final int Widget_AppCompat_RatingBar = 0x7f11025f;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f110260;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f110261;
        public static final int Widget_AppCompat_SearchView = 0x7f110262;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f110263;
        public static final int Widget_AppCompat_SeekBar = 0x7f110264;
        public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f110265;
        public static final int Widget_AppCompat_Spinner = 0x7f110266;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f110267;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f110268;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f110269;
        public static final int Widget_AppCompat_TextView = 0x7f11026a;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f11026b;
        public static final int Widget_AppCompat_Toolbar = 0x7f11026c;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f11026d;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f11026e;
        public static final int Widget_Compat_NotificationActionText = 0x7f11026f;
        public static final int Widget_Design_AppBarLayout = 0x7f110270;
        public static final int Widget_Design_BottomNavigationView = 0x7f110271;
        public static final int Widget_Design_BottomSheet_Modal = 0x7f110272;
        public static final int Widget_Design_CollapsingToolbar = 0x7f110273;
        public static final int Widget_Design_FloatingActionButton = 0x7f110274;
        public static final int Widget_Design_NavigationView = 0x7f110275;
        public static final int Widget_Design_ScrimInsetsFrameLayout = 0x7f110276;
        public static final int Widget_Design_Snackbar = 0x7f110277;
        public static final int Widget_Design_TabLayout = 0x7f110278;
        public static final int Widget_Design_TextInputEditText = 0x7f110279;
        public static final int Widget_Design_TextInputLayout = 0x7f11027a;
        public static final int Widget_MaterialComponents_ActionBar_Primary = 0x7f11027b;
        public static final int Widget_MaterialComponents_ActionBar_PrimarySurface = 0x7f11027c;
        public static final int Widget_MaterialComponents_ActionBar_Solid = 0x7f11027d;
        public static final int Widget_MaterialComponents_ActionBar_Surface = 0x7f11027e;
        public static final int Widget_MaterialComponents_AppBarLayout_Primary = 0x7f11027f;
        public static final int Widget_MaterialComponents_AppBarLayout_PrimarySurface = 0x7f110280;
        public static final int Widget_MaterialComponents_AppBarLayout_Surface = 0x7f110281;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f110282;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f110283;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f110284;
        public static final int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f110285;
        public static final int Widget_MaterialComponents_Badge = 0x7f110286;
        public static final int Widget_MaterialComponents_BottomAppBar = 0x7f110287;
        public static final int Widget_MaterialComponents_BottomAppBar_Colored = 0x7f110288;
        public static final int Widget_MaterialComponents_BottomAppBar_PrimarySurface = 0x7f110289;
        public static final int Widget_MaterialComponents_BottomNavigationView = 0x7f11028a;
        public static final int Widget_MaterialComponents_BottomNavigationView_Colored = 0x7f11028b;
        public static final int Widget_MaterialComponents_BottomNavigationView_PrimarySurface = 0x7f11028c;
        public static final int Widget_MaterialComponents_BottomSheet = 0x7f11028d;
        public static final int Widget_MaterialComponents_BottomSheet_Modal = 0x7f11028e;
        public static final int Widget_MaterialComponents_Button = 0x7f11028f;
        public static final int Widget_MaterialComponents_Button_Icon = 0x7f110290;
        public static final int Widget_MaterialComponents_Button_OutlinedButton = 0x7f110291;
        public static final int Widget_MaterialComponents_Button_OutlinedButton_Icon = 0x7f110292;
        public static final int Widget_MaterialComponents_Button_TextButton = 0x7f110293;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog = 0x7f110294;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog_Flush = 0x7f110295;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog_Icon = 0x7f110296;
        public static final int Widget_MaterialComponents_Button_TextButton_Icon = 0x7f110297;
        public static final int Widget_MaterialComponents_Button_TextButton_Snackbar = 0x7f110298;
        public static final int Widget_MaterialComponents_Button_UnelevatedButton = 0x7f110299;
        public static final int Widget_MaterialComponents_Button_UnelevatedButton_Icon = 0x7f11029a;
        public static final int Widget_MaterialComponents_CardView = 0x7f11029b;
        public static final int Widget_MaterialComponents_CheckedTextView = 0x7f11029c;
        public static final int Widget_MaterialComponents_Chip_Action = 0x7f11029d;
        public static final int Widget_MaterialComponents_Chip_Choice = 0x7f11029e;
        public static final int Widget_MaterialComponents_Chip_Entry = 0x7f11029f;
        public static final int Widget_MaterialComponents_Chip_Filter = 0x7f1102a0;
        public static final int Widget_MaterialComponents_ChipGroup = 0x7f1102a1;
        public static final int Widget_MaterialComponents_CompoundButton_CheckBox = 0x7f1102a2;
        public static final int Widget_MaterialComponents_CompoundButton_RadioButton = 0x7f1102a3;
        public static final int Widget_MaterialComponents_CompoundButton_Switch = 0x7f1102a4;
        public static final int Widget_MaterialComponents_ExtendedFloatingActionButton = 0x7f1102a5;
        public static final int Widget_MaterialComponents_ExtendedFloatingActionButton_Icon = 0x7f1102a6;
        public static final int Widget_MaterialComponents_FloatingActionButton = 0x7f1102a7;
        public static final int Widget_MaterialComponents_Light_ActionBar_Solid = 0x7f1102a8;
        public static final int Widget_MaterialComponents_MaterialButtonToggleGroup = 0x7f1102a9;
        public static final int Widget_MaterialComponents_MaterialCalendar = 0x7f1102aa;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day = 0x7f1102ab;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day_Invalid = 0x7f1102ac;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f1102ad;
        public static final int Widget_MaterialComponents_MaterialCalendar_Day_Today = 0x7f1102ae;
        public static final int Widget_MaterialComponents_MaterialCalendar_DayTextView = 0x7f1102af;
        public static final int Widget_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f1102b0;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton = 0x7f1102b1;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderDivider = 0x7f1102b2;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderLayout = 0x7f1102b3;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderSelection = 0x7f1102b4;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen = 0x7f1102b5;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderTitle = 0x7f1102b6;
        public static final int Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton = 0x7f1102b7;
        public static final int Widget_MaterialComponents_MaterialCalendar_Item = 0x7f1102b8;
        public static final int Widget_MaterialComponents_MaterialCalendar_Year = 0x7f1102b9;
        public static final int Widget_MaterialComponents_MaterialCalendar_Year_Selected = 0x7f1102ba;
        public static final int Widget_MaterialComponents_MaterialCalendar_Year_Today = 0x7f1102bb;
        public static final int Widget_MaterialComponents_NavigationView = 0x7f1102bc;
        public static final int Widget_MaterialComponents_PopupMenu = 0x7f1102bd;
        public static final int Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f1102be;
        public static final int Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f1102bf;
        public static final int Widget_MaterialComponents_PopupMenu_Overflow = 0x7f1102c0;
        public static final int Widget_MaterialComponents_ShapeableImageView = 0x7f1102c1;
        public static final int Widget_MaterialComponents_Slider = 0x7f1102c2;
        public static final int Widget_MaterialComponents_Snackbar = 0x7f1102c3;
        public static final int Widget_MaterialComponents_Snackbar_FullWidth = 0x7f1102c4;
        public static final int Widget_MaterialComponents_Snackbar_TextView = 0x7f1102c5;
        public static final int Widget_MaterialComponents_TabLayout = 0x7f1102c6;
        public static final int Widget_MaterialComponents_TabLayout_Colored = 0x7f1102c7;
        public static final int Widget_MaterialComponents_TabLayout_PrimarySurface = 0x7f1102c8;
        public static final int Widget_MaterialComponents_TextInputEditText_FilledBox = 0x7f1102c9;
        public static final int Widget_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f1102ca;
        public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f1102cb;
        public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f1102cc;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox = 0x7f1102cd;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense = 0x7f1102ce;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu = 0x7f1102cf;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu = 0x7f1102d0;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox = 0x7f1102d1;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense = 0x7f1102d2;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu = 0x7f1102d3;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu = 0x7f1102d4;
        public static final int Widget_MaterialComponents_TextView = 0x7f1102d5;
        public static final int Widget_MaterialComponents_Toolbar = 0x7f1102d6;
        public static final int Widget_MaterialComponents_Toolbar_Primary = 0x7f1102d7;
        public static final int Widget_MaterialComponents_Toolbar_PrimarySurface = 0x7f1102d8;
        public static final int Widget_MaterialComponents_Toolbar_Surface = 0x7f1102d9;
        public static final int Widget_MaterialComponents_Tooltip = 0x7f1102da;
        public static final int Widget_Support_CoordinatorLayout = 0x7f1102db;
    }

    public static final class xml {
        public static final int preferences_main = 0x7f130000;
        public static final int provider_paths = 0x7f130001;
        public static final int standalone_badge = 0x7f130002;
        public static final int standalone_badge_gravity_bottom_end = 0x7f130003;
        public static final int standalone_badge_gravity_bottom_start = 0x7f130004;
        public static final int standalone_badge_gravity_top_start = 0x7f130005;
        public static final int standalone_badge_offset = 0x7f130006;
        public static final int epic_paths = 0x7f130007;
    }
}
