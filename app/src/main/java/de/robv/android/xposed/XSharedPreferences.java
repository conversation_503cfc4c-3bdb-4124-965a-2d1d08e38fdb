package de.robv.android.xposed;

import android.content.SharedPreferences;
import android.os.Environment;
import android.os.StrictMode;
import android.util.Log;
import com.android.internal.util.XmlUtils;
import de.robv.android.xposed.XSharedPreferences;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.xmlpull.v1.XmlPullParserException;

@Deprecated
/* loaded from: assets/pine_xposed_api.dex */
public final class XSharedPreferences implements SharedPreferences {
    private static final String TAG = "XSharedPreferences";
    private static Loader sLoader = Loader.SYNC;
    private final File mFile;
    private long mFileSize;
    private long mLastModified;
    private boolean mLoaded;
    private Map<String, Object> mMap;

    public static void setLoader(Loader loader) {
        sLoader = loader;
    }

    public File getFile() {
        return this.mFile;
    }

    /* loaded from: assets/pine_xposed_api.dex */
    public interface Loader {
        public static final Loader SYNC = new Loader() { // from class: de.robv.android.xposed.XSharedPreferences$Loader$$ExternalSyntheticLambda0
            @Override // de.robv.android.xposed.XSharedPreferences.Loader
            public final void run(XSharedPreferences xSharedPreferences, Runnable runnable) {
                XSharedPreferences.Loader.CC.lambda$static$0(xSharedPreferences, runnable);
            }
        };
        public static final Loader ASYNC = new Loader() { // from class: de.robv.android.xposed.XSharedPreferences$Loader$$ExternalSyntheticLambda1
            @Override // de.robv.android.xposed.XSharedPreferences.Loader
            public final void run(XSharedPreferences xSharedPreferences2, Runnable runnable2) {
                new Thread("XSharedPreferences-load") { // from class: de.robv.android.xposed.XSharedPreferences.Loader.1
                    final /* synthetic */ Runnable val$action;
                    final /* synthetic */ XSharedPreferences val$pref;

                    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
                    public AnonymousClass1(String str, XSharedPreferences xSharedPreferences22, Runnable runnable22) {
                        super(str);
                        xSharedPreferences22 = xSharedPreferences22;
                        runnable22 = runnable22;
                    }

                    @Override // java.lang.Thread, java.lang.Runnable
                    public void run() {
                        synchronized (xSharedPreferences22) {
                            runnable22.run();
                        }
                    }
                }.start();
            }
        };

        void run(XSharedPreferences xSharedPreferences, Runnable runnable);

        /* renamed from: de.robv.android.xposed.XSharedPreferences$Loader$-CC */
        /* loaded from: assets/pine_xposed_api.dex */
        public final /* synthetic */ class CC {
            static {
                Loader loader = Loader.SYNC;
            }

            public static /* synthetic */ void lambda$static$0(XSharedPreferences xSharedPreferences, Runnable runnable) {
                StrictMode.ThreadPolicy allowThreadDiskReads = StrictMode.allowThreadDiskReads();
                try {
                    runnable.run();
                } finally {
                    StrictMode.setThreadPolicy(allowThreadDiskReads);
                }
            }
        }

        /* renamed from: de.robv.android.xposed.XSharedPreferences$Loader$1 */
        /* loaded from: assets/pine_xposed_api.dex */
        public class AnonymousClass1 extends Thread {
            final /* synthetic */ Runnable val$action;
            final /* synthetic */ XSharedPreferences val$pref;

            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            public AnonymousClass1(String str, XSharedPreferences xSharedPreferences22, Runnable runnable22) {
                super(str);
                xSharedPreferences22 = xSharedPreferences22;
                runnable22 = runnable22;
            }

            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                synchronized (xSharedPreferences22) {
                    runnable22.run();
                }
            }
        }
    }

    public XSharedPreferences(File file) {
        this.mLoaded = false;
        this.mFile = file;
        startLoadFromDisk();
    }

    public XSharedPreferences(String str) {
        this(str, str + "_preferences");
    }

    public XSharedPreferences(String str, String str2) {
        this.mLoaded = false;
        this.mFile = new File(Environment.getDataDirectory(), "data/" + str + "/shared_prefs/" + str2 + ".xml");
        startLoadFromDisk();
    }

    public boolean makeWorldReadable() {
        if (this.mFile.exists()) {
            return this.mFile.setReadable(true, false);
        }
        return false;
    }

    private void startLoadFromDisk() {
        synchronized (this) {
            this.mLoaded = false;
        }
        sLoader.run(this, new Runnable() { // from class: de.robv.android.xposed.XSharedPreferences$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() {
                XSharedPreferences.this.loadFromDiskLocked();
            }
        });
    }

    /* JADX WARN: Code restructure failed: missing block: B:30:0x0053, code lost:
    
        if (r3 == null) goto L161;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x0055, code lost:
    
        r3.close();
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x0046, code lost:
    
        if (r3 != null) goto L170;
     */
    /* JADX WARN: Not initialized variable reg: 3, insn: 0x004a: MOVE (r2 I:??[OBJECT, ARRAY]) = (r3 I:??[OBJECT, ARRAY]), block:B:41:0x004a */
    /* JADX WARN: Removed duplicated region for block: B:44:0x004d A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public void loadFromDiskLocked() {
        BufferedInputStream bufferedInputStream;
        BufferedInputStream bufferedInputStream2;
        Map<String, Object> map;
        if (this.mLoaded) {
            return;
        }
        long lastModified = this.mFile.lastModified();
        BufferedInputStream bufferedInputStream3 = null;
        r2 = null;
        r2 = null;
        r2 = null;
        Map<String, Object> map2 = null;
        BufferedInputStream bufferedInputStream4 = null;
        try {
            try {
                if (lastModified != this.mLastModified) {
                    bufferedInputStream = new BufferedInputStream(new FileInputStream(this.mFile), 4096);
                    try {
                        map = XmlUtils.readMapXml(bufferedInputStream);
                        bufferedInputStream4 = bufferedInputStream;
                    } catch (FileNotFoundException unused) {
                    } catch (IOException e) {
                        e = e;
                        Log.w(TAG, "getSharedPreferences", e);
                    } catch (XmlPullParserException e2) {
                        e = e2;
                        Log.w(TAG, "getSharedPreferences", e);
                    }
                } else {
                    map = this.mMap;
                }
                if (bufferedInputStream4 != null) {
                    try {
                        bufferedInputStream4.close();
                    } catch (IOException unused2) {
                    }
                }
                map2 = map;
            } catch (Throwable th) {
                th = th;
                bufferedInputStream3 = bufferedInputStream2;
                if (bufferedInputStream3 != null) {
                    try {
                        bufferedInputStream3.close();
                    } catch (IOException unused3) {
                    }
                }
                throw th;
            }
        } catch (FileNotFoundException unused4) {
            bufferedInputStream = null;
        } catch (IOException e3) {
            e = e3;
            bufferedInputStream = null;
            Log.w(TAG, "getSharedPreferences", e);
        } catch (XmlPullParserException e4) {
            e = e4;
            bufferedInputStream = null;
            Log.w(TAG, "getSharedPreferences", e);
        } catch (Throwable th2) {
            th = th2;
            if (bufferedInputStream3 != null) {
            }
            throw th;
        }
        this.mLoaded = true;
        if (map2 != null) {
            this.mMap = map2;
            this.mLastModified = lastModified;
            this.mFileSize = this.mFile.length();
        } else {
            this.mMap = new HashMap();
        }
        if (Thread.holdsLock(this)) {
            notifyAll();
        }
    }

    public synchronized void reload() {
        if (hasFileChanged()) {
            startLoadFromDisk();
        }
    }

    public synchronized boolean hasFileChanged() {
        StrictMode.ThreadPolicy allowThreadDiskReads = StrictMode.allowThreadDiskReads();
        try {
            boolean z = true;
            if (!this.mFile.exists()) {
                return true;
            }
            if (this.mFile.lastModified() == this.mLastModified) {
                if (this.mFile.length() == this.mFileSize) {
                    z = false;
                }
            }
            return z;
        } finally {
            StrictMode.setThreadPolicy(allowThreadDiskReads);
        }
    }

    private void awaitLoadedLocked() {
        while (!this.mLoaded) {
            try {
                wait();
            } catch (InterruptedException unused) {
            }
        }
    }

    @Override // android.content.SharedPreferences
    public Map<String, ?> getAll() {
        HashMap hashMap;
        synchronized (this) {
            awaitLoadedLocked();
            hashMap = new HashMap(this.mMap);
        }
        return hashMap;
    }

    @Override // android.content.SharedPreferences
    public String getString(String str, String str2) {
        synchronized (this) {
            awaitLoadedLocked();
            String str3 = (String) this.mMap.get(str);
            if (str3 != null) {
                str2 = str3;
            }
        }
        return str2;
    }

    @Override // android.content.SharedPreferences
    public Set<String> getStringSet(String str, Set<String> set) {
        synchronized (this) {
            awaitLoadedLocked();
            Set<String> set2 = (Set) this.mMap.get(str);
            if (set2 != null) {
                set = set2;
            }
        }
        return set;
    }

    @Override // android.content.SharedPreferences
    public int getInt(String str, int i) {
        synchronized (this) {
            awaitLoadedLocked();
            Integer num = (Integer) this.mMap.get(str);
            if (num != null) {
                i = num.intValue();
            }
        }
        return i;
    }

    @Override // android.content.SharedPreferences
    public long getLong(String str, long j) {
        synchronized (this) {
            awaitLoadedLocked();
            Long l = (Long) this.mMap.get(str);
            if (l != null) {
                j = l.longValue();
            }
        }
        return j;
    }

    @Override // android.content.SharedPreferences
    public float getFloat(String str, float f) {
        synchronized (this) {
            awaitLoadedLocked();
            Float f2 = (Float) this.mMap.get(str);
            if (f2 != null) {
                f = f2.floatValue();
            }
        }
        return f;
    }

    @Override // android.content.SharedPreferences
    public boolean getBoolean(String str, boolean z) {
        synchronized (this) {
            awaitLoadedLocked();
            Boolean bool = (Boolean) this.mMap.get(str);
            if (bool != null) {
                z = bool.booleanValue();
            }
        }
        return z;
    }

    @Override // android.content.SharedPreferences
    public boolean contains(String str) {
        boolean containsKey;
        synchronized (this) {
            awaitLoadedLocked();
            containsKey = this.mMap.containsKey(str);
        }
        return containsKey;
    }

    @Override // android.content.SharedPreferences
    @Deprecated
    public SharedPreferences.Editor edit() {
        throw new UnsupportedOperationException("read-only implementation");
    }

    @Override // android.content.SharedPreferences
    @Deprecated
    public void registerOnSharedPreferenceChangeListener(SharedPreferences.OnSharedPreferenceChangeListener onSharedPreferenceChangeListener) {
        throw new UnsupportedOperationException("listeners are not supported in this implementation");
    }

    @Override // android.content.SharedPreferences
    @Deprecated
    public void unregisterOnSharedPreferenceChangeListener(SharedPreferences.OnSharedPreferenceChangeListener onSharedPreferenceChangeListener) {
        throw new UnsupportedOperationException("listeners are not supported in this implementation");
    }
}
