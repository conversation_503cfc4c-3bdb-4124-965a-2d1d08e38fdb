#!/bin/bash

echo "=== 位置测试工具项目构建检查 ==="
echo ""

# 检查项目结构
echo "1. 检查项目结构..."
if [ -f "app/build.gradle" ]; then
    echo "✅ app/build.gradle 存在"
else
    echo "❌ app/build.gradle 不存在"
fi

if [ -f "settings.gradle" ]; then
    echo "✅ settings.gradle 存在"
else
    echo "❌ settings.gradle 不存在"
fi

if [ -f "local.properties" ]; then
    echo "✅ local.properties 存在"
else
    echo "❌ local.properties 不存在"
fi

echo ""

# 检查源代码文件
echo "2. 检查源代码文件..."
SOURCE_FILES=(
    "app/src/main/java/com/mymapapp/MainActivity.kt"
    "app/src/main/java/com/mymapapp/HistoryActivity.kt"
    "app/src/main/java/com/mymapapp/LocationTestData.kt"
    "app/src/main/java/com/mymapapp/LocationHistoryManager.kt"
    "app/src/main/AndroidManifest.xml"
)

for file in "${SOURCE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file"
    fi
done

echo ""

# 检查布局文件
echo "3. 检查布局文件..."
LAYOUT_FILES=(
    "app/src/main/res/layout/activity_main.xml"
    "app/src/main/res/layout/activity_history.xml"
    "app/src/main/res/layout/dialog_coordinate_input.xml"
    "app/src/main/res/layout/item_history.xml"
)

for file in "${LAYOUT_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file"
    fi
done

echo ""

# 检查资源文件
echo "4. 检查资源文件..."
RESOURCE_FILES=(
    "app/src/main/res/values/strings.xml"
    "app/src/main/res/values/colors.xml"
    "app/src/main/res/values/themes.xml"
)

for file in "${RESOURCE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file"
    fi
done

echo ""

# 检查API密钥配置
echo "5. 检查API密钥配置..."
if grep -q "YOUR_BAIDU_AK_HERE" local.properties; then
    echo "⚠️  请在 local.properties 中配置您的百度地图AK密钥"
    echo "   将 YOUR_BAIDU_AK_HERE 替换为实际的AK密钥"
    echo "   申请地址: https://lbsyun.baidu.com/"
else
    echo "✅ 百度地图AK密钥已配置"
fi

if grep -q "YOUR_GOOGLE_API_KEY_HERE" local.properties; then
    echo "ℹ️  Google Maps API密钥未配置（可选）"
else
    echo "✅ Google Maps API密钥已配置（备用）"
fi

echo ""
echo "=== 检查完成 ==="
echo ""
echo "📋 下一步操作："
echo "1. 申请百度地图AK密钥: https://lbsyun.baidu.com/"
echo "2. 在 local.properties 中配置百度地图AK"
echo "3. 在 Android Studio 中打开此项目"
echo "4. 等待 Gradle 同步完成"
echo "5. 连接 Android 设备或启动模拟器"
echo "6. 点击 Run 按钮构建并安装应用"
echo ""
echo "📖 详细配置指南请查看: BAIDU_MAP_SETUP.md"
