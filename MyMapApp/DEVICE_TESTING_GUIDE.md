# 设备测试指南

## 📱 真机测试步骤

### 1. 设备准备
1. **启用开发者模式**：
   - 设置 → 关于手机 → 连续点击"版本号"7次
   - 返回设置，找到"开发者选项"

2. **启用USB调试**：
   - 开发者选项 → USB调试 → 开启
   - 连接电脑时选择"允许USB调试"

3. **安装Google Play服务**（如果没有）：
   - 确保设备已安装Google Play服务
   - 这是Google Maps正常工作的必要条件

### 2. 权限设置
应用首次启动时会请求以下权限：
- **位置权限**：允许应用获取设备位置
- **网络权限**：用于加载地图数据

**重要**：请务必授予位置权限，否则应用无法正常工作。

### 3. 功能测试清单

#### 3.1 基础功能测试
- [ ] 应用正常启动
- [ ] 地图正常加载和显示
- [ ] 可以缩放和平移地图
- [ ] 位置权限已正确授予

#### 3.2 位置测试功能
- [ ] 点击地图可以选择测试位置
- [ ] 测试位置标记正确显示
- [ ] 位置测试开关可以正常切换
- [ ] 当前位置信息正确显示在控制面板

#### 3.3 坐标输入功能
- [ ] 点击"输入坐标"按钮打开对话框
- [ ] 可以输入经纬度坐标
- [ ] 坐标范围验证正常工作
- [ ] 支持WGS84和BD09坐标系选择
- [ ] 输入坐标后地图正确跳转

#### 3.4 历史记录功能
- [ ] 测试位置自动保存到历史记录
- [ ] 历史记录列表正常显示
- [ ] 搜索功能正常工作
- [ ] 可以重新使用历史位置
- [ ] 可以删除单个历史记录
- [ ] 可以清空所有历史记录

#### 3.5 移动模式功能
- [ ] 移动模式选择对话框正常显示
- [ ] 可以选择不同的移动模式
- [ ] 模式选择后正确反馈

#### 3.6 地图功能
- [ ] 地图类型切换正常工作
- [ ] "我的位置"按钮正常工作
- [ ] 地图标记点击显示详情

## 🧪 测试用例

### 测试用例1：基本位置选择
1. 启动应用
2. 点击地图上任意位置
3. 验证：标记出现，控制面板显示坐标
4. 开启位置测试开关
5. 验证：显示"位置测试已启用"提示

### 测试用例2：坐标输入测试
1. 点击"输入坐标"按钮
2. 输入有效坐标：经度116.404，纬度39.915
3. 选择WGS84坐标系
4. 点击确定
5. 验证：地图跳转到北京天安门附近，标记正确显示

### 测试用例3：历史记录测试
1. 选择几个不同的测试位置
2. 点击"历史记录"按钮
3. 验证：历史记录列表显示之前选择的位置
4. 点击某个历史记录的"使用"按钮
5. 验证：地图跳转到该位置

### 测试用例4：错误处理测试
1. 输入无效坐标（如经度200）
2. 验证：显示错误提示"经度超出范围"
3. 输入空坐标
4. 验证：显示"输入不能为空"提示

## 🔍 性能测试

### 内存使用
- 长时间使用应用，观察内存使用情况
- 频繁切换位置，检查是否有内存泄漏

### 响应速度
- 地图加载速度
- 位置选择响应速度
- 历史记录加载速度

### 电池消耗
- 长时间使用应用的电池消耗情况
- 后台运行时的电池消耗

## 🐛 常见问题及解决方案

### 问题1：地图不显示或显示空白
**可能原因**：
- API密钥未配置或无效
- 网络连接问题
- Google Play服务未安装

**解决方案**：
- 检查API密钥配置
- 确认网络连接正常
- 安装或更新Google Play服务

### 问题2：无法获取位置权限
**可能原因**：
- 用户拒绝了权限请求
- 设备GPS未开启

**解决方案**：
- 在设置中手动授予位置权限
- 开启设备GPS功能

### 问题3：应用崩溃
**可能原因**：
- 设备兼容性问题
- 内存不足
- 代码异常

**解决方案**：
- 查看崩溃日志
- 重启应用
- 清除应用数据

### 问题4：历史记录不保存
**可能原因**：
- 存储权限问题
- 应用数据被清除

**解决方案**：
- 检查应用存储权限
- 重新选择位置进行测试

## 📊 测试报告模板

### 测试环境
- 设备型号：
- Android版本：
- 应用版本：
- 测试日期：

### 测试结果
- 基础功能：✅/❌
- 位置测试：✅/❌
- 坐标输入：✅/❌
- 历史记录：✅/❌
- 移动模式：✅/❌
- 地图功能：✅/❌

### 发现的问题
1. 问题描述：
   - 重现步骤：
   - 预期结果：
   - 实际结果：

### 建议改进
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议

---

**测试完成后，您的位置测试工具就可以正式投入使用了！** 🎉
