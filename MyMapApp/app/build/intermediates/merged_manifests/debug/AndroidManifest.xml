<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.mymapapp"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 外部存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- 系统悬浮窗权限（摇杆功能需要） -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- 模拟位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION" />

    <permission
        android:name="com.mymapapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.mymapapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyMapApp" >

        <!-- 百度地图API Key -->
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="vDaQWEipFpei1hSbnJvAu7tk8OoWlAje" />

        <!-- 百度地图服务 -->
        <service
            android:name="com.baidu.location.f"
            android:enabled="true"
            android:process=":remote" />

        <!-- 百度地图版主Activity -->
        <activity
            android:name="com.mymapapp.BaiduMainActivity"
            android:exported="true"
            android:theme="@style/Theme.MyMapApp.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Google Maps版主Activity（备用） -->
        <activity
            android:name="com.mymapapp.MainActivity"
            android:exported="false"
            android:theme="@style/Theme.MyMapApp" />
        <activity
            android:name="com.mymapapp.SettingsActivity"
            android:exported="false"
            android:label="@string/settings"
            android:parentActivityName="com.mymapapp.MainActivity" />
        <activity
            android:name="com.mymapapp.HistoryActivity"
            android:exported="false"
            android:label="@string/history"
            android:parentActivityName="com.mymapapp.MainActivity" />
        <activity
            android:name="pub.devrel.easypermissions.AppSettingsDialogHolderActivity"
            android:exported="false"
            android:label=""
            android:theme="@style/EasyPermissions.Transparent" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.mymapapp.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>