<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".BaiduMainActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/Theme.MyMapApp.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/Theme.MyMapApp.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 位置测试控制面板 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/controlCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 标题 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="位置测试工具 (百度地图版)"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- 当前位置显示 -->
                <LinearLayout
                    android:id="@+id/currentLocationLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@drawable/bg_location_display"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:src="@android:drawable/ic_menu_mylocation"
                        android:layout_marginEnd="8dp"
                        android:contentDescription="当前位置" />

                    <TextView
                        android:id="@+id/tvCurrentLocation"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="点击地图选择测试位置"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchLocationTest"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical" />

                </LinearLayout>

                <!-- 操作按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnInputCoordinates"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="输入坐标"
                        android:icon="@android:drawable/ic_menu_edit"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnHistory"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="历史记录"
                        android:icon="@android:drawable/ic_menu_recent_history"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 百度地图容器 -->
        <com.baidu.mapapi.map.MapView
            android:id="@+id/mapView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/controlCard"
            android:clickable="true" />

        <!-- 我的位置按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabMyLocation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="16dp"
            android:contentDescription="@string/my_location"
            android:src="@android:drawable/ic_menu_mylocation"
            app:tint="@color/white" />

        <!-- 移动模式选择 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabMovementMode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@id/fabMapType"
            android:layout_alignParentEnd="true"
            android:layout_margin="16dp"
            android:contentDescription="移动模式"
            android:src="@android:drawable/ic_menu_directions"
            app:fabSize="mini"
            app:tint="@color/white" />

        <!-- 地图类型切换按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabMapType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@id/fabMyLocation"
            android:layout_alignParentEnd="true"
            android:layout_margin="16dp"
            android:contentDescription="@string/map_type"
            android:src="@android:drawable/ic_menu_mapmode"
            app:fabSize="mini"
            app:tint="@color/white" />

    </RelativeLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
