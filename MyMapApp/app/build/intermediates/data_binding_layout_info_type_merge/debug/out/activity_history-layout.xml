<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_history" modulePackage="com.mymapapp" filePath="app/src/main/res/layout/activity_history.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_history_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="120" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="19" endOffset="30"/></Target><Target id="@+id/etSearch" view="EditText"><Expressions/><location startLine="51" startOffset="16" endLine="59" endOffset="45"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="72" startOffset="12" endLine="77" endOffset="46"/></Target><Target id="@+id/emptyLayout" view="LinearLayout"><Expressions/><location startLine="80" startOffset="12" endLine="103" endOffset="26"/></Target><Target id="@+id/fabClear" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="110" startOffset="4" endLine="118" endOffset="33"/></Target></Targets></Layout>