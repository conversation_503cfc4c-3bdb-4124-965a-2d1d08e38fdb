<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res"><file name="bg_location_display" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/drawable/bg_location_display.xml" qualifiers="" type="drawable"/><file name="activity_settings" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/layout/activity_settings.xml" qualifiers="" type="layout"/><file name="dialog_coordinate_input" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/layout/dialog_coordinate_input.xml" qualifiers="" type="layout"/><file name="dialog_movement_mode" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/layout/dialog_movement_mode.xml" qualifiers="" type="layout"/><file name="activity_history" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/layout/activity_history.xml" qualifiers="" type="layout"/><file name="activity_baidu_main" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/layout/activity_baidu_main.xml" qualifiers="" type="layout"/><file name="item_history" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/layout/item_history.xml" qualifiers="" type="layout"/><file path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/values/colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_blue">#FF2196F3</color><color name="primary_blue_dark">#FF1976D2</color><color name="accent_orange">#FFFF9800</color><color name="background_light">#FFF5F5F5</color><color name="text_primary">#FF212121</color><color name="text_secondary">#FF757575</color></file><file path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/values/themes.xml" qualifiers=""><style name="Theme.MyMapApp" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.MyMapApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.MyMapApp.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.MyMapApp.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/></file><file path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">位置测试工具</string><string name="settings">设置</string><string name="location_permission_required">需要位置权限才能使用地图功能</string><string name="grant_permission">授予权限</string><string name="permission_denied">权限被拒绝</string><string name="my_location">我的位置</string><string name="search_location">搜索位置</string><string name="navigation">导航</string><string name="map_type">地图类型</string><string name="normal_map">普通地图</string><string name="satellite_map">卫星地图</string><string name="terrain_map">地形地图</string><string name="hybrid_map">混合地图</string><string name="zoom_in">放大</string><string name="zoom_out">缩小</string><string name="loading">加载中...</string><string name="error_loading_map">地图加载失败</string><string name="location_not_found">未找到位置</string><string name="location_test_tool">位置测试工具</string><string name="current_location">当前位置</string><string name="click_map_select">点击地图选择测试位置</string><string name="input_coordinates">输入坐标</string><string name="history_records">历史记录</string><string name="location_test_enabled">位置测试已启用</string><string name="location_test_disabled">位置测试已禁用</string><string name="coordinate_input">坐标输入</string><string name="longitude">经度</string><string name="latitude">纬度</string><string name="longitude_range">范围: -180.0 ~ 180.0</string><string name="latitude_range">范围: -90.0 ~ 90.0</string><string name="coordinate_system">坐标系类型</string><string name="wgs84_gps">WGS84 (GPS)</string><string name="bd09_baidu">BD09 (百度)</string><string name="confirm">确定</string><string name="cancel">取消</string><string name="movement_mode">移动模式</string><string name="walking">步行</string><string name="running">跑步</string><string name="cycling">骑行</string><string name="driving">驾车</string><string name="history">历史记录</string><string name="no_history">暂无历史记录</string><string name="search_history">搜索历史记录</string><string name="clear_history">清空历史记录</string><string name="delete_history_confirm">确定要清空所有历史记录吗？</string><string name="use_location">使用此位置</string><string name="delete_item">删除</string><string name="error_invalid_coordinates">坐标格式不正确</string><string name="error_longitude_range">经度超出范围 (-180.0 ~ 180.0)</string><string name="error_latitude_range">纬度超出范围 (-90.0 ~ 90.0)</string><string name="error_empty_input">输入不能为空</string><string-array name="map_type_entries">
        <item>普通地图</item>
        <item>卫星地图</item>
        <item>地形地图</item>
        <item>混合地图</item>
    </string-array><string-array name="map_type_values">
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
    </string-array></file><file name="root_preferences" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/xml/root_preferences.xml" qualifiers="" type="xml"/><file name="backup_rules" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/xml/backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/xml/data_extraction_rules.xml" qualifiers="" type="xml"/><file name="main_menu" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/menu/main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/破解/MyMapApp/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/破解/MyMapApp/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/破解/MyMapApp/app/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>