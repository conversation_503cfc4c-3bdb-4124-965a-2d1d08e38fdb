<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_movement_mode" modulePackage="com.mymapapp" filePath="app/src/main/res/layout/dialog_movement_mode.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_movement_mode_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="52" endOffset="14"/></Target><Target id="@+id/rgMovementMode" view="RadioGroup"><Expressions/><location startLine="16" startOffset="4" endLine="50" endOffset="16"/></Target><Target id="@+id/rbWalking" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="21" startOffset="8" endLine="27" endOffset="36"/></Target><Target id="@+id/rbRunning" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="29" startOffset="8" endLine="34" endOffset="36"/></Target><Target id="@+id/rbCycling" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="36" startOffset="8" endLine="41" endOffset="36"/></Target><Target id="@+id/rbDriving" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="43" startOffset="8" endLine="48" endOffset="36"/></Target></Targets></Layout>