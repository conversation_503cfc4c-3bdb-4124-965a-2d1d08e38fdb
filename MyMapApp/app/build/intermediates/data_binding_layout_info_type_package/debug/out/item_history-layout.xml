<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_history" modulePackage="com.mymapapp" filePath="app/src/main/res/layout/item_history.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_history_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="96" endOffset="51"/></Target><Target id="@+id/tvLocationName" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="44" endOffset="41"/></Target><Target id="@+id/tvCoordinates" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="53" endOffset="48"/></Target><Target id="@+id/tvTimestamp" view="TextView"><Expressions/><location startLine="55" startOffset="12" endLine="62" endOffset="48"/></Target><Target id="@+id/btnUse" view="ImageButton"><Expressions/><location startLine="73" startOffset="12" endLine="81" endOffset="49"/></Target><Target id="@+id/btnDelete" view="ImageButton"><Expressions/><location startLine="83" startOffset="12" endLine="90" endOffset="50"/></Target></Targets></Layout>