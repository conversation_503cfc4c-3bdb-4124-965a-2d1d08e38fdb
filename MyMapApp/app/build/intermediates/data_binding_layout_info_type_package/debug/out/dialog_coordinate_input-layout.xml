<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_coordinate_input" modulePackage="com.mymapapp" filePath="app/src/main/res/layout/dialog_coordinate_input.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_coordinate_input_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="110" endOffset="14"/></Target><Target id="@+id/etLongitude" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="26" startOffset="8" endLine="31" endOffset="34"/></Target><Target id="@+id/etLatitude" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="8" endLine="49" endOffset="34"/></Target><Target id="@+id/rgCoordinateSystem" view="RadioGroup"><Expressions/><location startLine="63" startOffset="4" endLine="85" endOffset="16"/></Target><Target id="@+id/rbWGS84" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="70" startOffset="8" endLine="76" endOffset="36"/></Target><Target id="@+id/rbBD09" view="com.google.android.material.radiobutton.MaterialRadioButton"><Expressions/><location startLine="78" startOffset="8" endLine="83" endOffset="38"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="94" startOffset="8" endLine="100" endOffset="63"/></Target><Target id="@+id/btnConfirm" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="102" startOffset="8" endLine="106" endOffset="31"/></Target></Targets></Layout>