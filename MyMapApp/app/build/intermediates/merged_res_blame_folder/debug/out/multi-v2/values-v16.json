{"logs": [{"outputFile": "com.mymapapp-mergeDebugResources-40:/values-v16/values-v16.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.14.3/transforms/6964c8352e66ce727bc26867b4e02313/transformed/core-1.12.0/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.3/transforms/42a789498413e29d3084f0bb837cd6b4/transformed/appcompat-1.6.1/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}]}