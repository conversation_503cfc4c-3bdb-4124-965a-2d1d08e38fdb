{"logs": [{"outputFile": "com.mymapapp-mergeDebugResources-40:/values-hr/values-hr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.14.3/transforms/82b626f1ae952bf18fd9578579a1ff08/transformed/preference-1.2.1/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,494,663,750", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "171,258,341,489,658,745,828"}, "to": {"startLines": "49,51,110,112,115,116,117", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4548,4687,9454,9617,9951,10120,10207", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "4614,4769,9532,9760,10115,10202,10285"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.3/transforms/42a789498413e29d3084f0bb837cd6b4/transformed/appcompat-1.6.1/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,9765", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,9845"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.3/transforms/6964c8352e66ce727bc26867b4e02313/transformed/core-1.12.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3512,3610,3717,3814,3913,4017,4121,9850", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3605,3712,3809,3908,4012,4116,4233,9946"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.3/transforms/748fc6ac1e01a97dae8cab077550d281/transformed/material-1.10.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2945,3036,3114,3170,3225,3291,3365,3443,3531,3613,3685,3762,3842,3916,4023,4116,4189,4281,4377,4451,4527,4623,4675,4757,4824,4911,4998,5060,5124,5187,5257,5363,5479,5576,5690,5750,5809", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2940,3031,3109,3165,3220,3286,3360,3438,3526,3608,3680,3757,3837,3911,4018,4111,4184,4276,4372,4446,4522,4618,4670,4752,4819,4906,4993,5055,5119,5182,5252,5358,5474,5571,5685,5745,5804,5884"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3159,3237,3322,3419,4238,4334,4464,4619,4774,4870,4938,5001,5109,5169,5235,5291,5362,5422,5476,5602,5659,5721,5775,5850,5984,6069,6150,6287,6371,6457,6590,6681,6759,6815,6870,6936,7010,7088,7176,7258,7330,7407,7487,7561,7668,7761,7834,7926,8022,8096,8172,8268,8320,8402,8469,8556,8643,8705,8769,8832,8902,9008,9124,9221,9335,9395,9537", "endLines": "6,34,35,36,37,38,46,47,48,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,111", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "365,3154,3232,3317,3414,3507,4329,4459,4543,4682,4865,4933,4996,5104,5164,5230,5286,5357,5417,5471,5597,5654,5716,5770,5845,5979,6064,6145,6282,6366,6452,6585,6676,6754,6810,6865,6931,7005,7083,7171,7253,7325,7402,7482,7556,7663,7756,7829,7921,8017,8091,8167,8263,8315,8397,8464,8551,8638,8700,8764,8827,8897,9003,9119,9216,9330,9390,9449,9612"}}]}]}