[{"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/layout_activity_settings.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/layout/activity_settings.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/mipmap-hdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/mipmap-hdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/layout_activity_baidu_main.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/layout/activity_baidu_main.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/xml_backup_rules.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/xml/backup_rules.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/drawable_bg_location_display.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/drawable/bg_location_display.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/layout_item_history.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/layout/item_history.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/xml_root_preferences.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/xml/root_preferences.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/layout_dialog_coordinate_input.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/layout/dialog_coordinate_input.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/layout_dialog_movement_mode.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/layout/dialog_movement_mode.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/menu_main_menu.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/menu/main_menu.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/layout_activity_history.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/layout/activity_history.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/xml_data_extraction_rules.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/xml/data_extraction_rules.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-merged_res-42:/layout_activity_main.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.14.3/com.mymapapp-main-44:/layout/activity_main.xml"}]