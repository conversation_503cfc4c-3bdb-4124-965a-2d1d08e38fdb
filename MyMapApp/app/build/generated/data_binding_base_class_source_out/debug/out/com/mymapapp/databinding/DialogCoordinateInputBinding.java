// Generated by view binder compiler. Do not edit!
package com.mymapapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.radiobutton.MaterialRadioButton;
import com.google.android.material.textfield.TextInputEditText;
import com.mymapapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCoordinateInputBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnConfirm;

  @NonNull
  public final TextInputEditText etLatitude;

  @NonNull
  public final TextInputEditText etLongitude;

  @NonNull
  public final MaterialRadioButton rbBD09;

  @NonNull
  public final MaterialRadioButton rbWGS84;

  @NonNull
  public final RadioGroup rgCoordinateSystem;

  private DialogCoordinateInputBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnConfirm,
      @NonNull TextInputEditText etLatitude, @NonNull TextInputEditText etLongitude,
      @NonNull MaterialRadioButton rbBD09, @NonNull MaterialRadioButton rbWGS84,
      @NonNull RadioGroup rgCoordinateSystem) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.etLatitude = etLatitude;
    this.etLongitude = etLongitude;
    this.rbBD09 = rbBD09;
    this.rbWGS84 = rbWGS84;
    this.rgCoordinateSystem = rgCoordinateSystem;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCoordinateInputBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCoordinateInputBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_coordinate_input, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCoordinateInputBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      MaterialButton btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.etLatitude;
      TextInputEditText etLatitude = ViewBindings.findChildViewById(rootView, id);
      if (etLatitude == null) {
        break missingId;
      }

      id = R.id.etLongitude;
      TextInputEditText etLongitude = ViewBindings.findChildViewById(rootView, id);
      if (etLongitude == null) {
        break missingId;
      }

      id = R.id.rbBD09;
      MaterialRadioButton rbBD09 = ViewBindings.findChildViewById(rootView, id);
      if (rbBD09 == null) {
        break missingId;
      }

      id = R.id.rbWGS84;
      MaterialRadioButton rbWGS84 = ViewBindings.findChildViewById(rootView, id);
      if (rbWGS84 == null) {
        break missingId;
      }

      id = R.id.rgCoordinateSystem;
      RadioGroup rgCoordinateSystem = ViewBindings.findChildViewById(rootView, id);
      if (rgCoordinateSystem == null) {
        break missingId;
      }

      return new DialogCoordinateInputBinding((LinearLayout) rootView, btnCancel, btnConfirm,
          etLatitude, etLongitude, rbBD09, rbWGS84, rgCoordinateSystem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
