// Generated by view binder compiler. Do not edit!
package com.mymapapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.radiobutton.MaterialRadioButton;
import com.mymapapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogMovementModeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialRadioButton rbCycling;

  @NonNull
  public final MaterialRadioButton rbDriving;

  @NonNull
  public final MaterialRadioButton rbRunning;

  @NonNull
  public final MaterialRadioButton rbWalking;

  @NonNull
  public final RadioGroup rgMovementMode;

  private DialogMovementModeBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialRadioButton rbCycling, @NonNull MaterialRadioButton rbDriving,
      @NonNull MaterialRadioButton rbRunning, @NonNull MaterialRadioButton rbWalking,
      @NonNull RadioGroup rgMovementMode) {
    this.rootView = rootView;
    this.rbCycling = rbCycling;
    this.rbDriving = rbDriving;
    this.rbRunning = rbRunning;
    this.rbWalking = rbWalking;
    this.rgMovementMode = rgMovementMode;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogMovementModeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogMovementModeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_movement_mode, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogMovementModeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rbCycling;
      MaterialRadioButton rbCycling = ViewBindings.findChildViewById(rootView, id);
      if (rbCycling == null) {
        break missingId;
      }

      id = R.id.rbDriving;
      MaterialRadioButton rbDriving = ViewBindings.findChildViewById(rootView, id);
      if (rbDriving == null) {
        break missingId;
      }

      id = R.id.rbRunning;
      MaterialRadioButton rbRunning = ViewBindings.findChildViewById(rootView, id);
      if (rbRunning == null) {
        break missingId;
      }

      id = R.id.rbWalking;
      MaterialRadioButton rbWalking = ViewBindings.findChildViewById(rootView, id);
      if (rbWalking == null) {
        break missingId;
      }

      id = R.id.rgMovementMode;
      RadioGroup rgMovementMode = ViewBindings.findChildViewById(rootView, id);
      if (rgMovementMode == null) {
        break missingId;
      }

      return new DialogMovementModeBinding((LinearLayout) rootView, rbCycling, rbDriving, rbRunning,
          rbWalking, rgMovementMode);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
