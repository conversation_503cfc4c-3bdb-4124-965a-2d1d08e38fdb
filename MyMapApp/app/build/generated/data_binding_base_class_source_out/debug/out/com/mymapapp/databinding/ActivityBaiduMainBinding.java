// Generated by view binder compiler. Do not edit!
package com.mymapapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.baidu.mapapi.map.MapView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.mymapapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBaiduMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnHistory;

  @NonNull
  public final MaterialButton btnInputCoordinates;

  @NonNull
  public final MaterialCardView controlCard;

  @NonNull
  public final LinearLayout currentLocationLayout;

  @NonNull
  public final FloatingActionButton fabMapType;

  @NonNull
  public final FloatingActionButton fabMovementMode;

  @NonNull
  public final FloatingActionButton fabMyLocation;

  @NonNull
  public final MapView mapView;

  @NonNull
  public final SwitchMaterial switchLocationTest;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCurrentLocation;

  private ActivityBaiduMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnHistory, @NonNull MaterialButton btnInputCoordinates,
      @NonNull MaterialCardView controlCard, @NonNull LinearLayout currentLocationLayout,
      @NonNull FloatingActionButton fabMapType, @NonNull FloatingActionButton fabMovementMode,
      @NonNull FloatingActionButton fabMyLocation, @NonNull MapView mapView,
      @NonNull SwitchMaterial switchLocationTest, @NonNull Toolbar toolbar,
      @NonNull TextView tvCurrentLocation) {
    this.rootView = rootView;
    this.btnHistory = btnHistory;
    this.btnInputCoordinates = btnInputCoordinates;
    this.controlCard = controlCard;
    this.currentLocationLayout = currentLocationLayout;
    this.fabMapType = fabMapType;
    this.fabMovementMode = fabMovementMode;
    this.fabMyLocation = fabMyLocation;
    this.mapView = mapView;
    this.switchLocationTest = switchLocationTest;
    this.toolbar = toolbar;
    this.tvCurrentLocation = tvCurrentLocation;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBaiduMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBaiduMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_baidu_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBaiduMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnHistory;
      MaterialButton btnHistory = ViewBindings.findChildViewById(rootView, id);
      if (btnHistory == null) {
        break missingId;
      }

      id = R.id.btnInputCoordinates;
      MaterialButton btnInputCoordinates = ViewBindings.findChildViewById(rootView, id);
      if (btnInputCoordinates == null) {
        break missingId;
      }

      id = R.id.controlCard;
      MaterialCardView controlCard = ViewBindings.findChildViewById(rootView, id);
      if (controlCard == null) {
        break missingId;
      }

      id = R.id.currentLocationLayout;
      LinearLayout currentLocationLayout = ViewBindings.findChildViewById(rootView, id);
      if (currentLocationLayout == null) {
        break missingId;
      }

      id = R.id.fabMapType;
      FloatingActionButton fabMapType = ViewBindings.findChildViewById(rootView, id);
      if (fabMapType == null) {
        break missingId;
      }

      id = R.id.fabMovementMode;
      FloatingActionButton fabMovementMode = ViewBindings.findChildViewById(rootView, id);
      if (fabMovementMode == null) {
        break missingId;
      }

      id = R.id.fabMyLocation;
      FloatingActionButton fabMyLocation = ViewBindings.findChildViewById(rootView, id);
      if (fabMyLocation == null) {
        break missingId;
      }

      id = R.id.mapView;
      MapView mapView = ViewBindings.findChildViewById(rootView, id);
      if (mapView == null) {
        break missingId;
      }

      id = R.id.switchLocationTest;
      SwitchMaterial switchLocationTest = ViewBindings.findChildViewById(rootView, id);
      if (switchLocationTest == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCurrentLocation;
      TextView tvCurrentLocation = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentLocation == null) {
        break missingId;
      }

      return new ActivityBaiduMainBinding((CoordinatorLayout) rootView, btnHistory,
          btnInputCoordinates, controlCard, currentLocationLayout, fabMapType, fabMovementMode,
          fabMyLocation, mapView, switchLocationTest, toolbar, tvCurrentLocation);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
