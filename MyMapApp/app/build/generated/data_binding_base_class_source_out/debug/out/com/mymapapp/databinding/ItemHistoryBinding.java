// Generated by view binder compiler. Do not edit!
package com.mymapapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.mymapapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemHistoryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnDelete;

  @NonNull
  public final ImageButton btnUse;

  @NonNull
  public final TextView tvCoordinates;

  @NonNull
  public final TextView tvLocationName;

  @NonNull
  public final TextView tvTimestamp;

  private ItemHistoryBinding(@NonNull MaterialCardView rootView, @NonNull ImageButton btnDelete,
      @NonNull ImageButton btnUse, @NonNull TextView tvCoordinates,
      @NonNull TextView tvLocationName, @NonNull TextView tvTimestamp) {
    this.rootView = rootView;
    this.btnDelete = btnDelete;
    this.btnUse = btnUse;
    this.tvCoordinates = tvCoordinates;
    this.tvLocationName = tvLocationName;
    this.tvTimestamp = tvTimestamp;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnDelete;
      ImageButton btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btnUse;
      ImageButton btnUse = ViewBindings.findChildViewById(rootView, id);
      if (btnUse == null) {
        break missingId;
      }

      id = R.id.tvCoordinates;
      TextView tvCoordinates = ViewBindings.findChildViewById(rootView, id);
      if (tvCoordinates == null) {
        break missingId;
      }

      id = R.id.tvLocationName;
      TextView tvLocationName = ViewBindings.findChildViewById(rootView, id);
      if (tvLocationName == null) {
        break missingId;
      }

      id = R.id.tvTimestamp;
      TextView tvTimestamp = ViewBindings.findChildViewById(rootView, id);
      if (tvTimestamp == null) {
        break missingId;
      }

      return new ItemHistoryBinding((MaterialCardView) rootView, btnDelete, btnUse, tvCoordinates,
          tvLocationName, tvTimestamp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
