<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 位置图标 -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp"
            android:src="@android:drawable/ic_menu_mylocation"
            android:contentDescription="位置"
            app:tint="@color/primary_blue" />

        <!-- 位置信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvLocationName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试位置"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tvCoordinates"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="116.404, 39.915"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp" />

            <TextView
                android:id="@+id/tvTimestamp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2024-07-29 10:30"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center_vertical">

            <ImageButton
                android:id="@+id/btnUse"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_menu_send"
                android:contentDescription="使用此位置"
                app:tint="@color/accent_orange" />

            <ImageButton
                android:id="@+id/btnDelete"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_menu_delete"
                android:contentDescription="删除"
                app:tint="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
